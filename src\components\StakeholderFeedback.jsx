import React, { useState } from 'react'

const StakeholderFeedback = ({ data, onChange }) => {
  const [feedbackData, setFeedbackData] = useState({
    stakeholders: [
      {
        id: 'STK001',
        name: '<PERSON>',
        role: 'CEO',
        category: 'Executive Sponsor',
        influence: 'High',
        interest: 'High',
        sentiment: 'Positive',
        lastEngagement: '2024-02-10',
        feedbackScore: 8.5,
        keyFeedback: 'Strong support for digital transformation initiative. Concerned about timeline.'
      },
      {
        id: 'STK002',
        name: '<PERSON>',
        role: 'CTO',
        category: 'Technical Lead',
        influence: 'High',
        interest: 'Very High',
        sentiment: 'Positive',
        lastEngagement: '2024-02-12',
        feedbackScore: 9.2,
        keyFeedback: 'Excellent technical approach. Recommends phased implementation.'
      },
      {
        id: 'STK003',
        name: '<PERSON>',
        role: 'Head of Sales',
        category: 'Business User',
        influence: 'Medium',
        interest: 'High',
        sentiment: 'Neutral',
        lastEngagement: '2024-02-08',
        feedbackScore: 6.8,
        keyFeedback: 'Needs more clarity on customer impact and sales process changes.'
      },
      {
        id: 'STK004',
        name: '<PERSON>',
        role: 'Customer Success Manager',
        category: 'End User',
        influence: 'Medium',
        interest: 'Very High',
        sentiment: 'Positive',
        lastEngagement: '2024-02-11',
        feedbackScore: 8.1,
        keyFeedback: 'Excited about customer portal improvements. Requests training plan.'
      }
    ],
    marketIntelligence: {
      marketTrends: [
        { trend: 'Digital Customer Experience', impact: 'High', confidence: 85, source: 'Gartner Research' },
        { trend: 'AI-Powered Analytics', impact: 'Medium', confidence: 78, source: 'McKinsey Report' },
        { trend: 'Mobile-First Strategy', impact: 'High', confidence: 92, source: 'Industry Analysis' },
        { trend: 'Data Privacy Regulations', impact: 'Medium', confidence: 88, source: 'Legal Assessment' }
      ],
      competitorAnalysis: [
        { competitor: 'Company A', strength: 'Technology', weakness: 'Customer Service', marketShare: 25 },
        { competitor: 'Company B', strength: 'Brand Recognition', weakness: 'Innovation', marketShare: 18 },
        { competitor: 'Company C', strength: 'Pricing', weakness: 'Features', marketShare: 15 }
      ],
      customerFeedback: {
        satisfaction: 7.2,
        nps: 42,
        churnRate: 8.5,
        topRequests: [
          'Better mobile experience',
          'Faster response times',
          'More self-service options',
          'Enhanced reporting'
        ]
      }
    },
    feedbackSessions: [
      {
        id: 'FS001',
        title: 'Executive Steering Committee',
        date: '2024-02-15',
        type: 'Formal Review',
        attendees: 8,
        status: 'Completed',
        outcome: 'Approved with conditions',
        keyDecisions: ['Proceed with Phase 1', 'Increase budget by 10%', 'Add risk mitigation measures']
      },
      {
        id: 'FS002',
        title: 'User Focus Group',
        date: '2024-02-18',
        type: 'User Research',
        attendees: 12,
        status: 'Scheduled',
        outcome: 'Pending',
        keyDecisions: []
      },
      {
        id: 'FS003',
        title: 'Technical Architecture Review',
        date: '2024-02-20',
        type: 'Technical Review',
        attendees: 6,
        status: 'Scheduled',
        outcome: 'Pending',
        keyDecisions: []
      }
    ],
    ...data
  })

  const [activeView, setActiveView] = useState('stakeholders')

  const getSentimentColor = (sentiment) => {
    const colors = {
      'Positive': '#28a745',
      'Neutral': '#ffc107',
      'Negative': '#dc3545'
    }
    return colors[sentiment] || '#6c757d'
  }

  const getInfluenceColor = (influence) => {
    const colors = {
      'High': '#dc3545',
      'Medium': '#ffc107',
      'Low': '#28a745'
    }
    return colors[influence] || '#6c757d'
  }

  const getImpactColor = (impact) => {
    const colors = {
      'High': '#dc3545',
      'Medium': '#ffc107',
      'Low': '#28a745'
    }
    return colors[impact] || '#6c757d'
  }

  const renderStakeholders = () => (
    <div className="stakeholders-view">
      <div className="card">
        <div className="card-header">
          <h2 className="card-title">👥 Stakeholder Management</h2>
          <button className="btn btn-primary">+ Add Stakeholder</button>
        </div>
        
        <div className="stakeholder-summary">
          <div className="summary-metrics">
            <div className="summary-metric">
              <span className="metric-label">Total Stakeholders</span>
              <span className="metric-value">{feedbackData.stakeholders.length}</span>
            </div>
            <div className="summary-metric">
              <span className="metric-label">Avg Feedback Score</span>
              <span className="metric-value">
                {(feedbackData.stakeholders.reduce((sum, s) => sum + s.feedbackScore, 0) / feedbackData.stakeholders.length).toFixed(1)}
              </span>
            </div>
            <div className="summary-metric">
              <span className="metric-label">Positive Sentiment</span>
              <span className="metric-value">
                {Math.round((feedbackData.stakeholders.filter(s => s.sentiment === 'Positive').length / feedbackData.stakeholders.length) * 100)}%
              </span>
            </div>
          </div>
        </div>

        <div className="stakeholders-grid">
          {feedbackData.stakeholders.map((stakeholder) => (
            <div key={stakeholder.id} className="stakeholder-card">
              <div className="stakeholder-header">
                <div className="stakeholder-info">
                  <h4>{stakeholder.name}</h4>
                  <p>{stakeholder.role}</p>
                  <span className="stakeholder-category">{stakeholder.category}</span>
                </div>
                <div className="stakeholder-score">
                  <div className="score-circle">
                    <span>{stakeholder.feedbackScore}</span>
                  </div>
                </div>
              </div>
              
              <div className="stakeholder-attributes">
                <div className="attribute">
                  <span>Influence:</span>
                  <span 
                    className="attribute-badge"
                    style={{ 
                      backgroundColor: getInfluenceColor(stakeholder.influence),
                      color: 'white',
                      padding: '0.25rem 0.5rem',
                      borderRadius: '4px',
                      fontSize: '0.8rem'
                    }}
                  >
                    {stakeholder.influence}
                  </span>
                </div>
                <div className="attribute">
                  <span>Interest:</span>
                  <span className="attribute-value">{stakeholder.interest}</span>
                </div>
                <div className="attribute">
                  <span>Sentiment:</span>
                  <span 
                    className="sentiment-badge"
                    style={{ 
                      backgroundColor: getSentimentColor(stakeholder.sentiment),
                      color: 'white',
                      padding: '0.25rem 0.5rem',
                      borderRadius: '4px',
                      fontSize: '0.8rem'
                    }}
                  >
                    {stakeholder.sentiment}
                  </span>
                </div>
              </div>
              
              <div className="stakeholder-feedback">
                <h5>Key Feedback:</h5>
                <p>{stakeholder.keyFeedback}</p>
              </div>
              
              <div className="stakeholder-engagement">
                <small>Last Engagement: {stakeholder.lastEngagement}</small>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )

  const renderMarketIntelligence = () => (
    <div className="market-intelligence-view">
      <div className="card">
        <div className="card-header">
          <h2 className="card-title">📊 Market Intelligence</h2>
        </div>
        
        <div className="market-sections">
          <div className="market-section">
            <h3>📈 Market Trends</h3>
            <div className="trends-list">
              {feedbackData.marketIntelligence.marketTrends.map((trend, index) => (
                <div key={index} className="trend-item">
                  <div className="trend-header">
                    <span className="trend-name">{trend.trend}</span>
                    <span 
                      className="trend-impact"
                      style={{ 
                        backgroundColor: getImpactColor(trend.impact),
                        color: 'white',
                        padding: '0.25rem 0.5rem',
                        borderRadius: '4px',
                        fontSize: '0.8rem'
                      }}
                    >
                      {trend.impact} Impact
                    </span>
                  </div>
                  <div className="trend-details">
                    <span>Confidence: {trend.confidence}%</span>
                    <span>Source: {trend.source}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="market-section">
            <h3>🏢 Competitor Analysis</h3>
            <div className="competitors-table">
              <table className="data-table">
                <thead>
                  <tr>
                    <th>Competitor</th>
                    <th>Strength</th>
                    <th>Weakness</th>
                    <th>Market Share</th>
                  </tr>
                </thead>
                <tbody>
                  {feedbackData.marketIntelligence.competitorAnalysis.map((competitor, index) => (
                    <tr key={index}>
                      <td>{competitor.competitor}</td>
                      <td>{competitor.strength}</td>
                      <td>{competitor.weakness}</td>
                      <td>{competitor.marketShare}%</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          <div className="market-section">
            <h3>👥 Customer Insights</h3>
            <div className="customer-metrics">
              <div className="customer-metric">
                <div className="metric-icon">😊</div>
                <div className="metric-info">
                  <span className="metric-label">Satisfaction Score</span>
                  <span className="metric-value">{feedbackData.marketIntelligence.customerFeedback.satisfaction}/10</span>
                </div>
              </div>
              
              <div className="customer-metric">
                <div className="metric-icon">📊</div>
                <div className="metric-info">
                  <span className="metric-label">Net Promoter Score</span>
                  <span className="metric-value">{feedbackData.marketIntelligence.customerFeedback.nps}</span>
                </div>
              </div>
              
              <div className="customer-metric">
                <div className="metric-icon">📉</div>
                <div className="metric-info">
                  <span className="metric-label">Churn Rate</span>
                  <span className="metric-value">{feedbackData.marketIntelligence.customerFeedback.churnRate}%</span>
                </div>
              </div>
            </div>
            
            <div className="top-requests">
              <h4>Top Customer Requests:</h4>
              <ul>
                {feedbackData.marketIntelligence.customerFeedback.topRequests.map((request, index) => (
                  <li key={index}>{request}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  const renderFeedbackSessions = () => (
    <div className="feedback-sessions-view">
      <div className="card">
        <div className="card-header">
          <h2 className="card-title">🗣️ Feedback Sessions</h2>
          <button className="btn btn-primary">+ Schedule Session</button>
        </div>
        
        <div className="sessions-list">
          {feedbackData.feedbackSessions.map((session) => (
            <div key={session.id} className="session-card">
              <div className="session-header">
                <div className="session-info">
                  <h4>{session.title}</h4>
                  <p>{session.type} • {session.date}</p>
                </div>
                <div className="session-status">
                  <span 
                    className="status-badge"
                    style={{ 
                      backgroundColor: session.status === 'Completed' ? '#28a745' : '#ffc107',
                      color: 'white',
                      padding: '0.5rem 1rem',
                      borderRadius: '6px'
                    }}
                  >
                    {session.status}
                  </span>
                </div>
              </div>
              
              <div className="session-details">
                <div className="session-metric">
                  <span>Attendees: {session.attendees}</span>
                </div>
                <div className="session-metric">
                  <span>Outcome: {session.outcome}</span>
                </div>
              </div>
              
              {session.keyDecisions.length > 0 && (
                <div className="session-decisions">
                  <h5>Key Decisions:</h5>
                  <ul>
                    {session.keyDecisions.map((decision, index) => (
                      <li key={index}>{decision}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  )

  const views = [
    { id: 'stakeholders', label: 'Stakeholders', icon: '👥' },
    { id: 'market', label: 'Market Intelligence', icon: '📊' },
    { id: 'sessions', label: 'Feedback Sessions', icon: '🗣️' },
    { id: 'analytics', label: 'Analytics', icon: '📈' }
  ]

  return (
    <div className="stakeholder-feedback">
      <div className="feedback-navigation">
        {views.map((view) => (
          <button
            key={view.id}
            className={`feedback-nav-btn ${activeView === view.id ? 'active' : ''}`}
            onClick={() => setActiveView(view.id)}
          >
            <span className="nav-icon">{view.icon}</span>
            <span className="nav-label">{view.label}</span>
          </button>
        ))}
      </div>

      <div className="feedback-content">
        {activeView === 'stakeholders' && renderStakeholders()}
        {activeView === 'market' && renderMarketIntelligence()}
        {activeView === 'sessions' && renderFeedbackSessions()}
        {activeView === 'analytics' && (
          <div className="analytics-view">
            <div className="card">
              <div className="card-header">
                <h2 className="card-title">📈 Feedback Analytics</h2>
              </div>
              <div className="chart-placeholder">
                📊 Advanced Analytics Dashboard<br/>
                <small>(Sentiment trends, stakeholder engagement patterns, market impact analysis)</small>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default StakeholderFeedback
