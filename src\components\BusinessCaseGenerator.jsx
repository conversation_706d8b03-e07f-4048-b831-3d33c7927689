import React, { useState } from 'react'

const BusinessCaseGenerator = () => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [generatedCase, setGeneratedCase] = useState(null)
  const [showResult, setShowResult] = useState(false)
  
  const [formData, setFormData] = useState({
    projectTitle: '',
    problemStatement: '',
    proposedSolution: '',
    objectives: [''],
    executiveSummaryPoints: [''],
    scope: {
      inScope: [''],
      outOfScope: ['']
    },
    benefits: {
      quantitative: [''],
      qualitative: ['']
    },
    costs: {
      oneTime: [{ item: '', amount: 0 }],
      recurring: [{ item: '', amount: 0, frequency: 'monthly' }]
    },
    financialProjections: {
      roiPercentage: '',
      paybackPeriodYears: '',
      npv: ''
    },
    risksAndMitigation: [{ risk: '', mitigation: '' }],
    assumptions: [''],
    timelineMilestones: [{ milestone: '', targetDate: '' }],
    stakeholders: [''],
    alternativesConsidered: [{ alternative: '', reasonForRejection: '' }],
    successMetrics: [''],
    companyContext: '',
    targetAudience: 'Executive Board',
    desiredTone: 'Formal',
    outputFormat: 'markdown'
  })

  const API_BASE = process.env.REACT_APP_API_URL || 'http://localhost:5000/api'

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleArrayChange = (field, index, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].map((item, i) => i === index ? value : item)
    }))
  }

  const addArrayItem = (field, defaultValue = '') => {
    setFormData(prev => ({
      ...prev,
      [field]: [...prev[field], defaultValue]
    }))
  }

  const removeArrayItem = (field, index) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].filter((_, i) => i !== index)
    }))
  }

  const handleNestedChange = (field, subfield, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: {
        ...prev[field],
        [subfield]: value
      }
    }))
  }

  const handleObjectArrayChange = (field, index, key, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].map((item, i) => 
        i === index ? { ...item, [key]: value } : item
      )
    }))
  }

  const generateBusinessCase = async () => {
    setLoading(true)
    setError(null)
    
    try {
      // Clean up form data - remove empty strings and objects
      const cleanedData = {
        ...formData,
        objectives: formData.objectives.filter(obj => obj.trim() !== ''),
        executiveSummaryPoints: formData.executiveSummaryPoints.filter(point => point.trim() !== ''),
        scope: {
          inScope: formData.scope.inScope.filter(item => item.trim() !== ''),
          outOfScope: formData.scope.outOfScope.filter(item => item.trim() !== '')
        },
        benefits: {
          quantitative: formData.benefits.quantitative.filter(benefit => benefit.trim() !== ''),
          qualitative: formData.benefits.qualitative.filter(benefit => benefit.trim() !== '')
        },
        costs: {
          oneTime: formData.costs.oneTime.filter(cost => cost.item.trim() !== '' && cost.amount > 0),
          recurring: formData.costs.recurring.filter(cost => cost.item.trim() !== '' && cost.amount > 0)
        },
        financialProjections: {
          roiPercentage: formData.financialProjections.roiPercentage ? parseFloat(formData.financialProjections.roiPercentage) : undefined,
          paybackPeriodYears: formData.financialProjections.paybackPeriodYears ? parseFloat(formData.financialProjections.paybackPeriodYears) : undefined,
          npv: formData.financialProjections.npv ? parseFloat(formData.financialProjections.npv) : undefined
        },
        risksAndMitigation: formData.risksAndMitigation.filter(risk => risk.risk.trim() !== '' && risk.mitigation.trim() !== ''),
        assumptions: formData.assumptions.filter(assumption => assumption.trim() !== ''),
        timelineMilestones: formData.timelineMilestones.filter(milestone => milestone.milestone.trim() !== '' && milestone.targetDate !== ''),
        stakeholders: formData.stakeholders.filter(stakeholder => stakeholder.trim() !== ''),
        alternativesConsidered: formData.alternativesConsidered.filter(alt => alt.alternative.trim() !== '' && alt.reasonForRejection.trim() !== ''),
        successMetrics: formData.successMetrics.filter(metric => metric.trim() !== '')
      }

      const response = await fetch(`${API_BASE}/generateBusinessCase`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(cleanedData)
      })

      const result = await response.json()

      if (response.ok) {
        setGeneratedCase(result)
        setShowResult(true)
      } else {
        setError(result.errors?.map(err => err.message).join(', ') || 'Failed to generate business case')
      }
    } catch (err) {
      setError('Network error: ' + err.message)
    } finally {
      setLoading(false)
    }
  }

  const downloadBusinessCase = () => {
    if (!generatedCase) return
    
    const blob = new Blob([generatedCase.generatedText], { type: 'text/markdown' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `business-case-${formData.projectTitle.replace(/\s+/g, '-').toLowerCase()}.md`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  if (showResult && generatedCase) {
    return (
      <div className="business-case-generator">
        <div className="result-header">
          <h2>📄 Generated Business Case</h2>
          <div className="result-actions">
            <button 
              className="btn btn-secondary"
              onClick={() => setShowResult(false)}
            >
              ← Back to Form
            </button>
            <button 
              className="btn btn-primary"
              onClick={downloadBusinessCase}
            >
              📥 Download
            </button>
          </div>
        </div>
        
        <div className="generated-content">
          <div className="content-info">
            <p><strong>Business Case ID:</strong> {generatedCase.businessCaseId}</p>
            {generatedCase.savedBusinessCase && (
              <p><strong>Saved to Database:</strong> {generatedCase.savedBusinessCase.programName}</p>
            )}
          </div>
          
          <div className="content-preview">
            <pre>{generatedCase.generatedText}</pre>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="business-case-generator">
      <div className="generator-header">
        <h2>🤖 AI Business Case Generator</h2>
        <p>Generate comprehensive business cases using our intelligent API</p>
      </div>

      {error && (
        <div className="error-message">
          <span className="error-icon">⚠️</span>
          <span>{error}</span>
          <button onClick={() => setError(null)}>✕</button>
        </div>
      )}

      <form onSubmit={(e) => { e.preventDefault(); generateBusinessCase(); }} className="generator-form">
        {/* Basic Information */}
        <div className="form-section">
          <h3>📋 Basic Information</h3>
          
          <div className="form-group">
            <label>Project Title *</label>
            <input
              type="text"
              value={formData.projectTitle}
              onChange={(e) => handleInputChange('projectTitle', e.target.value)}
              placeholder="e.g., Digital Transformation Initiative"
              required
            />
          </div>

          <div className="form-group">
            <label>Problem Statement *</label>
            <textarea
              value={formData.problemStatement}
              onChange={(e) => handleInputChange('problemStatement', e.target.value)}
              placeholder="Describe the problem or opportunity this project addresses..."
              rows={4}
              required
            />
          </div>

          <div className="form-group">
            <label>Proposed Solution *</label>
            <textarea
              value={formData.proposedSolution}
              onChange={(e) => handleInputChange('proposedSolution', e.target.value)}
              placeholder="Describe your proposed solution in detail..."
              rows={4}
              required
            />
          </div>
        </div>

        {/* Objectives */}
        <div className="form-section">
          <h3>🎯 Project Objectives</h3>
          {formData.objectives.map((objective, index) => (
            <div key={index} className="array-input">
              <input
                type="text"
                value={objective}
                onChange={(e) => handleArrayChange('objectives', index, e.target.value)}
                placeholder="e.g., Increase efficiency by 30% within 6 months"
              />
              <button 
                type="button" 
                onClick={() => removeArrayItem('objectives', index)}
                className="btn-remove"
              >
                ✕
              </button>
            </div>
          ))}
          <button 
            type="button" 
            onClick={() => addArrayItem('objectives')}
            className="btn btn-secondary btn-sm"
          >
            + Add Objective
          </button>
        </div>

        {/* Financial Projections */}
        <div className="form-section">
          <h3>💰 Financial Projections</h3>
          
          <div className="form-row">
            <div className="form-group">
              <label>ROI Percentage</label>
              <input
                type="number"
                value={formData.financialProjections.roiPercentage}
                onChange={(e) => handleNestedChange('financialProjections', 'roiPercentage', e.target.value)}
                placeholder="150"
              />
            </div>
            
            <div className="form-group">
              <label>Payback Period (Years)</label>
              <input
                type="number"
                step="0.1"
                value={formData.financialProjections.paybackPeriodYears}
                onChange={(e) => handleNestedChange('financialProjections', 'paybackPeriodYears', e.target.value)}
                placeholder="2.5"
              />
            </div>
            
            <div className="form-group">
              <label>Net Present Value ($)</label>
              <input
                type="number"
                value={formData.financialProjections.npv}
                onChange={(e) => handleNestedChange('financialProjections', 'npv', e.target.value)}
                placeholder="75000"
              />
            </div>
          </div>
        </div>

        {/* Configuration */}
        <div className="form-section">
          <h3>⚙️ Configuration</h3>
          
          <div className="form-row">
            <div className="form-group">
              <label>Target Audience</label>
              <select
                value={formData.targetAudience}
                onChange={(e) => handleInputChange('targetAudience', e.target.value)}
              >
                <option value="Executive Board">Executive Board</option>
                <option value="Investment Committee">Investment Committee</option>
                <option value="Steering Committee">Steering Committee</option>
                <option value="Department Heads">Department Heads</option>
              </select>
            </div>
            
            <div className="form-group">
              <label>Desired Tone</label>
              <select
                value={formData.desiredTone}
                onChange={(e) => handleInputChange('desiredTone', e.target.value)}
              >
                <option value="Formal">Formal</option>
                <option value="Persuasive">Persuasive</option>
                <option value="Concise">Concise</option>
                <option value="Technical">Technical</option>
              </select>
            </div>
            
            <div className="form-group">
              <label>Output Format</label>
              <select
                value={formData.outputFormat}
                onChange={(e) => handleInputChange('outputFormat', e.target.value)}
              >
                <option value="markdown">Markdown</option>
                <option value="plaintext">Plain Text</option>
              </select>
            </div>
          </div>
        </div>

        <div className="form-actions">
          <button 
            type="submit" 
            className="btn btn-primary btn-lg"
            disabled={loading || !formData.projectTitle || !formData.problemStatement || !formData.proposedSolution}
          >
            {loading ? '🤖 Generating...' : '🚀 Generate Business Case'}
          </button>
        </div>
      </form>
    </div>
  )
}

export default BusinessCaseGenerator
