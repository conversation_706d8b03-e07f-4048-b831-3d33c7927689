import React, { useState, useEffect } from 'react'

const CostSalesDefinition = ({ data, onChange }) => {
  const [activeSection, setActiveSection] = useState('costs')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingItem, setEditingItem] = useState(null)

  // Cost and Sales data for full-stack CRUD
  const [costs, setCosts] = useState([])
  const [sales, setSales] = useState([])

  // Financial calculations
  const [financialIndexes, setFinancialIndexes] = useState({
    totalCosts: 0,
    totalRevenue: 0,
    netProfit: 0,
    roi: 0,
    npv: 0,
    irr: 0,
    paybackPeriod: 0,
    profitabilityIndex: 0,
    breakEvenPoint: 0
  })

  // Form data for new items
  const [formData, setFormData] = useState({
    name: '',
    category: '',
    amount: 0,
    type: 'fixed',
    description: '',
    year: new Date().getFullYear(),
    frequency: 'annual',
    units: 0,
    unitPrice: 0,
    quarter: 'Q1',
    region: 'Global'
  })

  // API Base URL
  const API_BASE = process.env.REACT_APP_API_URL || 'http://localhost:5000/api'

  const [salesData, setSalesData] = useState({
    regions: [
      { name: 'North America', share: 40, growth: 5 },
      { name: 'Europe', share: 35, growth: 3 },
      { name: 'Asia Pacific', share: 25, growth: 8 }
    ],
    offers: [
      { name: 'Premium Package', price: 1200, margin: 35, volume: 5000 },
      { name: 'Standard Package', price: 800, margin: 25, volume: 8000 },
      { name: 'Basic Package', price: 500, margin: 15, volume: 12000 }
    ],
    ...data?.sales
  })

  // Load data on component mount
  useEffect(() => {
    loadData()
  }, [])

  // Recalculate financial indexes when data changes
  useEffect(() => {
    calculateFinancialIndexes()
  }, [costs, sales])

  // API Functions
  const apiCall = async (endpoint, options = {}) => {
    try {
      const response = await fetch(`${API_BASE}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        ...options
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('API call failed:', error)
      throw error
    }
  }

  const loadData = async () => {
    setLoading(true)
    setError(null)

    try {
      const [costsData, salesData, financialData] = await Promise.all([
        apiCall('/costs'),
        apiCall('/sales'),
        apiCall('/financial-indexes')
      ])

      setCosts(costsData)
      setSales(salesData)
      setFinancialIndexes(financialData)
    } catch (err) {
      setError('Failed to load data. Using offline mode.')
      // Fallback to mock data
      setCosts(getMockCosts())
      setSales(getMockSales())
      calculateFinancialIndexes()
    } finally {
      setLoading(false)
    }
  }

  const saveItem = async (item, type) => {
    setLoading(true)
    setError(null)

    try {
      const endpoint = editingItem ? `/${type}/${editingItem._id}` : `/${type}`
      const method = editingItem ? 'PUT' : 'POST'

      const result = await apiCall(endpoint, {
        method,
        body: JSON.stringify(item)
      })

      if (type === 'costs') {
        if (editingItem) {
          setCosts(prev => prev.map(c => c._id === editingItem._id ? result.cost : c))
        } else {
          setCosts(prev => [...prev, result.cost])
        }
      } else {
        if (editingItem) {
          setSales(prev => prev.map(s => s._id === editingItem._id ? result.sales : s))
        } else {
          setSales(prev => [...prev, result.sales])
        }
      }

      // Update financial indexes
      if (result.financialIndexes) {
        setFinancialIndexes(result.financialIndexes)
      }

      resetForm()
      setShowAddForm(false)
      setEditingItem(null)
    } catch (err) {
      setError(`Failed to save ${type}. Changes saved locally.`)
      // Fallback to local state management
      const newItem = { ...item, _id: Date.now().toString(), createdAt: new Date().toISOString() }

      if (type === 'costs') {
        if (editingItem) {
          setCosts(prev => prev.map(c => c._id === editingItem._id ? newItem : c))
        } else {
          setCosts(prev => [...prev, newItem])
        }
      } else {
        if (editingItem) {
          setSales(prev => prev.map(s => s._id === editingItem._id ? newItem : s))
        } else {
          setSales(prev => [...prev, newItem])
        }
      }

      calculateFinancialIndexes()
      resetForm()
      setShowAddForm(false)
      setEditingItem(null)
    } finally {
      setLoading(false)
    }
  }

  const deleteItem = async (id, type) => {
    setLoading(true)
    setError(null)

    try {
      const result = await apiCall(`/${type}/${id}`, { method: 'DELETE' })

      if (type === 'costs') {
        setCosts(prev => prev.filter(c => c._id !== id))
      } else {
        setSales(prev => prev.filter(s => s._id !== id))
      }

      // Update financial indexes
      if (result.financialIndexes) {
        setFinancialIndexes(result.financialIndexes)
      }
    } catch (err) {
      setError(`Failed to delete ${type}. Removed locally.`)
      // Fallback to local deletion
      if (type === 'costs') {
        setCosts(prev => prev.filter(c => c._id !== id))
      } else {
        setSales(prev => prev.filter(s => s._id !== id))
      }
      calculateFinancialIndexes()
    } finally {
      setLoading(false)
    }
  }

  // Financial calculations (local fallback)
  const calculateFinancialIndexes = () => {
    const totalCosts = costs.reduce((sum, cost) => sum + (cost.amount || 0), 0)
    const totalRevenue = sales.reduce((sum, sale) => sum + (sale.revenue || sale.amount || 0), 0)
    const netProfit = totalRevenue - totalCosts
    const roi = totalCosts > 0 ? ((netProfit / totalCosts) * 100) : 0

    // NPV calculation (simplified)
    const discountRate = 0.10
    const years = 5
    const annualCashFlow = netProfit / years
    let npv = -totalCosts
    for (let year = 1; year <= years; year++) {
      npv += annualCashFlow / Math.pow(1 + discountRate, year)
    }

    const irr = totalCosts > 0 ? ((Math.pow(totalRevenue / totalCosts, 1/years) - 1) * 100) : 0
    const paybackPeriod = annualCashFlow > 0 ? totalCosts / annualCashFlow : 0
    const profitabilityIndex = totalCosts > 0 ? (npv + totalCosts) / totalCosts : 0
    const avgUnitPrice = sales.length > 0 ? sales.reduce((sum, sale) => sum + (sale.unitPrice || 0), 0) / sales.length : 0
    const breakEvenPoint = avgUnitPrice > 0 ? totalCosts / avgUnitPrice : 0

    const newIndexes = {
      totalCosts: Math.round(totalCosts),
      totalRevenue: Math.round(totalRevenue),
      netProfit: Math.round(netProfit),
      roi: Math.round(roi * 100) / 100,
      npv: Math.round(npv),
      irr: Math.round(irr * 100) / 100,
      paybackPeriod: Math.round(paybackPeriod * 100) / 100,
      profitabilityIndex: Math.round(profitabilityIndex * 100) / 100,
      breakEvenPoint: Math.round(breakEvenPoint)
    }

    setFinancialIndexes(newIndexes)

    // Update parent component
    if (onChange) {
      onChange({
        costs,
        sales,
        financialIndexes: newIndexes
      })
    }
  }

  // Form handlers
  const resetForm = () => {
    setFormData({
      name: '',
      category: '',
      amount: 0,
      type: 'fixed',
      description: '',
      year: new Date().getFullYear(),
      frequency: 'annual',
      units: 0,
      unitPrice: 0,
      quarter: 'Q1',
      region: 'Global'
    })
  }

  const handleSubmit = (e) => {
    e.preventDefault()

    if (activeSection === 'costs') {
      const costItem = {
        ...formData,
        category: formData.category || 'Other'
      }
      saveItem(costItem, 'costs')
    } else if (activeSection === 'sales') {
      const salesItem = {
        ...formData,
        revenue: formData.units * formData.unitPrice,
        category: formData.category || 'Other'
      }
      saveItem(salesItem, 'sales')
    }
  }

  const startEdit = (item) => {
    setEditingItem(item)
    setFormData({
      name: item.name || '',
      category: item.category || '',
      amount: item.amount || 0,
      type: item.type || 'fixed',
      description: item.description || '',
      year: item.year || new Date().getFullYear(),
      frequency: item.frequency || 'annual',
      units: item.units || 0,
      unitPrice: item.unitPrice || 0,
      quarter: item.quarter || 'Q1',
      region: item.region || 'Global'
    })
    setShowAddForm(true)
  }

  // Mock data functions
  const getMockCosts = () => [
    {
      _id: '1',
      name: 'Personnel Costs',
      category: 'Personnel',
      amount: 500000,
      type: 'fixed',
      description: 'Annual salary and benefits',
      year: 2024,
      frequency: 'annual'
    },
    {
      _id: '2',
      name: 'Equipment Purchase',
      category: 'Equipment',
      amount: 300000,
      type: 'one-time',
      description: 'Manufacturing equipment',
      year: 2024,
      frequency: 'one-time'
    }
  ]

  const getMockSales = () => [
    {
      _id: '1',
      name: 'Product A Sales',
      category: 'Product Sales',
      revenue: 800000,
      units: 1000,
      unitPrice: 800,
      description: 'Premium product line',
      year: 2024,
      quarter: 'Q1'
    },
    {
      _id: '2',
      name: 'Service Revenue',
      category: 'Service Revenue',
      revenue: 400000,
      units: 500,
      unitPrice: 800,
      description: 'Consulting and support services',
      year: 2024,
      quarter: 'Q2'
    }
  ]

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const costCategories = ['Personnel', 'Equipment', 'Software', 'Infrastructure', 'Marketing', 'Operations', 'Other']
  const salesCategories = ['Product Sales', 'Service Revenue', 'Licensing', 'Subscriptions', 'Consulting', 'Other']

  const sections = [
    { id: 'costs', label: 'Cost Management', icon: '💰' },
    { id: 'sales', label: 'Sales Management', icon: '📈' },
    { id: 'analytics', label: 'Financial Analytics', icon: '📊' },
    { id: 'capex', label: 'CAPEX', icon: '🏗️' },
    { id: 'opex', label: 'OPEX', icon: '🔄' },
    { id: 'tools', label: 'Tools', icon: '�️' },
    { id: 'machinery', label: 'Machinery', icon: '⚙️' }
  ]

  const addCostItem = (section) => {
    const newItem = section === 'capex'
      ? { name: '', amount: 0, year: 1 }
      : section === 'opex'
      ? { name: '', amount: 0, recurring: true }
      : section === 'tools'
      ? { name: '', amount: 0, lifespan: 1 }
      : { name: '', amount: 0, capacity: 0, year: 1 }

    setCosts(prev => ({
      ...prev,
      [section]: [...prev[section], newItem]
    }))
  }

  const updateCostItem = (section, index, field, value) => {
    setCosts(prev => ({
      ...prev,
      [section]: prev[section].map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      )
    }))
  }

  const renderCostSection = (section) => {
    const items = costs[section] || []

    return (
      <div className="cost-section">
        <div className="section-header">
          <h3>{sections.find(s => s.id === section)?.label} Items</h3>
          <button
            className="btn btn-primary"
            onClick={() => addCostItem(section)}
          >
            + Add Item
          </button>
        </div>

        <div className="cost-items">
          {items.map((item, index) => (
            <div key={index} className="cost-item">
              <div className="form-row">
                <div className="form-group">
                  <label className="form-label">Name</label>
                  <input
                    type="text"
                    className="form-input"
                    value={item.name}
                    onChange={(e) => updateCostItem(section, index, 'name', e.target.value)}
                  />
                </div>

                <div className="form-group">
                  <label className="form-label">Amount ($)</label>
                  <input
                    type="number"
                    className="form-input"
                    value={item.amount}
                    onChange={(e) => updateCostItem(section, index, 'amount', parseFloat(e.target.value))}
                  />
                </div>

                {section === 'capex' && (
                  <div className="form-group">
                    <label className="form-label">Year</label>
                    <input
                      type="number"
                      className="form-input"
                      value={item.year}
                      onChange={(e) => updateCostItem(section, index, 'year', parseInt(e.target.value))}
                    />
                  </div>
                )}

                {section === 'tools' && (
                  <div className="form-group">
                    <label className="form-label">Lifespan (Years)</label>
                    <input
                      type="number"
                      className="form-input"
                      value={item.lifespan}
                      onChange={(e) => updateCostItem(section, index, 'lifespan', parseInt(e.target.value))}
                    />
                  </div>
                )}

                {section === 'machinery' && (
                  <>
                    <div className="form-group">
                      <label className="form-label">Capacity</label>
                      <input
                        type="number"
                        className="form-input"
                        value={item.capacity}
                        onChange={(e) => updateCostItem(section, index, 'capacity', parseInt(e.target.value))}
                      />
                    </div>
                    <div className="form-group">
                      <label className="form-label">Year</label>
                      <input
                        type="number"
                        className="form-input"
                        value={item.year}
                        onChange={(e) => updateCostItem(section, index, 'year', parseInt(e.target.value))}
                      />
                    </div>
                  </>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  const renderSalesSection = () => (
    <div className="sales-section">
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">🌍 Sales by Region</h3>
        </div>
        {salesData.regions.map((region, index) => (
          <div key={index} className="form-row">
            <div className="form-group">
              <label className="form-label">Region</label>
              <input type="text" className="form-input" value={region.name} readOnly />
            </div>
            <div className="form-group">
              <label className="form-label">Market Share (%)</label>
              <input type="number" className="form-input" value={region.share} />
            </div>
            <div className="form-group">
              <label className="form-label">Growth Rate (%)</label>
              <input type="number" className="form-input" value={region.growth} />
            </div>
          </div>
        ))}
      </div>

      <div className="card">
        <div className="card-header">
          <h3 className="card-title">📦 Product Offers</h3>
        </div>
        {salesData.offers.map((offer, index) => (
          <div key={index} className="form-row">
            <div className="form-group">
              <label className="form-label">Offer Name</label>
              <input type="text" className="form-input" value={offer.name} />
            </div>
            <div className="form-group">
              <label className="form-label">Price ($)</label>
              <input type="number" className="form-input" value={offer.price} />
            </div>
            <div className="form-group">
              <label className="form-label">Margin (%)</label>
              <input type="number" className="form-input" value={offer.margin} />
            </div>
            <div className="form-group">
              <label className="form-label">Volume (Units)</label>
              <input type="number" className="form-input" value={offer.volume} />
            </div>
          </div>
        ))}
      </div>
    </div>
  )

  return (
    <div className="cost-sales-definition">
      <div className="section-navigation">
        {sections.map((section) => (
          <button
            key={section.id}
            className={`section-btn ${activeSection === section.id ? 'active' : ''}`}
            onClick={() => setActiveSection(section.id)}
          >
            <span className="section-icon">{section.icon}</span>
            <span className="section-label">{section.label}</span>
          </button>
        ))}
      </div>

      <div className="section-content">
        {activeSection === 'sales' ? renderSalesSection() : (
          <div className="card">
            {renderCostSection(activeSection)}
          </div>
        )}
      </div>
    </div>
  )
}

export default CostSalesDefinition
