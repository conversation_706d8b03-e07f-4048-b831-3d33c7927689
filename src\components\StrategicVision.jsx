import React, { useState } from 'react'

const StrategicVision = ({ data, onChange }) => {
  const [activeSection, setActiveSection] = useState('vision')

  const handleChange = (field, value) => {
    const updatedData = { ...data, [field]: value }
    onChange(updatedData)
  }

  const handleArrayChange = (field, index, value) => {
    const updatedArray = [...(data[field] || [])]
    updatedArray[index] = value
    handleChange(field, updatedArray)
  }

  const addArrayItem = (field, defaultValue = '') => {
    const updatedArray = [...(data[field] || []), defaultValue]
    handleChange(field, updatedArray)
  }

  const removeArrayItem = (field, index) => {
    const updatedArray = (data[field] || []).filter((_, i) => i !== index)
    handleChange(field, updatedArray)
  }

  const sections = [
    { id: 'vision', label: 'Strategic Vision', icon: '🎯' },
    { id: 'tenets', label: 'Key Tenets', icon: '📋' },
    { id: 'communication', label: 'Communication Strategy', icon: '📢' },
    { id: 'influence', label: 'Business Case Influence', icon: '💼' }
  ]

  const renderVisionSection = () => (
    <div className="vision-section">
      <h3>🎯 Strategic Vision Definition</h3>
      
      <div className="form-group">
        <label>Mission Statement</label>
        <textarea
          value={data.mission || ''}
          onChange={(e) => handleChange('mission', e.target.value)}
          placeholder="Define the organization's core mission and purpose..."
          rows={4}
        />
      </div>

      <div className="form-group">
        <label>Vision Statement</label>
        <textarea
          value={data.vision || ''}
          onChange={(e) => handleChange('vision', e.target.value)}
          placeholder="Describe the long-term aspirational direction..."
          rows={4}
        />
      </div>

      <div className="form-group">
        <label>Core Values</label>
        {(data.coreValues || []).map((value, index) => (
          <div key={index} className="array-input">
            <input
              type="text"
              value={value}
              onChange={(e) => handleArrayChange('coreValues', index, e.target.value)}
              placeholder="e.g., Innovation, Integrity, Excellence"
            />
            <button 
              type="button" 
              onClick={() => removeArrayItem('coreValues', index)}
              className="btn-remove"
            >
              ✕
            </button>
          </div>
        ))}
        <button 
          type="button" 
          onClick={() => addArrayItem('coreValues')}
          className="btn btn-secondary btn-sm"
        >
          + Add Core Value
        </button>
      </div>

      <div className="form-group">
        <label>Strategic Focus Areas</label>
        {(data.focusAreas || []).map((area, index) => (
          <div key={index} className="array-input">
            <input
              type="text"
              value={area}
              onChange={(e) => handleArrayChange('focusAreas', index, e.target.value)}
              placeholder="e.g., Digital Transformation, Sustainability, Market Leadership"
            />
            <button 
              type="button" 
              onClick={() => removeArrayItem('focusAreas', index)}
              className="btn-remove"
            >
              ✕
            </button>
          </div>
        ))}
        <button 
          type="button" 
          onClick={() => addArrayItem('focusAreas')}
          className="btn btn-secondary btn-sm"
        >
          + Add Focus Area
        </button>
      </div>
    </div>
  )

  const renderTenetsSection = () => (
    <div className="tenets-section">
      <h3>📋 Key Strategic Tenets</h3>
      
      <div className="tenet-card">
        <h4>🏛️ Policy Alignment</h4>
        <div className="form-group">
          <label>National/Organizational Policy Alignment</label>
          <textarea
            value={data.policyAlignment || ''}
            onChange={(e) => handleChange('policyAlignment', e.target.value)}
            placeholder="Describe how the strategy aligns with national or organizational policies..."
            rows={3}
          />
        </div>
      </div>

      <div className="tenet-card">
        <h4>📈 Long-term Value Creation</h4>
        <div className="form-group">
          <label>Value Creation Framework</label>
          <textarea
            value={data.valueCreation || ''}
            onChange={(e) => handleChange('valueCreation', e.target.value)}
            placeholder="Define how long-term value will be created and measured..."
            rows={3}
          />
        </div>
      </div>

      <div className="tenet-card">
        <h4>👥 Stakeholder Outcomes</h4>
        <div className="form-group">
          <label>Benefits Realization Strategy</label>
          <textarea
            value={data.benefitsRealization || ''}
            onChange={(e) => handleChange('benefitsRealization', e.target.value)}
            placeholder="Describe the approach to stakeholder outcomes and benefits realization..."
            rows={3}
          />
        </div>
      </div>

      <div className="tenet-card">
        <h4>🌍 Market & Societal Responsiveness</h4>
        <div className="form-group">
          <label>Market Responsiveness Strategy</label>
          <textarea
            value={data.marketResponsiveness || ''}
            onChange={(e) => handleChange('marketResponsiveness', e.target.value)}
            placeholder="Explain how the strategy responds to market and societal needs..."
            rows={3}
          />
        </div>
      </div>
    </div>
  )

  const renderCommunicationSection = () => (
    <div className="communication-section">
      <h3>📢 Communication & Understanding</h3>
      
      <div className="form-group">
        <label>Strategic Planning Documents</label>
        {(data.planningDocuments || []).map((doc, index) => (
          <div key={index} className="array-input">
            <input
              type="text"
              value={doc}
              onChange={(e) => handleArrayChange('planningDocuments', index, e.target.value)}
              placeholder="e.g., Strategic Plan 2025-2030, Digital Strategy Framework"
            />
            <button 
              type="button" 
              onClick={() => removeArrayItem('planningDocuments', index)}
              className="btn-remove"
            >
              ✕
            </button>
          </div>
        ))}
        <button 
          type="button" 
          onClick={() => addArrayItem('planningDocuments')}
          className="btn btn-secondary btn-sm"
        >
          + Add Document
        </button>
      </div>

      <div className="form-group">
        <label>Executive Briefing Strategy</label>
        <textarea
          value={data.executiveBriefing || ''}
          onChange={(e) => handleChange('executiveBriefing', e.target.value)}
          placeholder="Describe the approach for executive briefings and leadership communication..."
          rows={3}
        />
      </div>

      <div className="form-group">
        <label>Stakeholder Engagement Sessions</label>
        {(data.engagementSessions || []).map((session, index) => (
          <div key={index} className="array-input">
            <input
              type="text"
              value={session}
              onChange={(e) => handleArrayChange('engagementSessions', index, e.target.value)}
              placeholder="e.g., Quarterly Town Halls, Department Head Meetings"
            />
            <button 
              type="button" 
              onClick={() => removeArrayItem('engagementSessions', index)}
              className="btn-remove"
            >
              ✕
            </button>
          </div>
        ))}
        <button 
          type="button" 
          onClick={() => addArrayItem('engagementSessions')}
          className="btn btn-secondary btn-sm"
        >
          + Add Session Type
        </button>
      </div>

      <div className="form-group">
        <label>Program Charter Embedding</label>
        <textarea
          value={data.charterEmbedding || ''}
          onChange={(e) => handleChange('charterEmbedding', e.target.value)}
          placeholder="Explain how strategic vision is embedded in program charters and business case templates..."
          rows={3}
        />
      </div>
    </div>
  )

  const renderInfluenceSection = () => (
    <div className="influence-section">
      <h3>💼 Influence on Business Cases & Product Sales</h3>
      
      <div className="form-group">
        <label>Success Criteria Framework</label>
        <div className="criteria-grid">
          <div className="criteria-item">
            <label>ROI Thresholds</label>
            <input
              type="number"
              value={data.roiThreshold || ''}
              onChange={(e) => handleChange('roiThreshold', e.target.value)}
              placeholder="Minimum ROI %"
            />
          </div>
          <div className="criteria-item">
            <label>Social Impact Weight</label>
            <input
              type="number"
              value={data.socialImpactWeight || ''}
              onChange={(e) => handleChange('socialImpactWeight', e.target.value)}
              placeholder="Impact scoring weight"
            />
          </div>
          <div className="criteria-item">
            <label>Innovation Score</label>
            <input
              type="number"
              value={data.innovationScore || ''}
              onChange={(e) => handleChange('innovationScore', e.target.value)}
              placeholder="Innovation threshold"
            />
          </div>
        </div>
      </div>

      <div className="form-group">
        <label>Market Positioning Strategy</label>
        <textarea
          value={data.marketPositioning || ''}
          onChange={(e) => handleChange('marketPositioning', e.target.value)}
          placeholder="Define how strategic vision shapes market positioning and value propositions..."
          rows={3}
        />
      </div>

      <div className="form-group">
        <label>Product Sales Strategy Alignment</label>
        <textarea
          value={data.salesAlignment || ''}
          onChange={(e) => handleChange('salesAlignment', e.target.value)}
          placeholder="Describe how product sales strategy must reflect strategic vision..."
          rows={3}
        />
      </div>

      <div className="form-group">
        <label>Business Case Evaluation Criteria</label>
        {(data.evaluationCriteria || []).map((criteria, index) => (
          <div key={index} className="array-input">
            <input
              type="text"
              value={criteria}
              onChange={(e) => handleArrayChange('evaluationCriteria', index, e.target.value)}
              placeholder="e.g., Strategic Alignment Score, Risk-Adjusted NPV, Stakeholder Impact"
            />
            <button 
              type="button" 
              onClick={() => removeArrayItem('evaluationCriteria', index)}
              className="btn-remove"
            >
              ✕
            </button>
          </div>
        ))}
        <button 
          type="button" 
          onClick={() => addArrayItem('evaluationCriteria')}
          className="btn btn-secondary btn-sm"
        >
          + Add Criteria
        </button>
      </div>
    </div>
  )

  return (
    <div className="strategic-vision">
      <div className="vision-header">
        <h2>🎯 Strategic Vision Framework</h2>
        <p>Define the foundational strategic elements that guide program management and business case development</p>
      </div>

      <div className="vision-navigation">
        {sections.map(section => (
          <button
            key={section.id}
            className={`nav-btn ${activeSection === section.id ? 'active' : ''}`}
            onClick={() => setActiveSection(section.id)}
          >
            {section.icon} {section.label}
          </button>
        ))}
      </div>

      <div className="vision-content">
        {activeSection === 'vision' && renderVisionSection()}
        {activeSection === 'tenets' && renderTenetsSection()}
        {activeSection === 'communication' && renderCommunicationSection()}
        {activeSection === 'influence' && renderInfluenceSection()}
      </div>

      <div className="vision-summary">
        <h4>📊 Strategic Vision Summary</h4>
        <div className="summary-stats">
          <div className="stat-item">
            <span className="stat-label">Core Values:</span>
            <span className="stat-value">{(data.coreValues || []).length}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Focus Areas:</span>
            <span className="stat-value">{(data.focusAreas || []).length}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Evaluation Criteria:</span>
            <span className="stat-value">{(data.evaluationCriteria || []).length}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">ROI Threshold:</span>
            <span className="stat-value">{data.roiThreshold || 'Not Set'}%</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default StrategicVision
