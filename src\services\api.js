import mockDataService from './mockDataService';

// Use environment variables with fallbacks for browser compatibility
const API_BASE_URL = (typeof process !== 'undefined' && process.env?.REACT_APP_API_URL) || 'http://localhost:5000/api';
const USE_MOCK_DATA = (typeof process !== 'undefined' && process.env?.REACT_APP_USE_MOCK_DATA) !== 'false'; // Default to true for demo

class ApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
    this.useMockData = USE_MOCK_DATA;
  }

  // Generic request method
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;

    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // GET request
  async get(endpoint, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `${endpoint}?${queryString}` : endpoint;

    return this.request(url, {
      method: 'GET',
    });
  }

  // POST request
  async post(endpoint, data) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // PUT request
  async put(endpoint, data) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  // PATCH request
  async patch(endpoint, data) {
    return this.request(endpoint, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  // DELETE request
  async delete(endpoint) {
    return this.request(endpoint, {
      method: 'DELETE',
    });
  }
}

class MasterBusinessCaseService extends ApiService {
  constructor() {
    super();
    this.endpoint = '/master-business-cases';
  }

  // Get all master business cases with pagination and filtering
  async getAll(params = {}) {
    if (this.useMockData) {
      return mockDataService.getAll(params);
    }
    return this.get(this.endpoint, params);
  }

  // Get single master business case by ID
  async getById(id) {
    if (this.useMockData) {
      return mockDataService.getById(id);
    }
    return this.get(`${this.endpoint}/${id}`);
  }

  // Create new master business case
  async create(data) {
    if (this.useMockData) {
      return mockDataService.create(data);
    }
    return this.post(this.endpoint, data);
  }

  // Update master business case
  async update(id, data) {
    if (this.useMockData) {
      return mockDataService.update(id, data);
    }
    return this.put(`${this.endpoint}/${id}`, data);
  }

  // Delete master business case
  async delete(id) {
    if (this.useMockData) {
      return mockDataService.delete(id);
    }
    return this.delete(`${this.endpoint}/${id}`);
  }

  // Update status only
  async updateStatus(id, status, updatedBy) {
    if (this.useMockData) {
      return mockDataService.updateStatus(id, status, updatedBy);
    }
    return this.patch(`${this.endpoint}/${id}/status`, { status, updatedBy });
  }

  // Get summary statistics
  async getStats() {
    if (this.useMockData) {
      return mockDataService.getStats();
    }
    return this.get(`${this.endpoint}/stats/summary`);
  }

  // Search master business cases
  async search(query, filters = {}) {
    const params = {
      search: query,
      ...filters
    };
    return this.get(this.endpoint, params);
  }

  // Get master business cases by status
  async getByStatus(status, params = {}) {
    return this.get(this.endpoint, { status, ...params });
  }

  // Get master business cases by business driver
  async getByBusinessDriver(businessDriver, params = {}) {
    return this.get(this.endpoint, { businessDriver, ...params });
  }

  // Get master business cases by investment category
  async getByInvestmentCategory(investmentCategory, params = {}) {
    return this.get(this.endpoint, { investmentCategory, ...params });
  }

  // Get master business cases created by specific user
  async getByCreatedBy(createdBy, params = {}) {
    return this.get(this.endpoint, { createdBy, ...params });
  }

  // Duplicate master business case
  async duplicate(id, newProgramName) {
    if (this.useMockData) {
      return mockDataService.duplicate(id, newProgramName);
    }

    try {
      const response = await this.getById(id);
      const originalCase = response.data;

      // Create a copy with modified data
      const duplicatedCase = {
        ...originalCase,
        _id: undefined, // Remove the ID so MongoDB creates a new one
        programInfo: {
          ...originalCase.programInfo,
          programName: newProgramName || `${originalCase.programInfo.programName} (Copy)`,
        },
        status: 'Draft',
        createdBy: 'Current User', // Should be replaced with actual user
        lastModifiedBy: undefined,
        createdAt: undefined,
        updatedAt: undefined,
      };

      return this.create(duplicatedCase);
    } catch (error) {
      console.error('Error duplicating master business case:', error);
      throw error;
    }
  }

  // Export master business case data
  async export(id, format = 'json') {
    try {
      const response = await this.getById(id);
      const data = response.data;

      if (format === 'json') {
        return JSON.stringify(data, null, 2);
      } else if (format === 'csv') {
        return this.convertToCSV(data);
      }

      return data;
    } catch (error) {
      console.error('Error exporting master business case:', error);
      throw error;
    }
  }

  // Convert data to CSV format
  convertToCSV(data) {
    const headers = [
      'Program Name',
      'Program Manager',
      'Sponsor',
      'Status',
      'Business Driver',
      'Investment Category',
      'Total Budget',
      'Duration (Months)',
      'Use Cases Count',
      'Milestones Count',
      'Created Date'
    ];

    const row = [
      data.programInfo.programName,
      data.programInfo.programManager,
      data.programInfo.sponsor,
      data.status,
      data.programInfo.businessDriver,
      data.programInfo.investmentCategory,
      data.programInfo.totalBudget,
      data.programInfo.duration,
      data.useCases?.length || 0,
      data.milestones?.length || 0,
      new Date(data.createdAt).toLocaleDateString()
    ];

    return [headers.join(','), row.join(',')].join('\n');
  }

  // Validate master business case data
  validateData(data) {
    const errors = [];

    if (!data.programInfo?.programName?.trim()) {
      errors.push('Program name is required');
    }

    if (!data.programInfo?.programManager?.trim()) {
      errors.push('Program manager is required');
    }

    if (!data.programInfo?.sponsor?.trim()) {
      errors.push('Executive sponsor is required');
    }

    if (!data.programInfo?.totalBudget || data.programInfo.totalBudget <= 0) {
      errors.push('Total budget must be greater than 0');
    }

    if (!data.programInfo?.duration || data.programInfo.duration <= 0) {
      errors.push('Duration must be greater than 0');
    }

    if (!data.createdBy?.trim()) {
      errors.push('Created by is required');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Calculate financial metrics
  calculateMetrics(data) {
    const useCases = data.useCases || [];
    const milestones = data.milestones || [];

    const totalUseCasesBudget = useCases.reduce((sum, uc) => sum + (uc.budget || 0), 0);
    const averageROI = useCases.length > 0
      ? useCases.reduce((sum, uc) => sum + (uc.roi || 0), 0) / useCases.length
      : 0;

    const totalMilestonesBudget = milestones.reduce((sum, ms) => sum + (ms.budgetAllocated || 0), 0);
    const totalActualCosts = milestones.reduce((sum, ms) => sum + (ms.actualCost || 0), 0);
    const budgetVariance = totalMilestonesBudget - totalActualCosts;

    return {
      totalUseCasesBudget,
      averageROI,
      totalMilestonesBudget,
      totalActualCosts,
      budgetVariance,
      budgetVariancePercentage: totalMilestonesBudget > 0
        ? ((budgetVariance / totalMilestonesBudget) * 100).toFixed(2)
        : 0
    };
  }
}

// Create singleton instances
const masterBusinessCaseService = new MasterBusinessCaseService();

export { masterBusinessCaseService };
export default masterBusinessCaseService;
