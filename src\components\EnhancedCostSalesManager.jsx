import React, { useState, useEffect } from 'react'
import { v4 as uuidv4 } from 'uuid'

const EnhancedCostSalesManager = ({ data, onChange }) => {
  const [activeTab, setActiveTab] = useState('costs')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingItem, setEditingItem] = useState(null)
  
  // Data states
  const [costs, setCosts] = useState([])
  const [sales, setSales] = useState([])
  const [financialIndexes, setFinancialIndexes] = useState({
    totalCosts: 0,
    totalRevenue: 0,
    netProfit: 0,
    roi: 0,
    npv: 0,
    irr: 0,
    paybackPeriod: 0,
    profitabilityIndex: 0,
    breakEvenPoint: 0
  })
  
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    category: '',
    amount: 0,
    type: 'fixed',
    description: '',
    year: new Date().getFullYear(),
    frequency: 'annual',
    units: 0,
    unitPrice: 0,
    quarter: 'Q1'
  })

  // Load data on component mount
  useEffect(() => {
    loadCostSalesData()
  }, [])

  // Recalculate financial indexes when data changes
  useEffect(() => {
    calculateFinancialIndexes()
  }, [costs, sales])

  // API Functions
  const loadCostSalesData = async () => {
    setLoading(true)
    try {
      const [costsResponse, salesResponse] = await Promise.all([
        fetch('/api/costs'),
        fetch('/api/sales')
      ])
      
      if (costsResponse.ok && salesResponse.ok) {
        const costsData = await costsResponse.json()
        const salesData = await salesResponse.json()
        setCosts(costsData)
        setSales(salesData)
      }
    } catch (err) {
      console.error('Error loading data:', err)
      // Use mock data if API fails
      setCosts(getMockCosts())
      setSales(getMockSales())
    } finally {
      setLoading(false)
    }
  }

  const saveItem = async (item, type) => {
    setLoading(true)
    try {
      const url = editingItem ? `/api/${type}/${editingItem._id}` : `/api/${type}`
      const method = editingItem ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(item)
      })
      
      if (response.ok) {
        const savedItem = await response.json()
        
        if (type === 'costs') {
          if (editingItem) {
            setCosts(prev => prev.map(c => c._id === editingItem._id ? savedItem : c))
          } else {
            setCosts(prev => [...prev, savedItem])
          }
        } else {
          if (editingItem) {
            setSales(prev => prev.map(s => s._id === editingItem._id ? savedItem : s))
          } else {
            setSales(prev => [...prev, savedItem])
          }
        }
        
        resetForm()
        setShowAddForm(false)
        setEditingItem(null)
      } else {
        throw new Error('Failed to save item')
      }
    } catch (err) {
      setError(`Error saving ${type}: ${err.message}`)
      // Fallback to local state management
      const newItem = { ...item, _id: uuidv4(), createdAt: new Date().toISOString() }
      
      if (type === 'costs') {
        if (editingItem) {
          setCosts(prev => prev.map(c => c._id === editingItem._id ? newItem : c))
        } else {
          setCosts(prev => [...prev, newItem])
        }
      } else {
        if (editingItem) {
          setSales(prev => prev.map(s => s._id === editingItem._id ? newItem : s))
        } else {
          setSales(prev => [...prev, newItem])
        }
      }
      
      resetForm()
      setShowAddForm(false)
      setEditingItem(null)
    } finally {
      setLoading(false)
    }
  }

  const deleteItem = async (id, type) => {
    setLoading(true)
    try {
      const response = await fetch(`/api/${type}/${id}`, {
        method: 'DELETE'
      })
      
      if (response.ok) {
        if (type === 'costs') {
          setCosts(prev => prev.filter(c => c._id !== id))
        } else {
          setSales(prev => prev.filter(s => s._id !== id))
        }
      } else {
        throw new Error('Failed to delete item')
      }
    } catch (err) {
      setError(`Error deleting ${type}: ${err.message}`)
      // Fallback to local deletion
      if (type === 'costs') {
        setCosts(prev => prev.filter(c => c._id !== id))
      } else {
        setSales(prev => prev.filter(s => s._id !== id))
      }
    } finally {
      setLoading(false)
    }
  }

  // Financial calculations
  const calculateFinancialIndexes = () => {
    const totalCosts = costs.reduce((sum, cost) => sum + (cost.amount || 0), 0)
    const totalRevenue = sales.reduce((sum, sale) => sum + (sale.revenue || sale.amount || 0), 0)
    const netProfit = totalRevenue - totalCosts
    const roi = totalCosts > 0 ? ((netProfit / totalCosts) * 100) : 0
    
    // NPV calculation (simplified)
    const discountRate = 0.10 // 10%
    const years = 5
    const annualCashFlow = netProfit / years
    let npv = -totalCosts
    for (let year = 1; year <= years; year++) {
      npv += annualCashFlow / Math.pow(1 + discountRate, year)
    }
    
    // IRR calculation (simplified approximation)
    const irr = totalCosts > 0 ? ((Math.pow(totalRevenue / totalCosts, 1/years) - 1) * 100) : 0
    
    // Payback period
    const paybackPeriod = annualCashFlow > 0 ? totalCosts / annualCashFlow : 0
    
    // Profitability index
    const profitabilityIndex = totalCosts > 0 ? (npv + totalCosts) / totalCosts : 0
    
    // Break-even point
    const breakEvenPoint = totalCosts > 0 ? totalCosts / (totalRevenue > 0 ? totalRevenue / sales.length : 1) : 0

    const newIndexes = {
      totalCosts,
      totalRevenue,
      netProfit,
      roi: Math.round(roi * 100) / 100,
      npv: Math.round(npv),
      irr: Math.round(irr * 100) / 100,
      paybackPeriod: Math.round(paybackPeriod * 100) / 100,
      profitabilityIndex: Math.round(profitabilityIndex * 100) / 100,
      breakEvenPoint: Math.round(breakEvenPoint)
    }
    
    setFinancialIndexes(newIndexes)
    
    // Update parent component
    if (onChange) {
      onChange({
        costs,
        sales,
        financialIndexes: newIndexes
      })
    }
  }

  // Form handlers
  const resetForm = () => {
    setFormData({
      name: '',
      category: '',
      amount: 0,
      type: 'fixed',
      description: '',
      year: new Date().getFullYear(),
      frequency: 'annual',
      units: 0,
      unitPrice: 0,
      quarter: 'Q1'
    })
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    
    if (activeTab === 'costs') {
      const costItem = {
        ...formData,
        revenue: formData.amount // For consistency
      }
      saveItem(costItem, 'costs')
    } else {
      const salesItem = {
        ...formData,
        revenue: formData.units * formData.unitPrice,
        amount: formData.units * formData.unitPrice
      }
      saveItem(salesItem, 'sales')
    }
  }

  const startEdit = (item) => {
    setEditingItem(item)
    setFormData({
      name: item.name || '',
      category: item.category || '',
      amount: item.amount || 0,
      type: item.type || 'fixed',
      description: item.description || '',
      year: item.year || new Date().getFullYear(),
      frequency: item.frequency || 'annual',
      units: item.units || 0,
      unitPrice: item.unitPrice || 0,
      quarter: item.quarter || 'Q1'
    })
    setShowAddForm(true)
  }

  // Mock data functions
  const getMockCosts = () => [
    {
      _id: '1',
      name: 'Personnel Costs',
      category: 'Human Resources',
      amount: 500000,
      type: 'fixed',
      description: 'Annual salary and benefits',
      year: 2024,
      frequency: 'annual'
    },
    {
      _id: '2',
      name: 'Equipment Purchase',
      category: 'Capital Expenditure',
      amount: 300000,
      type: 'one-time',
      description: 'Manufacturing equipment',
      year: 2024,
      frequency: 'one-time'
    }
  ]

  const getMockSales = () => [
    {
      _id: '1',
      name: 'Product A Sales',
      category: 'Product Revenue',
      amount: 800000,
      revenue: 800000,
      units: 1000,
      unitPrice: 800,
      description: 'Premium product line',
      year: 2024,
      quarter: 'Q1'
    },
    {
      _id: '2',
      name: 'Service Revenue',
      category: 'Service Revenue',
      amount: 400000,
      revenue: 400000,
      units: 500,
      unitPrice: 800,
      description: 'Consulting and support services',
      year: 2024,
      quarter: 'Q2'
    }
  ]

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const tabs = [
    { id: 'costs', label: 'Cost Management', icon: '💰' },
    { id: 'sales', label: 'Sales Management', icon: '📈' },
    { id: 'analytics', label: 'Financial Analytics', icon: '📊' }
  ]

  return (
    <div className="enhanced-cost-sales-manager">
      <div className="manager-header">
        <h2>💼 Enhanced Cost & Sales Management</h2>
        <p>Full-stack CRUD operations with real-time financial calculations</p>
      </div>

      {error && (
        <div className="error-message">
          <span className="error-icon">⚠️</span>
          <span>{error}</span>
          <button onClick={() => setError(null)}>✕</button>
        </div>
      )}

      <div className="tab-navigation">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            className={`tab-btn ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
          >
            <span className="tab-icon">{tab.icon}</span>
            <span className="tab-label">{tab.label}</span>
          </button>
        ))}
      </div>

      <div className="tab-content">
        {/* Content will be added in next step */}
      </div>
    </div>
  )
}

export default EnhancedCostSalesManager
