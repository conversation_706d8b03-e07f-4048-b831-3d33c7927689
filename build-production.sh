#!/bin/bash

echo "🏗️ Building Master BC Framework - Governance Platform for Production"
echo "=================================================================="
echo ""

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "📦 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo ""
echo "🔧 Building frontend application..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Failed to build frontend application"
    exit 1
fi

echo ""
echo "📦 Installing backend dependencies..."
cd server
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install backend dependencies"
    exit 1
fi

cd ..

echo ""
echo "✅ Production build completed successfully!"
echo ""
echo "📁 Build artifacts:"
echo "   Frontend: ./dist/"
echo "   Backend: ./server/"
echo ""
echo "🚀 To deploy:"
echo "   1. Copy ./dist/ to your web server"
echo "   2. Copy ./server/ to your application server"
echo "   3. Set up MongoDB connection"
echo "   4. Configure environment variables"
echo "   5. Start the backend server: cd server && npm start"
echo ""
echo "🎯 Features included in this build:"
echo "   ✅ Master Business Case Management"
echo "   ✅ Enhanced PMI Guidelines & PGMP Framework"
echo "   ✅ Multi-Stakeholder Feedback System with CRUD"
echo "   ✅ Change Management & Roadmap"
echo "   ✅ Financial Modeling & Analysis"
echo "   ✅ Real-time MongoDB Integration"
echo "   ✅ Professional UI/UX Design"
echo "   ✅ Mobile-Responsive Layout"
echo ""
