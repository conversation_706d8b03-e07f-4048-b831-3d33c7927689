* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: #f5f7fa;
  color: #333;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header */
.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.header-content h1 {
  font-size: 1.8rem;
  font-weight: 600;
}

.project-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.project-name {
  font-weight: 500;
  font-size: 1rem;
}

.last-saved {
  font-size: 0.85rem;
  opacity: 0.8;
}

/* Navigation */
.tab-navigation {
  background: white;
  border-bottom: 1px solid #e1e5e9;
  padding: 0 2rem;
  display: flex;
  gap: 0;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.tab-button {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.95rem;
  color: #666;
  border-bottom: 3px solid transparent;
  transition: all 0.2s ease;
}

.tab-button:hover {
  background-color: #f8f9fa;
  color: #333;
}

.tab-button.active {
  color: #667eea;
  border-bottom-color: #667eea;
  background-color: #f8f9fa;
}

.tab-icon {
  font-size: 1.1rem;
}

/* Main Content */
.main-content {
  flex: 1;
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

/* Footer */
.app-footer {
  background: white;
  border-top: 1px solid #e1e5e9;
  padding: 1rem 2rem;
  box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
}

.footer-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* Buttons */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: #667eea;
  color: white;
}

.btn-primary:hover {
  background-color: #5a6fd8;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

.btn-success {
  background-color: #28a745;
  color: white;
}

.btn-success:hover {
  background-color: #218838;
}

/* Card Components */
.card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 1.5rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e1e5e9;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
}

/* Form Elements */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #555;
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.95rem;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

/* Component Specific Styles */
.section-navigation {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.section-btn {
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 0.75rem 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.section-btn:hover {
  background-color: #f8f9fa;
  border-color: #667eea;
}

.section-btn.active {
  background-color: #667eea;
  color: white;
  border-color: #667eea;
}

.section-icon {
  font-size: 1.1rem;
}

.cost-section {
  padding: 1rem 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.cost-items {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.cost-item {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.toggle-group {
  margin-bottom: 1rem;
}

.toggle-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.toggle-text {
  font-size: 0.95rem;
}

.sensitivity-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.variable-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
}

.variable-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.variable-name {
  font-weight: 600;
  color: #333;
}

.range-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.scenario-results {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-top: 1rem;
}

.scenario-card {
  text-align: center;
  padding: 1rem;
  border-radius: 6px;
}

.scenario-best {
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
}

.scenario-base {
  background-color: #e2e3e5;
  border: 1px solid #d6d8db;
}

.scenario-worst {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
}

.financial-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.metric-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.metric-value {
  font-size: 2rem;
  font-weight: bold;
  margin: 0.5rem 0;
}

.metric-positive {
  color: #28a745;
}

.metric-negative {
  color: #dc3545;
}

.metric-neutral {
  color: #6c757d;
}

/* Responsive */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .tab-navigation {
    overflow-x: auto;
    padding: 0 1rem;
  }

  .main-content {
    padding: 1rem;
  }

  .footer-actions {
    flex-direction: column;
  }

  .section-navigation {
    justify-content: center;
  }

  .range-inputs {
    grid-template-columns: 1fr;
  }

  .scenario-results {
    grid-template-columns: 1fr;
  }
}

/* Export Dropdown */
.export-dropdown {
  position: relative;
  display: inline-block;
}

.export-menu {
  display: none;
  position: absolute;
  bottom: 100%;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  min-width: 150px;
  z-index: 1000;
}

.export-dropdown:hover .export-menu {
  display: block;
}

.export-menu button {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s ease;
}

.export-menu button:hover {
  background-color: #f8f9fa;
}

.export-menu button:first-child {
  border-radius: 6px 6px 0 0;
}

.export-menu button:last-child {
  border-radius: 0 0 6px 6px;
}

/* Chart Container */
.chart-container {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.chart-container h3 {
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.1rem;
}

.visualizations-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Loading States */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.calculating-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.calculating-spinner {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  text-align: center;
}

/* Enhanced Metrics */
.metric-comparison {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.trend-up {
  color: #28a745;
}

.trend-down {
  color: #dc3545;
}

.trend-neutral {
  color: #6c757d;
}

/* Advanced Form Elements */
.form-section {
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e1e5e9;
}

.input-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.input-addon {
  background: #f8f9fa;
  border: 1px solid #ddd;
  padding: 0.75rem;
  border-radius: 4px 0 0 4px;
  font-size: 0.9rem;
  color: #666;
}

.input-group .form-input {
  border-radius: 0 4px 4px 0;
  border-left: none;
}

/* Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
}

.status-success {
  background: #d4edda;
  color: #155724;
}

.status-warning {
  background: #fff3cd;
  color: #856404;
}

.status-danger {
  background: #f8d7da;
  color: #721c24;
}

.status-info {
  background: #d1ecf1;
  color: #0c5460;
}

/* Master Business Case Manager Styles */
.master-business-case-manager {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e1e5e9;
}

.manager-header h1 {
  margin: 0;
  color: #333;
  font-size: 2rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #f5c6cb;
}

.error-message button {
  background: none;
  border: none;
  color: #721c24;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  align-items: center;
}

.search-box {
  flex: 1;
  min-width: 300px;
}

.filters {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.filters select {
  min-width: 180px;
}

.business-cases-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.business-case-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.business-case-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  transform: translateY(-2px);
}

.business-case-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e1e5e9;
}

.business-case-card .card-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
  line-height: 1.3;
  flex: 1;
  margin-right: 1rem;
}

.status-badge {
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  white-space: nowrap;
}

.card-content {
  margin-bottom: 1.5rem;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.info-row .label {
  font-weight: 500;
  color: #666;
  min-width: 120px;
}

.info-row span:last-child {
  color: #333;
  text-align: right;
  flex: 1;
}

.card-actions-footer {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid #e1e5e9;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.8rem;
}

.status-select {
  padding: 0.375rem 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.8rem;
  background: white;
  cursor: pointer;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
  padding: 1rem;
}

.pagination-info {
  font-size: 0.9rem;
  color: #666;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e1e5e9;
}

.modal-header h2 {
  margin: 0;
  color: #333;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.modal-close:hover {
  background: #f8f9fa;
  color: #333;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e1e5e9;
  background: #f8f9fa;
}

.form-section {
  margin-bottom: 2rem;
}

.form-section h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e1e5e9;
}

.form-section:last-child {
  margin-bottom: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .master-business-case-manager {
    padding: 1rem;
  }

  .manager-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .search-filters {
    flex-direction: column;
  }

  .search-box {
    min-width: auto;
  }

  .filters {
    justify-content: stretch;
  }

  .filters select {
    min-width: auto;
    flex: 1;
  }

  .business-cases-grid {
    grid-template-columns: 1fr;
  }

  .card-actions-footer {
    flex-direction: column;
    align-items: stretch;
  }

  .card-actions-footer .btn-sm,
  .card-actions-footer .status-select {
    width: 100%;
    text-align: center;
  }

  .modal-content {
    margin: 0;
    border-radius: 0;
    height: 100vh;
    max-height: 100vh;
  }

  .pagination {
    flex-direction: column;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .business-case-card .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .business-case-card .card-header h3 {
    margin-right: 0;
  }

  .info-row {
    flex-direction: column;
    gap: 0.25rem;
  }

  .info-row .label {
    min-width: auto;
  }

  .info-row span:last-child {
    text-align: left;
  }
}

/* Change Management & Roadmap Styles */
.change-management-roadmap {
  padding: 1rem;
}

.roadmap-navigation {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.roadmap-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: 2px solid #e1e5e9;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
  color: #333;
}

.roadmap-btn:hover {
  border-color: #667eea;
  background: #f8f9ff;
}

.roadmap-btn.active {
  border-color: #667eea;
  background: #667eea;
  color: white;
}

.roadmap-btn span:first-child {
  font-size: 1.2rem;
}

.roadmap-section {
  margin-bottom: 2rem;
}

.roadmap-section h3 {
  margin-bottom: 1.5rem;
  color: #333;
  font-size: 1.5rem;
}

.governance-table {
  margin-bottom: 2rem;
  overflow-x: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.data-table th {
  background: #667eea;
  color: white;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
}

.data-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e1e5e9;
}

.data-table tbody tr:hover {
  background: #f8f9fa;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  white-space: nowrap;
}

.status-approved {
  background: #28a745;
  color: white;
}

.current-state-analysis,
.proposed-benefits,
.transformation-benefits {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  margin-top: 1.5rem;
}

.current-state-analysis h4,
.proposed-benefits h4,
.transformation-benefits h4 {
  margin: 0 0 1rem 0;
  color: #333;
}

.current-state-analysis ul,
.proposed-benefits ul,
.transformation-benefits ul {
  margin: 0;
  padding-left: 1.5rem;
}

.current-state-analysis li,
.proposed-benefits li,
.transformation-benefits li {
  margin-bottom: 0.5rem;
  color: #555;
}

.implementation-timeline {
  background: #e8f4fd;
  padding: 1.5rem;
  border-radius: 8px;
  margin-top: 1.5rem;
}

.implementation-timeline h4 {
  margin: 0 0 1rem 0;
  color: #333;
}

.timeline-item {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.75rem;
  align-items: flex-start;
}

.timeline-phase {
  font-weight: 600;
  color: #667eea;
  min-width: 140px;
}

.sequential-approach {
  margin: 2rem 0;
}

.sequential-approach h4 {
  margin-bottom: 1.5rem;
  color: #333;
}

.sequence-step {
  margin-bottom: 2rem;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  overflow: hidden;
}

.step-header {
  background: #667eea;
  color: white;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.step-number {
  background: rgba(255,255,255,0.2);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
}

.step-header h5 {
  margin: 0;
  font-size: 1.1rem;
}

.key-principle {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 2rem 0;
}

.key-principle h4 {
  margin: 0 0 1rem 0;
  color: #856404;
}

.principle-box {
  background: white;
  padding: 1rem;
  border-radius: 6px;
  border-left: 4px solid #ffc107;
}

.principle-box strong {
  display: block;
  margin-bottom: 0.5rem;
  color: #856404;
}

.principle-box p {
  margin: 0;
  color: #6c757d;
}

.recommendations {
  display: grid;
  gap: 1.5rem;
}

.recommendation-item {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.recommendation-item h4 {
  margin: 0 0 1rem 0;
  color: #333;
}

.recommendation-item ul {
  margin: 0;
  padding-left: 1.5rem;
}

.recommendation-item li {
  margin-bottom: 0.5rem;
  color: #555;
}

/* Responsive Design for Roadmap */
@media (max-width: 768px) {
  .roadmap-navigation {
    flex-direction: column;
  }

  .roadmap-btn {
    justify-content: center;
  }

  .governance-table {
    font-size: 0.9rem;
  }

  .data-table th,
  .data-table td {
    padding: 0.5rem;
  }

  .timeline-item {
    flex-direction: column;
    gap: 0.5rem;
  }

  .timeline-phase {
    min-width: auto;
  }

  .step-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* Enhanced PMI Compliance Styles */
.enhanced-pmi-compliance {
  padding: 1rem;
}

.compliance-header {
  margin-bottom: 2rem;
}

.compliance-header h2 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.compliance-info {
  display: flex;
  gap: 2rem;
  font-size: 0.9rem;
  color: #666;
}

.compliance-navigation {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.compliance-nav-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 2px solid #e1e5e9;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  color: #333;
}

.compliance-nav-btn:hover {
  border-color: #667eea;
  background: #f8f9ff;
}

.compliance-nav-btn.active {
  border-color: #667eea;
  background: #667eea;
  color: white;
}

.nav-icon {
  font-size: 1.1rem;
}

/* Overview Styles */
.compliance-overview {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.overview-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.metric-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.metric-icon {
  font-size: 2rem;
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.25rem;
}

.metric-label {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.25rem;
}

.metric-status {
  font-size: 0.8rem;
  font-weight: 500;
}

.compliance-summary h4 {
  margin: 0 0 1.5rem 0;
  color: #333;
}

.knowledge-areas-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.knowledge-area-summary {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1rem;
}

.area-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.area-name {
  font-weight: 600;
  color: #333;
}

.area-score {
  font-weight: 700;
  font-size: 1.1rem;
}

.compliance-bar {
  height: 8px;
  background: #e1e5e9;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.compliance-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.area-status {
  font-size: 0.8rem;
  font-weight: 500;
  text-align: center;
}

/* Lifecycle Styles */
.lifecycle-view h3 {
  margin: 0 0 2rem 0;
  color: #333;
}

.lifecycle-phases {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.phase-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
  border-left: 4px solid #667eea;
}

.phase-card.completed {
  border-left-color: #28a745;
}

.phase-card.in-progress {
  border-left-color: #17a2b8;
}

.phase-card.not-started {
  border-left-color: #6c757d;
}

.phase-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.phase-info h4 {
  margin: 0 0 0.25rem 0;
  color: #333;
}

.phase-dates {
  font-size: 0.9rem;
  color: #666;
}

.phase-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  color: white;
  font-size: 0.8rem;
  font-weight: 500;
}

.compliance-score {
  font-weight: 700;
  font-size: 1.1rem;
}

.phase-progress {
  margin-bottom: 1.5rem;
}

.progress-bar {
  height: 10px;
  background: #e1e5e9;
  border-radius: 5px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.9rem;
  color: #666;
}

.phase-processes h5,
.phase-gates h5 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1rem;
}

.processes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.process-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 0.9rem;
}

.process-name {
  font-weight: 500;
}

.process-status {
  font-size: 0.8rem;
  font-weight: 500;
}

.gate-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.gate-name {
  font-weight: 500;
}

.gate-status,
.gate-date,
.gate-approver {
  font-size: 0.9rem;
}

/* Knowledge Areas Detailed Styles */
.knowledge-areas-view h3 {
  margin: 0 0 2rem 0;
  color: #333;
}

.knowledge-areas-detailed {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.knowledge-area-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
}

.area-header-detailed {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e1e5e9;
}

.area-header-detailed h4 {
  margin: 0;
  color: #333;
}

.area-metrics {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.process-groups-summary h5 {
  margin: 0 0 1rem 0;
  color: #333;
}

.process-groups-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.process-group-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 6px;
  text-align: center;
}

.group-name {
  font-weight: 500;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.group-progress {
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 0.25rem;
}

.group-compliance {
  font-weight: 600;
  font-size: 0.9rem;
}

.artifacts-list h5,
.issues-section h5,
.recommendations-section h5 {
  margin: 0 0 1rem 0;
  color: #333;
}

.artifacts-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.artifact-tag {
  padding: 0.25rem 0.75rem;
  background: #e3f2fd;
  color: #1976d2;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.issues-list,
.recommendations-list {
  margin: 0 0 1.5rem 0;
  padding-left: 1.5rem;
}

.issue-item {
  color: #dc3545;
  margin-bottom: 0.5rem;
}

.recommendation-item {
  color: #28a745;
  margin-bottom: 0.5rem;
}

/* PGMP Framework Styles */
.pgmp-framework-view h3 {
  margin: 0 0 2rem 0;
  color: #333;
}

.pgmp-domains {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.domain-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
}

.domain-card h4 {
  margin: 0 0 1.5rem 0;
  color: #333;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #e1e5e9;
}

.strategy-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.strategy-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 6px;
}

.strategy-item .score {
  font-weight: 700;
  color: #667eea;
}

.strategy-item .status {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.strategy-item .status.good {
  background: #d4edda;
  color: #155724;
}

.lifecycle-phases-pgmp {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.pgmp-phase {
  padding: 1rem;
  border-radius: 6px;
  border-left: 4px solid #667eea;
}

.pgmp-phase.completed {
  background: #d4edda;
  border-left-color: #28a745;
}

.pgmp-phase.in-progress {
  background: #d1ecf1;
  border-left-color: #17a2b8;
}

.pgmp-phase.not-started {
  background: #f8f9fa;
  border-left-color: #6c757d;
}

.phase-header-pgmp {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.phase-name {
  font-weight: 600;
  color: #333;
}

.phase-score {
  font-weight: 700;
  color: #667eea;
}

.phase-status {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.75rem;
}

.phase-artifacts {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.phase-artifacts span {
  padding: 0.25rem 0.5rem;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border-radius: 12px;
  font-size: 0.8rem;
}

.supporting-activities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.activity-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 6px;
}

.activity-name {
  font-weight: 500;
}

.activity-score {
  font-weight: 700;
  color: #667eea;
}

.activity-status {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.activity-status.excellent {
  background: #d4edda;
  color: #155724;
}

.activity-status.good {
  background: #d1ecf1;
  color: #0c5460;
}

.activity-status.needs-attention {
  background: #fff3cd;
  color: #856404;
}

/* Artifacts Styles */
.artifacts-view h3 {
  margin: 0 0 2rem 0;
  color: #333;
}

.artifacts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.artifact-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1rem;
  border-left: 4px solid #667eea;
}

.artifact-card.approved {
  border-left-color: #28a745;
}

.artifact-card.in-review {
  border-left-color: #ffc107;
}

.artifact-card.in-progress {
  border-left-color: #17a2b8;
}

.artifact-card.draft {
  border-left-color: #6c757d;
}

.artifact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.artifact-name {
  font-weight: 600;
  color: #333;
}

.artifact-status {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  color: white;
}

.artifact-card.approved .artifact-status {
  background: #28a745;
}

.artifact-card.in-review .artifact-status {
  background: #ffc107;
  color: #333;
}

.artifact-card.in-progress .artifact-status {
  background: #17a2b8;
}

.artifact-card.draft .artifact-status {
  background: #6c757d;
}

.artifact-details {
  border-top: 1px solid #e1e5e9;
  padding-top: 0.75rem;
}

.artifact-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.artifact-info span {
  font-size: 0.9rem;
  color: #666;
}

/* Assessment Styles */
.assessment-view h3 {
  margin: 0 0 2rem 0;
  color: #333;
}

.assessment-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.summary-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
  border-left: 4px solid #667eea;
}

.summary-card.critical {
  border-left-color: #dc3545;
}

.summary-card.warning {
  border-left-color: #ffc107;
}

.summary-card.success {
  border-left-color: #28a745;
}

.summary-card h4 {
  margin: 0 0 1rem 0;
  color: #333;
}

.summary-card ul {
  margin: 0;
  padding-left: 1.5rem;
}

.summary-card li {
  margin-bottom: 0.5rem;
  color: #555;
}

.action-items h4 {
  margin: 0 0 1.5rem 0;
  color: #333;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1rem;
}

.action-item {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1rem;
  border-left: 4px solid #667eea;
}

.action-item.high-priority {
  border-left-color: #dc3545;
}

.action-item.medium-priority {
  border-left-color: #ffc107;
}

.action-item.low-priority {
  border-left-color: #28a745;
}

.action-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.action-title {
  font-weight: 600;
  color: #333;
}

.action-priority {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.action-item.high-priority .action-priority {
  background: #f8d7da;
  color: #721c24;
}

.action-item.medium-priority .action-priority {
  background: #fff3cd;
  color: #856404;
}

.action-item.low-priority .action-priority {
  background: #d4edda;
  color: #155724;
}

.action-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.action-details span {
  font-size: 0.9rem;
  color: #666;
}

/* Responsive Design for Enhanced PMI Compliance */
@media (max-width: 768px) {
  .compliance-navigation {
    flex-direction: column;
  }

  .overview-metrics {
    grid-template-columns: 1fr;
  }

  .knowledge-areas-grid {
    grid-template-columns: 1fr;
  }

  .strategy-metrics,
  .supporting-activities-grid,
  .artifacts-grid,
  .assessment-summary,
  .actions-grid {
    grid-template-columns: 1fr;
  }

  .process-groups-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .phase-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .area-header-detailed {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .action-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* Enhanced Stakeholder Feedback Styles */
.enhanced-stakeholder-feedback {
  padding: 1rem;
}

.feedback-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.feedback-header h2 {
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.feedback-summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.summary-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.summary-icon {
  font-size: 2rem;
}

.summary-content {
  flex: 1;
}

.summary-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.25rem;
}

.summary-label {
  font-size: 0.9rem;
  color: #666;
}

.feedback-navigation {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 2px solid #e1e5e9;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  color: #333;
}

.nav-btn:hover {
  border-color: #667eea;
  background: #f8f9ff;
}

.nav-btn.active {
  border-color: #667eea;
  background: #667eea;
  color: white;
}

.nav-icon {
  font-size: 1.1rem;
}

/* Table View Styles */
.table-view {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  overflow: hidden;
}

.table-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e5e9;
  gap: 1rem;
  flex-wrap: wrap;
}

.filter-controls,
.sort-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-controls label,
.sort-controls label {
  font-weight: 500;
  color: #333;
}

.form-select {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
}

.stakeholder-table-container {
  overflow-x: auto;
}

.stakeholder-table {
  width: 100%;
  border-collapse: collapse;
}

.stakeholder-table th {
  background: #667eea;
  color: white;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  white-space: nowrap;
}

.stakeholder-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e1e5e9;
  vertical-align: top;
}

.stakeholder-table tbody tr:hover {
  background: #f8f9fa;
}

.stakeholder-name {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stakeholder-name strong {
  color: #333;
}

.stakeholder-email {
  font-size: 0.8rem;
  color: #666;
}

.status-badge,
.priority-badge,
.engagement-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  color: white;
  font-size: 0.8rem;
  font-weight: 500;
  white-space: nowrap;
}

.rating-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.rating-value {
  font-weight: 700;
  color: #333;
}

.rating-stars {
  color: #ffc107;
  font-size: 0.9rem;
}

.action-buttons {
  display: flex;
  gap: 0.25rem;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover {
  background: #5a6fd8;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
}

.btn-warning {
  background: #ffc107;
  color: #333;
}

.btn-warning:hover {
  background: #e0a800;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover {
  background: #c82333;
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-info:hover {
  background: #138496;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
}

.btn-close {
  background: transparent;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
}

.btn-close:hover {
  color: #333;
}

/* Details View Styles */
.details-view {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  overflow: hidden;
}

.stakeholder-details-card {
  padding: 2rem;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e1e5e9;
}

.details-header h3 {
  margin: 0;
  color: #333;
}

.header-badges {
  display: flex;
  gap: 0.5rem;
}

.details-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.details-section {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
}

.details-section h4 {
  margin: 0 0 1rem 0;
  color: #333;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.info-item label {
  font-weight: 600;
  color: #666;
  font-size: 0.9rem;
}

.info-item span {
  color: #333;
}

.feedback-content {
  display: flex;
  gap: 2rem;
  align-items: flex-start;
}

.rating-display-large {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  min-width: 120px;
}

.rating-value-large {
  font-size: 3rem;
  font-weight: 700;
  color: #667eea;
}

.rating-stars-large {
  color: #ffc107;
  font-size: 1.5rem;
}

.feedback-text {
  flex: 1;
}

.feedback-text p {
  margin: 0;
  color: #333;
  line-height: 1.6;
}

/* Comments Section Styles */
.comments-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.add-comment {
  background: white;
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid #e1e5e9;
}

.comment-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-row {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-textarea {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  resize: vertical;
  font-family: inherit;
}

.comments-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.comment-item {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  padding: 1rem;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.comment-meta {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.comment-author {
  font-weight: 600;
  color: #333;
}

.comment-date {
  font-size: 0.9rem;
  color: #666;
}

.comment-type {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  color: white;
  font-size: 0.8rem;
  font-weight: 500;
}

.comment-content {
  color: #333;
  line-height: 1.6;
}

/* Analytics View Styles */
.analytics-view {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 2rem;
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.analytics-card {
  background: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
}

.analytics-card h4 {
  margin: 0 0 1.5rem 0;
  color: #333;
}

.status-chart,
.engagement-chart,
.rating-chart,
.priority-chart {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.status-bar,
.engagement-bar,
.rating-bar,
.priority-bar {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.status-label,
.engagement-label,
.rating-label,
.priority-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  color: #333;
}

.progress-bar {
  height: 8px;
  background: #e1e5e9;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  transition: width 0.3s ease;
}

/* Comments View Styles */
.comments-view {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 2rem;
}

.comments-view h3 {
  margin: 0 0 2rem 0;
  color: #333;
}

.all-comments {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.stakeholder-comments {
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  overflow: hidden;
}

.stakeholder-header {
  background: #f8f9fa;
  padding: 1rem;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stakeholder-header h4 {
  margin: 0;
  color: #333;
}

.comment-count {
  font-size: 0.9rem;
  color: #666;
  background: #e1e5e9;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
}

.no-comments {
  padding: 1rem;
  color: #666;
  font-style: italic;
  text-align: center;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 8px;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e1e5e9;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e1e5e9;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-weight: 600;
  color: #333;
}

.form-input {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* Responsive Design for Enhanced Stakeholder Feedback */
@media (max-width: 768px) {
  .feedback-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .feedback-summary-cards {
    grid-template-columns: 1fr;
  }

  .feedback-navigation {
    flex-direction: column;
  }

  .table-controls {
    flex-direction: column;
    align-items: flex-start;
  }

  .stakeholder-table {
    font-size: 0.9rem;
  }

  .stakeholder-table th,
  .stakeholder-table td {
    padding: 0.5rem;
  }

  .feedback-content {
    flex-direction: column;
    gap: 1rem;
  }

  .analytics-grid {
    grid-template-columns: 1fr;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .modal-content {
    margin: 0;
    max-height: 100vh;
    border-radius: 0;
  }

  .comment-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .details-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}

/* Advanced Financial Modeling Styles */
.advanced-financial-modeling {
  padding: 1rem;
}

.modeling-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e1e5e9;
}

.modeling-header h2 {
  margin: 0;
  color: #333;
}

.calculation-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.calculating {
  color: #17a2b8;
  font-weight: 500;
  animation: pulse 1.5s infinite;
}

.updated {
  color: #28a745;
  font-weight: 500;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.modeling-navigation {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.modeling-navigation .nav-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 2px solid #e1e5e9;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  color: #333;
}

.modeling-navigation .nav-btn:hover {
  border-color: #667eea;
  background: #f8f9ff;
}

.modeling-navigation .nav-btn.active {
  border-color: #667eea;
  background: #667eea;
  color: white;
}

.modeling-content {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 2rem;
}

/* Parameters View Styles */
.parameters-view h3 {
  margin: 0 0 2rem 0;
  color: #333;
}

.parameters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.parameter-section {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.parameter-section h4 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.1rem;
}

.parameter-group {
  margin-bottom: 1rem;
}

.parameter-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #333;
}

.parameter-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.parameter-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* Cost & Sales View Styles */
.cost-sales-view {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.view-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.view-header h3 {
  margin: 0;
  color: #333;
}

.products-table {
  overflow-x: auto;
}

.financial-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.financial-table th {
  background: #667eea;
  color: white;
  padding: 1rem 0.5rem;
  text-align: center;
  font-weight: 600;
  font-size: 0.9rem;
  white-space: nowrap;
}

.financial-table td {
  padding: 0.75rem 0.5rem;
  border-bottom: 1px solid #e1e5e9;
  text-align: center;
}

.financial-table tbody tr:hover {
  background: #f8f9fa;
}

.table-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  text-align: center;
  font-size: 0.9rem;
}

.table-input:focus {
  outline: none;
  border-color: #667eea;
}

.calculated-cell {
  background: #e8f4fd !important;
  font-weight: 600;
  color: #1976d2;
}

.totals-row {
  background: #f8f9fa;
  font-weight: 600;
}

.total-cell {
  background: #667eea !important;
  color: white !important;
  font-weight: 700;
}

/* Sensitivity Analysis Styles */
.sensitivity-view {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.scenarios-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.scenario-card {
  background: #f8f9fa;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.scenario-card:hover {
  border-color: #667eea;
  background: #f0f4ff;
}

.scenario-card.active {
  border-color: #667eea;
  background: #667eea;
  color: white;
}

.scenario-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.scenario-name {
  font-weight: 700;
  font-size: 1.1rem;
}

.scenario-probability {
  background: rgba(255,255,255,0.2);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.scenario-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.scenario-metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Financial Indexes Styles */
.indexes-view {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.indexes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.index-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.index-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.index-value {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.index-label {
  font-size: 1rem;
  opacity: 0.9;
  margin-bottom: 0.5rem;
}

.index-description {
  font-size: 0.8rem;
  opacity: 0.8;
}

.index-card.positive {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.index-card.negative {
  background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
}

.index-card.neutral {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

/* Cash Flow Analysis Styles */
.cashflow-view {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.cashflow-table {
  overflow-x: auto;
}

.cashflow-table table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.cashflow-table th {
  background: #667eea;
  color: white;
  padding: 1rem;
  text-align: center;
  font-weight: 600;
}

.cashflow-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e1e5e9;
  text-align: right;
}

.cashflow-table .row-label {
  text-align: left;
  font-weight: 600;
  background: #f8f9fa;
}

.cashflow-table .positive {
  color: #28a745;
  font-weight: 600;
}

.cashflow-table .negative {
  color: #dc3545;
  font-weight: 600;
}

/* Audit Trail Styles */
.audit-view {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.audit-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.audit-metric {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  text-align: center;
  border-left: 4px solid #667eea;
}

.audit-metric-value {
  font-size: 2rem;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 0.5rem;
}

.audit-metric-label {
  color: #666;
  font-size: 0.9rem;
}

.audit-trail-list {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  overflow: hidden;
}

.audit-item {
  padding: 1rem;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.audit-item:last-child {
  border-bottom: none;
}

.audit-item:hover {
  background: #f8f9fa;
}

.audit-details {
  flex: 1;
}

.audit-action {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
}

.audit-field {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.audit-values {
  display: flex;
  gap: 1rem;
  font-size: 0.8rem;
}

.audit-old-value {
  color: #dc3545;
}

.audit-new-value {
  color: #28a745;
}

.audit-meta {
  text-align: right;
  color: #666;
  font-size: 0.8rem;
}

/* Responsive Design for Financial Modeling */
@media (max-width: 768px) {
  .modeling-navigation {
    flex-direction: column;
  }

  .parameters-grid {
    grid-template-columns: 1fr;
  }

  .scenarios-grid,
  .indexes-grid {
    grid-template-columns: 1fr;
  }

  .financial-table,
  .cashflow-table table {
    font-size: 0.8rem;
  }

  .financial-table th,
  .financial-table td,
  .cashflow-table th,
  .cashflow-table td {
    padding: 0.5rem 0.25rem;
  }

  .table-input {
    font-size: 0.8rem;
    padding: 0.25rem;
  }

  .index-card {
    padding: 1.5rem;
  }

  .index-value {
    font-size: 2rem;
  }

  .audit-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .audit-values {
    flex-direction: column;
    gap: 0.25rem;
  }
}

/* Augment Excel Analysis Styles */
.augment-excel-analysis {
  padding: 1rem;
}

.analysis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e1e5e9;
}

.analysis-header h2 {
  margin: 0;
  color: #333;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.analysis-navigation {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.analysis-navigation .nav-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 2px solid #e1e5e9;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  color: #333;
}

.analysis-navigation .nav-btn:hover {
  border-color: #667eea;
  background: #f8f9ff;
}

.analysis-navigation .nav-btn.active {
  border-color: #667eea;
  background: #667eea;
  color: white;
}

.analysis-content {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 2rem;
}

/* Import View Styles */
.import-view {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.import-section h3 {
  margin: 0 0 1rem 0;
  color: #333;
}

.upload-area {
  margin: 2rem 0;
}

.upload-dropzone {
  border: 2px dashed #667eea;
  border-radius: 8px;
  padding: 3rem 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #f8f9ff;
}

.upload-dropzone:hover {
  border-color: #5a67d8;
  background: #f0f4ff;
}

.upload-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.upload-text strong {
  display: block;
  font-size: 1.2rem;
  color: #333;
  margin-bottom: 0.5rem;
}

.upload-text p {
  color: #666;
  margin: 0;
}

.requirements-section {
  margin-top: 2rem;
}

.requirements-section h4 {
  margin: 1.5rem 0 1rem 0;
  color: #333;
}

.requirements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.requirement-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border-radius: 6px;
  background: #f8f9fa;
}

.requirement-item.required {
  border-left: 4px solid #28a745;
}

.requirement-item.optional {
  border-left: 4px solid #ffc107;
}

.requirement-icon {
  font-size: 1.2rem;
}

.requirement-name {
  font-weight: 600;
  color: #333;
}

.requirement-type {
  font-size: 0.8rem;
  color: #666;
  margin-left: auto;
}

.validation-errors {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 6px;
  padding: 1rem;
  margin-top: 1rem;
}

.validation-errors h4 {
  margin: 0 0 1rem 0;
  color: #721c24;
}

.error-item {
  color: #721c24;
  margin-bottom: 0.5rem;
  padding: 0.5rem;
  background: rgba(220, 53, 69, 0.1);
  border-radius: 4px;
}

.import-success {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 6px;
  padding: 1rem;
  margin-top: 1rem;
}

.import-success h4 {
  margin: 0 0 1rem 0;
  color: #155724;
}

.import-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.detail-item {
  color: #155724;
}

.detail-item strong {
  display: block;
  margin-bottom: 0.25rem;
}

/* Data View Styles */
.data-view {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.data-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.data-header h3 {
  margin: 0;
  color: #333;
}

.data-actions {
  display: flex;
  gap: 1rem;
}

.data-table-container {
  overflow-x: auto;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  min-width: 800px;
}

.data-table th {
  background: #667eea;
  color: white;
  padding: 1rem 0.5rem;
  text-align: center;
  font-weight: 600;
  font-size: 0.9rem;
  white-space: nowrap;
}

.data-table td {
  padding: 0.75rem 0.5rem;
  border-bottom: 1px solid #e1e5e9;
  text-align: center;
}

.data-table tbody tr:hover {
  background: #f8f9fa;
}

.data-table tbody tr.selected {
  background: #e3f2fd;
}

.table-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  text-align: center;
  font-size: 0.9rem;
}

.table-input:focus {
  outline: none;
  border-color: #667eea;
}

.table-input.currency {
  text-align: right;
}

.calculated-cell {
  background: #e8f4fd !important;
  font-weight: 600;
  color: #1976d2;
}

.calculated-cell.positive {
  color: #28a745;
}

.calculated-cell.negative {
  color: #dc3545;
}

.no-data {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;
}

.no-data-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.no-data h4 {
  margin: 0 0 1rem 0;
  color: #333;
}

/* Analysis View Styles */
.analysis-view {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.analysis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.analysis-header h3 {
  margin: 0;
  color: #333;
}

.analyzing-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #667eea;
  font-weight: 500;
}

.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.analysis-controls {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.control-group label {
  font-weight: 600;
  color: #333;
}

.control-input {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.control-input:focus {
  outline: none;
  border-color: #667eea;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.metric-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  transition: transform 0.2s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
}

.metric-card.irr {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.metric-card.npv {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.metric-card.payback {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

.metric-card.roi {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
}

.metric-card.breakeven {
  background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
}

.metric-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.metric-value {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.metric-label {
  font-size: 1rem;
  opacity: 0.9;
  margin-bottom: 0.5rem;
}

.metric-description {
  font-size: 0.8rem;
  opacity: 0.8;
}

/* Cash Flow Chart Styles */
.cash-flow-chart {
  margin-top: 2rem;
}

.cash-flow-chart h4 {
  margin: 0 0 1.5rem 0;
  color: #333;
}

.chart-container {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 2rem;
}

.chart-bars {
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  height: 200px;
  margin-bottom: 1rem;
  padding: 1rem 0;
  border-bottom: 2px solid #e1e5e9;
}

.chart-bar-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.chart-year {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.chart-bars-container {
  display: flex;
  gap: 2px;
  align-items: flex-end;
}

.chart-bar {
  width: 20px;
  min-height: 5px;
  border-radius: 2px 2px 0 0;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.chart-bar:hover {
  opacity: 0.8;
}

.chart-bar.revenue {
  background: #28a745;
}

.chart-bar.cost {
  background: #dc3545;
}

.chart-bar.cashflow.positive {
  background: #17a2b8;
}

.chart-bar.cashflow.negative {
  background: #ffc107;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 2px;
}

.legend-color.revenue {
  background: #28a745;
}

.legend-color.cost {
  background: #dc3545;
}

.legend-color.cashflow {
  background: #17a2b8;
}

/* Business Case Framework Styles */
.business-case-framework {
  padding: 1rem;
}

.framework-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e1e5e9;
}

.framework-header h2 {
  margin: 0;
  color: #333;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.framework-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.current-step {
  font-weight: 600;
  color: #667eea;
}

.progress-bar {
  width: 200px;
  height: 8px;
  background: #e1e5e9;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
}

.framework-navigation {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.framework-navigation .nav-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 2px solid #e1e5e9;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  color: #333;
}

.framework-navigation .nav-btn:hover {
  border-color: #667eea;
  background: #f8f9ff;
}

.framework-navigation .nav-btn.active {
  border-color: #667eea;
  background: #667eea;
  color: white;
}

.framework-content {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 2rem;
}

/* Framework Overview Styles */
.framework-overview {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.overview-header h2 {
  margin: 0 0 1rem 0;
  color: #333;
}

.overview-description {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.framework-benefits h3 {
  margin: 0 0 2rem 0;
  color: #333;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.benefit-card {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 8px;
  text-align: center;
  border-left: 4px solid #667eea;
  transition: transform 0.2s ease;
}

.benefit-card:hover {
  transform: translateY(-2px);
}

.benefit-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.benefit-card h4 {
  margin: 0 0 1rem 0;
  color: #333;
}

.benefit-card p {
  color: #666;
  margin: 0;
  line-height: 1.5;
}

.process-flow h3 {
  margin: 0 0 2rem 0;
  color: #333;
}

.steps-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
  align-items: center;
}

.step-card {
  background: white;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
  min-width: 200px;
  transition: all 0.2s ease;
  position: relative;
}

.step-card.completed {
  border-color: #28a745;
  background: #f8fff9;
}

.step-card.current {
  border-color: #667eea;
  background: #f8f9ff;
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.2);
}

.step-card.pending {
  border-color: #e1e5e9;
  background: #f8f9fa;
  opacity: 0.7;
}

.step-number {
  display: inline-block;
  width: 30px;
  height: 30px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  line-height: 30px;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.step-card.completed .step-number {
  background: #28a745;
}

.step-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.step-content h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.step-content p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.step-arrow {
  font-size: 1.5rem;
  color: #667eea;
  margin: 0 1rem;
}

.master-bc-integration h3 {
  margin: 0 0 2rem 0;
  color: #333;
}

.integration-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.integration-section {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.integration-section h4 {
  margin: 0 0 1rem 0;
  color: #333;
}

.integration-section p {
  margin: 0 0 1rem 0;
  color: #666;
  line-height: 1.5;
}

.integration-section ul {
  margin: 0;
  padding-left: 1.5rem;
  color: #666;
}

.integration-section li {
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

/* Process View Styles */
.process-view {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.process-view h3 {
  margin: 0 0 2rem 0;
  color: #333;
}

.detailed-steps {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.detailed-step {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.detailed-step.completed {
  border-color: #28a745;
}

.detailed-step.current {
  border-color: #667eea;
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.1);
}

.step-header {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e5e9;
}

.step-indicator {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-right: 2rem;
}

.step-indicator .step-number {
  width: 40px;
  height: 40px;
  line-height: 40px;
  font-size: 1.2rem;
}

.step-indicator .step-icon {
  font-size: 2rem;
}

.step-title {
  flex: 1;
}

.step-title h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.step-title p {
  margin: 0;
  color: #666;
}

.step-status {
  margin-left: auto;
}

.status-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.status-badge.completed {
  background: #d4edda;
  color: #155724;
}

.status-badge.current {
  background: #cce7ff;
  color: #004085;
}

.status-badge.pending {
  background: #f8f9fa;
  color: #6c757d;
}

.step-details {
  padding: 2rem;
}

.step-content h5 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1rem;
}

.step-content ul {
  margin: 0 0 1.5rem 0;
  padding-left: 1.5rem;
  color: #666;
}

.step-content li {
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.step-actions {
  padding: 1rem 2rem;
  background: #f8f9fa;
  border-top: 1px solid #e1e5e9;
}

/* Alternatives View Styles */
.alternatives-view {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.alternatives-view h3 {
  margin: 0 0 1rem 0;
  color: #333;
}

.alternatives-view p {
  margin: 0 0 2rem 0;
  color: #666;
  font-size: 1.1rem;
}

.alternatives-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.alternative-card {
  background: white;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
  position: relative;
}

.alternative-card:hover {
  border-color: #667eea;
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.1);
}

.alternative-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e5e9;
}

.alternative-header h4 {
  margin: 0;
  color: #333;
}

.risk-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.risk-badge.low {
  background: #d4edda;
  color: #155724;
}

.risk-badge.medium {
  background: #fff3cd;
  color: #856404;
}

.risk-badge.high {
  background: #f8d7da;
  color: #721c24;
}

.alternative-content {
  padding: 1.5rem;
}

.alternative-description {
  margin: 0 0 1.5rem 0;
  color: #666;
  line-height: 1.5;
}

.alternative-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.detail-item {
  color: #333;
}

.detail-item strong {
  margin-right: 0.5rem;
}

.pros-cons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.pros h5,
.cons h5 {
  margin: 0 0 1rem 0;
  color: #333;
}

.pros ul,
.cons ul {
  margin: 0;
  padding-left: 1.5rem;
  color: #666;
}

.pros li,
.cons li {
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.recommended-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.alternatives-comparison h4 {
  margin: 0 0 1.5rem 0;
  color: #333;
}

.comparison-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.comparison-table th {
  background: #667eea;
  color: white;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
}

.comparison-table td {
  padding: 1rem;
  border-bottom: 1px solid #e1e5e9;
}

.comparison-table tbody tr:hover {
  background: #f8f9fa;
}

.recommended {
  color: #ffc107;
  font-weight: 600;
}

.not-recommended {
  color: #6c757d;
}

/* Responsive Design for Framework */
@media (max-width: 768px) {
  .framework-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .framework-status {
    align-items: flex-start;
  }

  .framework-navigation {
    flex-direction: column;
  }

  .benefits-grid,
  .integration-content,
  .alternatives-grid {
    grid-template-columns: 1fr;
  }

  .steps-container {
    flex-direction: column;
  }

  .step-arrow {
    transform: rotate(90deg);
    margin: 1rem 0;
  }

  .step-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .step-indicator {
    margin-right: 0;
  }

  .step-status {
    margin-left: 0;
  }

  .pros-cons {
    grid-template-columns: 1fr;
  }

  .comparison-table {
    font-size: 0.8rem;
  }

  .comparison-table th,
  .comparison-table td {
    padding: 0.5rem;
  }
}

/* Integrated Dashboard Styles */
.integrated-dashboard {
  padding: 1rem;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
}

.header-content h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
}

.header-description {
  margin: 0;
  opacity: 0.9;
  font-size: 1.1rem;
  line-height: 1.5;
}

.header-stats {
  display: flex;
  gap: 2rem;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
}

.dashboard-content {
  margin-top: 2rem;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.dashboard-section {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 2rem;
}

.dashboard-section h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.3rem;
}

/* Metrics Section */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.metric-card {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  text-align: center;
  border-left: 4px solid #667eea;
}

.metric-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.25rem;
}

.metric-label {
  font-size: 0.9rem;
  color: #666;
}

/* Actions Section */
.actions-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.action-card {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-left: 4px solid #667eea;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-card:hover {
  background: #f0f4ff;
  border-left-color: #5a67d8;
  transform: translateX(4px);
}

.action-icon {
  font-size: 2rem;
  margin-right: 1rem;
}

.action-content {
  flex: 1;
}

.action-content h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.action-content p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.action-arrow {
  font-size: 1.5rem;
  color: #667eea;
  margin-left: 1rem;
}

/* Integration Section */
.integration-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.integration-item {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 6px;
  border-left: 4px solid #28a745;
}

.integration-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.integration-name {
  font-weight: 600;
  color: #333;
}

.integration-status {
  font-size: 0.8rem;
  font-weight: 600;
}

.integration-progress {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: #e1e5e9;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.8rem;
  font-weight: 600;
  color: #666;
}

/* Activity Section */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #17a2b8;
}

.activity-icon {
  font-size: 1.5rem;
  margin-right: 1rem;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
}

.activity-action {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.activity-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.8rem;
  color: #999;
}

.activity-status {
  font-size: 1.2rem;
  margin-left: 1rem;
}

/* Overview Section */
.overview-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.overview-item {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.overview-item h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.overview-item p {
  margin: 0 0 1rem 0;
  color: #666;
  line-height: 1.5;
}

.overview-features {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.feature-tag {
  background: #667eea;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Integration View Styles */
.integration-view {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.integration-view h3 {
  margin: 0 0 1rem 0;
  color: #333;
}

.integration-view p {
  margin: 0 0 2rem 0;
  color: #666;
  font-size: 1.1rem;
}

.integration-overview {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.integration-diagram {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
}

.central-hub {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 50%;
  text-align: center;
  min-width: 150px;
  min-height: 150px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.hub-icon {
  font-size: 3rem;
  margin-bottom: 0.5rem;
}

.hub-title {
  font-weight: 700;
  font-size: 1.1rem;
}

.integration-connections {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  width: 100%;
}

.connection-item {
  background: white;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.2s ease;
}

.connection-item:hover {
  border-color: #667eea;
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.1);
}

.connection-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.connection-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.connection-status.active {
  color: #28a745;
  font-size: 0.9rem;
  font-weight: 600;
}

.integration-features h4 {
  margin: 0 0 1.5rem 0;
  color: #333;
}

.data-flow-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.flow-item {
  display: grid;
  grid-template-columns: 1fr auto 1fr 2fr;
  gap: 1rem;
  align-items: center;
  padding: 1rem;
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
}

.flow-source,
.flow-target {
  font-weight: 600;
  color: #333;
  text-align: center;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 4px;
}

.flow-arrow {
  font-size: 1.5rem;
  color: #667eea;
  text-align: center;
}

.flow-description {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.integration-benefits h4 {
  margin: 0 0 1.5rem 0;
  color: #333;
}

.benefits-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.benefit-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  border-left: 4px solid #28a745;
}

.benefit-icon {
  font-size: 2rem;
  color: #28a745;
}

.benefit-content h5 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.benefit-content p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.integration-actions h4 {
  margin: 0 0 1.5rem 0;
  color: #333;
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.integration-btn {
  padding: 1rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  color: white;
}

.integration-btn.financial {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.integration-btn.stakeholders {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
}

.integration-btn.compliance {
  background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
}

.integration-btn.excel {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

.integration-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Responsive Design for Integration */
@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .header-stats {
    flex-direction: row;
    gap: 1rem;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .integration-connections {
    grid-template-columns: 1fr;
  }

  .flow-item {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .flow-arrow {
    transform: rotate(90deg);
  }

  .benefits-list,
  .action-buttons {
    grid-template-columns: 1fr;
  }

  .central-hub {
    min-width: 120px;
    min-height: 120px;
  }

  .hub-icon {
    font-size: 2rem;
  }
}

/* Compliance Check Styles */
.compliance-check-section {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 8px;
  border-left: 4px solid #667eea;
  margin-bottom: 2rem;
}

.compliance-check-section h4 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.4rem;
}

.compliance-overview {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.compliance-status {
  display: flex;
  gap: 2rem;
  align-items: flex-start;
}

.compliance-score {
  display: flex;
  justify-content: center;
  align-items: center;
}

.score-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.score-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.score-label {
  font-size: 0.9rem;
  opacity: 0.9;
}

.compliance-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.compliance-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 6px;
  border-left: 4px solid #28a745;
}

.compliance-item.good {
  border-left-color: #28a745;
}

.compliance-item.warning {
  border-left-color: #ffc107;
}

.compliance-item.pending {
  border-left-color: #6c757d;
}

.compliance-icon {
  font-size: 1.2rem;
}

.compliance-text {
  flex: 1;
  color: #333;
  font-weight: 500;
}

.compliance-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.compliance-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.compliance-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.compliance-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.compliance-btn.secondary {
  background: #f8f9fa;
  color: #333;
  border: 2px solid #e1e5e9;
}

.compliance-btn.secondary:hover {
  border-color: #667eea;
  background: #f0f4ff;
}

.compliance-quick-check {
  margin-top: 1.5rem;
  padding: 1rem;
  background: white;
  border-radius: 6px;
  border: 1px solid #e1e5e9;
}

.quick-check-score {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.score-text {
  color: #666;
  font-weight: 500;
}

.score-value {
  color: #667eea;
  font-weight: 700;
  font-size: 1.1rem;
}

.check-compliance-btn {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.check-compliance-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

/* Portfolio Integration Styles */
.portfolio-integration {
  margin-bottom: 2rem;
}

.portfolio-integration h4 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.4rem;
}

.portfolio-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.portfolio-card {
  background: white;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.2s ease;
}

.portfolio-card:hover {
  border-color: #667eea;
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.1);
}

.portfolio-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.portfolio-content h5 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1.1rem;
}

.portfolio-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 0.5rem;
}

.portfolio-description {
  color: #666;
  font-size: 0.9rem;
}

/* Governance Framework Styles */
.governance-framework h4 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.4rem;
}

.governance-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.governance-item {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
  border-left: 4px solid #667eea;
}

.governance-item h5 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1.1rem;
}

.governance-item p {
  margin: 0 0 1rem 0;
  color: #666;
  line-height: 1.5;
}

.governance-status {
  display: flex;
  justify-content: flex-end;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.status-badge.good {
  background: #d4edda;
  color: #155724;
}

.status-badge.warning {
  background: #fff3cd;
  color: #856404;
}

.status-badge.pending {
  background: #f8f9fa;
  color: #6c757d;
}

/* Responsive Design for Compliance */
@media (max-width: 768px) {
  .compliance-status {
    flex-direction: column;
    gap: 1rem;
  }

  .score-circle {
    width: 100px;
    height: 100px;
  }

  .score-value {
    font-size: 1.5rem;
  }

  .compliance-actions {
    flex-direction: column;
  }

  .portfolio-metrics {
    grid-template-columns: 1fr;
  }

  .compliance-overview {
    gap: 1rem;
  }
}

/* CRUD Interface Styles */
.crud-toolbar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem 2rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.toolbar-section h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.case-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.case-status {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.version {
  background: rgba(255, 255, 255, 0.3);
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  margin-left: 0.5rem;
}

.toolbar-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #28a745;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #218838;
  transform: translateY(-1px);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
}

.btn-secondary:hover:not(:disabled) {
  background: white;
  transform: translateY(-1px);
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #c82333;
  transform: translateY(-1px);
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.8rem;
}

/* Status Messages */
.status-message {
  padding: 1rem 1.5rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  animation: slideIn 0.3s ease;
}

.status-message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.status-message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-message button {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  font-size: 1.2rem;
  margin-left: auto;
}

.status-icon {
  font-size: 1.2rem;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.modal-content {
  background: white;
  border-radius: 8px;
  max-width: 800px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  background: #f8f9fa;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.3rem;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  padding: 0.5rem;
  border-radius: 4px;
}

.modal-close:hover {
  background: #e9ecef;
}

.modal-body {
  padding: 2rem;
  max-height: 60vh;
  overflow-y: auto;
}

/* Loading Indicator */
.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 2rem;
  color: #666;
}

.spinner {
  width: 24px;
  height: 24px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Saved Cases List */
.saved-cases-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.saved-case-item {
  background: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  transition: all 0.2s ease;
}

.saved-case-item:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.saved-case-item .case-info {
  flex: 1;
}

.saved-case-item .case-info h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1.1rem;
}

.saved-case-item .case-info p {
  margin: 0 0 0.5rem 0;
  color: #666;
  font-size: 0.9rem;
}

.saved-case-item .case-info small {
  color: #888;
  font-size: 0.8rem;
}

.case-actions {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  color: #666;
}

.empty-state p {
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
}

/* Animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Responsive Design for CRUD */
@media (max-width: 768px) {
  .crud-toolbar {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }

  .toolbar-actions {
    justify-content: center;
  }

  .modal-content {
    width: 95%;
    margin: 1rem;
  }

  .modal-header {
    padding: 1rem 1.5rem;
  }

  .modal-body {
    padding: 1.5rem;
  }

  .saved-case-item {
    flex-direction: column;
    gap: 1rem;
  }

  .case-actions {
    justify-content: center;
  }

  .btn {
    flex: 1;
    justify-content: center;
  }
}

/* Business Case Generator Styles */
.business-case-generator {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.generator-header {
  text-align: center;
  margin-bottom: 3rem;
}

.generator-header h2 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 2rem;
  font-weight: 700;
}

.generator-header p {
  color: #666;
  font-size: 1.1rem;
  margin: 0;
}

.generator-form {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.form-section {
  padding: 2rem;
  border-bottom: 1px solid #e1e5e9;
}

.form-section:last-child {
  border-bottom: none;
}

.form-section h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.3rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #333;
  font-weight: 500;
  font-size: 0.9rem;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.array-input {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  align-items: center;
}

.array-input input {
  flex: 1;
  margin-bottom: 0;
}

.btn-remove {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background-color 0.2s ease;
  flex-shrink: 0;
}

.btn-remove:hover {
  background: #c82333;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.8rem;
}

.form-actions {
  padding: 2rem;
  background: #f8f9fa;
  text-align: center;
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
}

/* Result Display */
.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e1e5e9;
}

.result-header h2 {
  margin: 0;
  color: #333;
  font-size: 1.8rem;
}

.result-actions {
  display: flex;
  gap: 1rem;
}

.generated-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.content-info {
  background: #f8f9fa;
  padding: 1.5rem;
  border-bottom: 1px solid #e1e5e9;
}

.content-info p {
  margin: 0 0 0.5rem 0;
  color: #666;
  font-size: 0.9rem;
}

.content-info p:last-child {
  margin-bottom: 0;
}

.content-preview {
  padding: 2rem;
  max-height: 70vh;
  overflow-y: auto;
}

.content-preview pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Georgia', serif;
  line-height: 1.6;
  color: #333;
  margin: 0;
  font-size: 0.95rem;
}

/* Loading States */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Responsive Design for Generator */
@media (max-width: 768px) {
  .business-case-generator {
    padding: 1rem;
  }

  .generator-header h2 {
    font-size: 1.5rem;
  }

  .form-section {
    padding: 1.5rem;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .result-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .result-actions {
    justify-content: center;
  }

  .array-input {
    flex-direction: column;
    align-items: stretch;
  }

  .btn-remove {
    align-self: flex-end;
    width: fit-content;
  }

  .content-preview {
    padding: 1rem;
  }

  .content-preview pre {
    font-size: 0.85rem;
  }
}

/* Enhanced Form Styling */
.form-group input[type="number"] {
  text-align: right;
}

.form-group select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  appearance: none;
}

.form-section:nth-child(even) {
  background: #fafbfc;
}

/* Success States */
.success-indicator {
  background: #d4edda;
  color: #155724;
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.success-indicator .icon {
  font-size: 1.2rem;
}

/* Validation States */
.form-group.error input,
.form-group.error textarea,
.form-group.error select {
  border-color: #dc3545;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.form-group .error-message {
  color: #dc3545;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

/* Progress Indicator */
.progress-indicator {
  background: #e9ecef;
  height: 4px;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 2rem;
}

.progress-bar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  height: 100%;
  transition: width 0.3s ease;
}

/* Tooltip Styles */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip .tooltiptext {
  visibility: hidden;
  width: 200px;
  background-color: #333;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px 10px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  margin-left: -100px;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 0.8rem;
}

.tooltip:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}

/* Strategic Foundation Styles */
.strategic-vision,
.program-management {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.vision-header,
.program-header {
  text-align: center;
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 3px solid #e1e5e9;
}

.vision-header h2,
.program-header h2 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 2.2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.vision-header p,
.program-header p {
  color: #666;
  font-size: 1.1rem;
  margin: 0;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.vision-navigation,
.program-navigation {
  display: flex;
  gap: 1rem;
  margin-bottom: 3rem;
  justify-content: center;
  flex-wrap: wrap;
}

.nav-btn {
  background: white;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  padding: 1rem 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.95rem;
  font-weight: 500;
  color: #666;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 180px;
  justify-content: center;
}

.nav-btn:hover {
  border-color: #667eea;
  background: #f8f9ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.nav-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: transparent;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.vision-content,
.program-content {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 3rem;
  margin-bottom: 3rem;
}

.vision-section h3,
.program-section h3,
.tenets-section h3,
.communication-section h3,
.influence-section h3,
.roles-section h3,
.initiation-section h3,
.approval-section h3,
.interface-section h3 {
  margin: 0 0 2rem 0;
  color: #2c3e50;
  font-size: 1.6rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f1f3f4;
}

/* Tenet Cards */
.tenet-card,
.responsibility-card,
.initiation-card,
.approval-card,
.interface-card {
  background: #fafbfc;
  border: 1px solid #e1e5e9;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  transition: all 0.3s ease;
}

.tenet-card:hover,
.responsibility-card:hover,
.initiation-card:hover,
.approval-card:hover,
.interface-card:hover {
  border-color: #667eea;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
}

.tenet-card h4,
.responsibility-card h4,
.initiation-card h4,
.approval-card h4,
.interface-card h4 {
  margin: 0 0 1.5rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Criteria Grid */
.criteria-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.criteria-item {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
}

.criteria-item label {
  display: block;
  margin-bottom: 0.75rem;
  color: #2c3e50;
  font-weight: 500;
  font-size: 0.9rem;
}

.criteria-item input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  text-align: center;
  font-size: 1rem;
  font-weight: 600;
}

/* Specialized Input Styles */
.stage-input,
.team-input,
.board-input {
  display: grid;
  grid-template-columns: 1fr 2fr auto;
  gap: 1rem;
  margin-bottom: 1rem;
  align-items: center;
}

.stage-input input,
.team-input input,
.board-input input {
  padding: 0.75rem;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  font-size: 0.9rem;
}

/* Summary Section */
.vision-summary,
.program-summary {
  background: linear-gradient(135deg, #f8f9ff 0%, #f1f3ff 100%);
  border: 1px solid #e1e5e9;
  border-radius: 12px;
  padding: 2rem;
}

.vision-summary h4,
.program-summary h4 {
  margin: 0 0 1.5rem 0;
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
  text-align: center;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.stat-item {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

.stat-item:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.stat-label {
  display: block;
  color: #666;
  font-size: 0.85rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  display: block;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 700;
}

/* Responsive Design for Strategic Foundation */
@media (max-width: 768px) {
  .strategic-vision,
  .program-management {
    padding: 1rem;
  }

  .vision-header h2,
  .program-header h2 {
    font-size: 1.8rem;
  }

  .vision-navigation,
  .program-navigation {
    flex-direction: column;
    align-items: center;
  }

  .nav-btn {
    width: 100%;
    max-width: 300px;
  }

  .vision-content,
  .program-content {
    padding: 2rem;
  }

  .criteria-grid {
    grid-template-columns: 1fr;
  }

  .stage-input,
  .team-input,
  .board-input {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .summary-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Enhanced Form Styling for Strategic Foundation */
.strategic-vision .form-group,
.program-management .form-group {
  margin-bottom: 2rem;
}

.strategic-vision .form-group label,
.program-management .form-group label {
  display: block;
  margin-bottom: 0.75rem;
  color: #2c3e50;
  font-weight: 600;
  font-size: 0.95rem;
}

.strategic-vision .form-group input,
.strategic-vision .form-group textarea,
.strategic-vision .form-group select,
.program-management .form-group input,
.program-management .form-group textarea,
.program-management .form-group select {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  background: white;
}

.strategic-vision .form-group input:focus,
.strategic-vision .form-group textarea:focus,
.strategic-vision .form-group select:focus,
.program-management .form-group input:focus,
.program-management .form-group textarea:focus,
.program-management .form-group select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
  background: #fafbff;
}

.strategic-vision .form-group textarea,
.program-management .form-group textarea {
  resize: vertical;
  min-height: 120px;
  line-height: 1.6;
}

/* Array Input Styling for Strategic Foundation */
.strategic-vision .array-input,
.program-management .array-input {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1rem;
  align-items: center;
}

.strategic-vision .array-input input,
.program-management .array-input input {
  flex: 1;
  margin-bottom: 0;
}

.strategic-vision .btn-remove,
.program-management .btn-remove {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.75rem;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.3s ease;
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.strategic-vision .btn-remove:hover,
.program-management .btn-remove:hover {
  background: #c82333;
  transform: scale(1.05);
}

.strategic-vision .btn-sm,
.program-management .btn-sm {
  padding: 0.75rem 1.5rem;
  font-size: 0.85rem;
  border-radius: 6px;
  background: #667eea;
  color: white;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 0.5rem;
}

.strategic-vision .btn-sm:hover,
.program-management .btn-sm:hover {
  background: #5a6fd8;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* AI Market Analysis Styles */
.ai-market-analysis {
  padding: 1rem;
}

.analysis-header {
  text-align: center;
  margin-bottom: 2rem;
  padding: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
}

.analysis-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
}

.analysis-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
}

.analysis-navigation {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.analysis-navigation .nav-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 2px solid #e1e5e9;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  color: #333;
}

.analysis-navigation .nav-btn:hover {
  border-color: #667eea;
  background: #f8f9ff;
}

.analysis-navigation .nav-btn.active {
  border-color: #667eea;
  background: #667eea;
  color: white;
}

.analysis-content {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 2rem;
}

/* AI Overview Styles */
.ai-overview h3 {
  margin: 0 0 2rem 0;
  color: #333;
  font-size: 1.8rem;
}

.market-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.metric-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
}

.metric-value {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.metric-label {
  font-size: 1rem;
  opacity: 0.9;
}

.key-insights h4 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.4rem;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.insight-card {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.insight-card h5 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.1rem;
}

.insight-card p {
  margin: 0;
  color: #666;
  line-height: 1.6;
}

.strategic-recommendations h4 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.4rem;
}

.recommendation-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.recommendation-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #28a745;
}

.rec-priority {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  min-width: 100px;
  text-align: center;
}

.rec-priority.high {
  background: #dc3545;
  color: white;
}

.rec-priority.medium {
  background: #ffc107;
  color: #333;
}

.rec-priority.low {
  background: #28a745;
  color: white;
}

.rec-text {
  flex: 1;
  color: #333;
  font-weight: 500;
}

/* AI Trends Styles */
.ai-trends h3 {
  margin: 0 0 2rem 0;
  color: #333;
  font-size: 1.8rem;
}

.trends-section {
  margin-bottom: 3rem;
}

.trends-section h4 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.4rem;
}

.trends-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.trend-card {
  background: white;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.trend-card:hover {
  border-color: #667eea;
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.1);
}

.trend-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.trend-adoption {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.adoption-bar {
  width: 100%;
  height: 8px;
  background: #e1e5e9;
  border-radius: 4px;
  overflow: hidden;
}

.adoption-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
}

.adoption-text {
  font-size: 0.9rem;
  color: #666;
}

.market-drivers h4 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.4rem;
}

.drivers-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 3rem;
}

.driver-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #17a2b8;
}

.driver-icon {
  color: #17a2b8;
  font-weight: 700;
}

.driver-text {
  color: #333;
  font-weight: 500;
}

.technology-timeline h4 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.4rem;
}

.timeline {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.timeline-item {
  display: flex;
  gap: 2rem;
  align-items: flex-start;
}

.timeline-year {
  background: #667eea;
  color: white;
  padding: 1rem;
  border-radius: 50%;
  font-weight: 700;
  min-width: 80px;
  text-align: center;
}

.timeline-item.current .timeline-year {
  background: #28a745;
}

.timeline-item.near .timeline-year {
  background: #ffc107;
  color: #333;
}

.timeline-item.future .timeline-year {
  background: #6c757d;
}

.timeline-content {
  flex: 1;
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.timeline-content h5 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.timeline-content p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

/* Competitive Analysis Styles */
.competitive-analysis h3 {
  margin: 0 0 2rem 0;
  color: #333;
  font-size: 1.8rem;
}

.market-leaders h4 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.4rem;
}

.leaders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.leader-card {
  background: white;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.leader-card:hover {
  border-color: #667eea;
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.1);
}

.leader-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.leader-header h5 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
}

.trend-indicator {
  font-size: 1.5rem;
}

.leader-strength {
  color: #666;
  margin-bottom: 1rem;
  font-style: italic;
}

.market-share {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.share-bar {
  width: 100%;
  height: 12px;
  background: #e1e5e9;
  border-radius: 6px;
  overflow: hidden;
}

.share-fill {
  height: 100%;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  transition: width 0.3s ease;
}

.share-text {
  font-size: 0.9rem;
  color: #666;
  font-weight: 600;
}

.emerging-players h4 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.4rem;
}

.emerging-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 3rem;
}

.emerging-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #ffc107;
}

.emerging-name {
  font-weight: 600;
  color: #333;
}

.emerging-status {
  background: #ffc107;
  color: #333;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.competitive-matrix h4 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.4rem;
}

.matrix-description {
  margin-bottom: 2rem;
}

.matrix-description p {
  color: #666;
  font-style: italic;
}

.matrix-chart {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  height: 300px;
}

.matrix-quadrant {
  padding: 2rem;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.matrix-quadrant.leaders {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
}

.matrix-quadrant.challengers {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
  color: #333;
}

.matrix-quadrant.visionaries {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.matrix-quadrant.niche {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  color: white;
}

.matrix-quadrant h5 {
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
}

.matrix-quadrant p {
  margin: 0;
  opacity: 0.9;
}

/* Responsive Design for AI Analysis */
@media (max-width: 768px) {
  .analysis-header h2 {
    font-size: 2rem;
  }

  .analysis-navigation {
    flex-direction: column;
  }

  .market-metrics,
  .insights-grid,
  .trends-grid,
  .leaders-grid,
  .emerging-grid {
    grid-template-columns: 1fr;
  }

  .timeline-item {
    flex-direction: column;
    gap: 1rem;
  }

  .timeline-year {
    align-self: flex-start;
  }

  .matrix-chart {
    grid-template-columns: 1fr;
    height: auto;
  }

  .recommendation-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* Master Business Case Styles */
.master-business-case {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.master-navigation {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.master-nav-btn {
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 0.75rem 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.master-nav-btn:hover {
  background-color: #f8f9fa;
  border-color: #667eea;
}

.master-nav-btn.active {
  background-color: #667eea;
  color: white;
  border-color: #667eea;
}

.metrics-dashboard {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin: 1.5rem 0;
}

.use-cases-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.use-case-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
  transition: box-shadow 0.2s ease;
}

.use-case-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.use-case-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.use-case-id {
  font-family: monospace;
  font-weight: bold;
  color: #667eea;
}

.use-case-name {
  margin: 0.5rem 0 1rem 0;
  color: #333;
}

.use-case-metrics {
  margin: 1rem 0;
}

.metric-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.use-case-status {
  margin-top: 1rem;
  text-align: center;
}

.use-case-dependencies {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e1e5e9;
  color: #666;
}

/* PMI Compliance Styles */
.pmi-compliance {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.compliance-navigation {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.compliance-nav-btn {
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 0.75rem 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.compliance-nav-btn:hover {
  background-color: #f8f9fa;
  border-color: #667eea;
}

.compliance-nav-btn.active {
  background-color: #667eea;
  color: white;
  border-color: #667eea;
}

.lifecycle-phases {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
}

.phase-card {
  background: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  padding: 1rem;
}

.phase-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.phase-progress {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: #e1e5e9;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.8rem;
  font-weight: 500;
}

.phase-dates {
  font-size: 0.8rem;
  color: #666;
}

.knowledge-areas-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.knowledge-area-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
}

.area-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.area-artifacts {
  margin: 1rem 0;
}

.area-artifacts h5 {
  margin-bottom: 0.5rem;
  color: #333;
}

.area-artifacts ul {
  margin: 0;
  padding-left: 1.5rem;
  font-size: 0.9rem;
}

.compliance-bar {
  height: 6px;
  background: #e1e5e9;
  border-radius: 3px;
  overflow: hidden;
  margin-top: 1rem;
}

.compliance-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.pgmp-sections {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin-top: 1.5rem;
}

.pgmp-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
}

.pgmp-metrics {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
}

.pgmp-metric {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.pgmp-metric span:first-child {
  min-width: 200px;
  font-weight: 500;
}

.metric-bar {
  flex: 1;
  height: 20px;
  background: #e1e5e9;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
}

.metric-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.metric-bar span {
  position: absolute;
  right: 10px;
  font-size: 0.8rem;
  font-weight: 500;
  color: #333;
}

.supporting-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.supporting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: white;
  border-radius: 6px;
  border: 1px solid #e1e5e9;
}

.supporting-name {
  font-size: 0.9rem;
}

.supporting-score {
  font-weight: bold;
}

/* Stakeholder Feedback Styles */
.stakeholder-feedback {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.feedback-navigation {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.feedback-nav-btn {
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 0.75rem 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.feedback-nav-btn:hover {
  background-color: #f8f9fa;
  border-color: #667eea;
}

.feedback-nav-btn.active {
  background-color: #667eea;
  color: white;
  border-color: #667eea;
}

.stakeholder-summary {
  margin-bottom: 2rem;
}

.summary-metrics {
  display: flex;
  gap: 2rem;
  justify-content: center;
  flex-wrap: wrap;
}

.summary-metric {
  text-align: center;
}

.summary-metric .metric-label {
  display: block;
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.summary-metric .metric-value {
  display: block;
  font-size: 1.5rem;
  font-weight: bold;
  color: #667eea;
}

.stakeholders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;
}

.stakeholder-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
  transition: box-shadow 0.2s ease;
}

.stakeholder-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.stakeholder-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.stakeholder-info h4 {
  margin: 0 0 0.25rem 0;
  color: #333;
}

.stakeholder-info p {
  margin: 0 0 0.5rem 0;
  color: #666;
  font-size: 0.9rem;
}

.stakeholder-category {
  background: #e9ecef;
  color: #495057;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
}

.score-circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #667eea;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.1rem;
}

.stakeholder-attributes {
  margin: 1rem 0;
}

.attribute {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.stakeholder-feedback h5 {
  margin: 1rem 0 0.5rem 0;
  color: #333;
  font-size: 0.9rem;
}

.stakeholder-feedback p {
  margin: 0;
  font-size: 0.9rem;
  color: #666;
  line-height: 1.4;
}

.stakeholder-engagement {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e1e5e9;
  color: #666;
  font-size: 0.8rem;
}
