# Master Business Case Governance Platform - Setup Guide

## 🚀 Full-Stack Setup with MongoDB CRUD Operations

This guide will help you set up the complete Master Business Case Governance Platform with MongoDB integration for full CRUD operations.

## 📋 Prerequisites

- **Node.js** (v16 or higher)
- **MongoDB** (v5.0 or higher)
- **npm** or **yarn**
- **Git**

## 🛠️ Installation Steps

### 1. **Install Dependencies**

#### Frontend Dependencies:
```bash
npm install
```

#### Backend Dependencies:
```bash
cd server
npm install
```

### 2. **MongoDB Setup**

#### Option A: Local MongoDB
1. Install MongoDB locally
2. Start MongoDB service:
   ```bash
   mongod --dbpath /path/to/your/data/directory
   ```

#### Option B: MongoDB Atlas (Cloud)
1. Create account at [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Create a new cluster
3. Get connection string
4. Update `server/.env` with your connection string

### 3. **Environment Configuration**

#### Frontend (.env):
```env
REACT_APP_API_URL=http://localhost:5000/api
REACT_APP_APP_NAME=Master Business Case Governance Platform
REACT_APP_VERSION=1.0.0
```

#### Backend (server/.env):
```env
MONGODB_URI=mongodb://localhost:27017/master_business_case_db
PORT=5000
NODE_ENV=development
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRE=7d
FRONTEND_URL=http://localhost:3000
```

### 4. **Start the Application**

#### Terminal 1 - Backend Server:
```bash
cd server
npm run dev
```

#### Terminal 2 - Frontend Application:
```bash
npm start
```

### 5. **Access the Application**

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000
- **API Health Check**: http://localhost:5000/api/health

## 🎯 Features Available

### ✅ **CRUD Operations**
- **Create** new Master Business Cases
- **Read** all business cases with pagination and filtering
- **Update** existing business cases
- **Delete** business cases with confirmation

### ✅ **Advanced Features**
- **Search** business cases by name or content
- **Filter** by status, business driver, investment category
- **Pagination** for large datasets
- **Status Management** with workflow transitions
- **Duplicate** business cases for templates
- **Export** data in JSON/CSV formats

### ✅ **Data Management**
- **MongoDB Integration** with Mongoose ODM
- **Data Validation** on both frontend and backend
- **Error Handling** with user-friendly messages
- **Real-time Updates** after CRUD operations

## 📊 API Endpoints

### Master Business Cases
- `GET /api/master-business-cases` - Get all business cases
- `GET /api/master-business-cases/:id` - Get single business case
- `POST /api/master-business-cases` - Create new business case
- `PUT /api/master-business-cases/:id` - Update business case
- `DELETE /api/master-business-cases/:id` - Delete business case
- `PATCH /api/master-business-cases/:id/status` - Update status only
- `GET /api/master-business-cases/stats/summary` - Get statistics

### Query Parameters
- `page` - Page number for pagination
- `limit` - Items per page
- `status` - Filter by status
- `businessDriver` - Filter by business driver
- `investmentCategory` - Filter by investment category
- `search` - Search in program names
- `sortBy` - Sort field
- `sortOrder` - Sort direction (asc/desc)

## 🗄️ Database Schema

### MasterBusinessCase Collection
```javascript
{
  programInfo: {
    programName: String,
    programManager: String,
    sponsor: String,
    strategicAlignment: String,
    businessDriver: String,
    investmentCategory: String,
    totalBudget: Number,
    duration: Number,
    startDate: Date,
    endDate: Date,
    currency: String
  },
  governance: {
    steeringCommittee: Array,
    decisionGates: Array,
    riskTolerance: String,
    complianceFramework: Array
  },
  useCases: Array,
  milestones: Array,
  financialMetrics: Object,
  status: String,
  createdBy: String,
  lastModifiedBy: String,
  tags: Array,
  attachments: Array,
  createdAt: Date,
  updatedAt: Date
}
```

## 🔧 Development Commands

### Frontend
```bash
npm start          # Start development server
npm run build      # Build for production
npm test           # Run tests
npm run eject      # Eject from Create React App
```

### Backend
```bash
npm run dev        # Start with nodemon (auto-restart)
npm start          # Start production server
npm test           # Run tests
```

## 🚀 Production Deployment

### Frontend (Netlify/Vercel)
1. Build the application: `npm run build`
2. Deploy the `build` folder
3. Set environment variables in deployment platform

### Backend (Heroku/Railway/DigitalOcean)
1. Set up MongoDB Atlas for production
2. Configure environment variables
3. Deploy server folder
4. Update CORS settings for production domain

## 🔒 Security Features

- **Input Validation** with express-validator
- **Rate Limiting** to prevent abuse
- **CORS Configuration** for cross-origin requests
- **Helmet** for security headers
- **Data Sanitization** to prevent injection attacks

## 📈 Performance Optimizations

- **Database Indexing** for faster queries
- **Pagination** to handle large datasets
- **Caching** with appropriate headers
- **Compression** for API responses
- **Lazy Loading** for frontend components

## 🐛 Troubleshooting

### Common Issues:

1. **MongoDB Connection Error**
   - Check if MongoDB is running
   - Verify connection string in .env
   - Check network connectivity for Atlas

2. **CORS Errors**
   - Verify FRONTEND_URL in server/.env
   - Check if both servers are running

3. **API Not Found**
   - Ensure backend server is running on port 5000
   - Check API_URL in frontend .env

4. **Build Errors**
   - Clear node_modules and reinstall
   - Check for version conflicts

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review API documentation
3. Check browser console for errors
4. Verify MongoDB connection and data

## 🎉 Success!

You now have a fully functional Master Business Case Governance Platform with:
- ✅ MongoDB CRUD operations
- ✅ Real-time data management
- ✅ Professional UI/UX
- ✅ PMI/PGMP compliance features
- ✅ Advanced search and filtering
- ✅ Export capabilities
- ✅ Responsive design

Happy coding! 🚀
