import React, { useState, useEffect } from 'react'
import './App.css'
import ParametersSetup from './components/ParametersSetup'
import CostSalesDefinition from './components/CostSalesDefinition'
import SensitivityAnalysis from './components/SensitivityAnalysis'
import FinancialIndexes from './components/FinancialIndexes'
import Visualizations from './components/Visualizations'
import MasterBusinessCase from './components/MasterBusinessCase'
import EnhancedPMICompliance from './components/EnhancedPMICompliance'
import EnhancedStakeholderFeedback from './components/EnhancedStakeholderFeedback'
import MasterBusinessCaseManager from './components/MasterBusinessCaseManager'
import ChangeManagementRoadmap from './components/ChangeManagementRoadmap'
import AdvancedFinancialModeling from './components/AdvancedFinancialModeling'
import AugmentExcelAnalysis from './components/AugmentExcelAnalysis'
import BusinessCaseFramework from './components/BusinessCaseFramework'
import BusinessCaseGenerator from './components/BusinessCaseGenerator'
import CORSTest from './components/CORSTest'
import StrategicVision from './components/StrategicVision'
import ProgramManagement from './components/ProgramManagement'
import { FinancialCalculator, SensitivityAnalyzer } from './utils/financialCalculations'
import DataExporter from './utils/dataExport'

function App() {
  const [activeTab, setActiveTab] = useState('strategic-vision')
  const [projectData, setProjectData] = useState({
    // Strategic Foundation Data
    strategicVision: {
      mission: '',
      vision: '',
      coreValues: [],
      focusAreas: [],
      policyAlignment: '',
      valueCreation: '',
      benefitsRealization: '',
      marketResponsiveness: '',
      planningDocuments: [],
      executiveBriefing: '',
      engagementSessions: [],
      charterEmbedding: '',
      roiThreshold: '',
      socialImpactWeight: '',
      innovationScore: '',
      marketPositioning: '',
      salesAlignment: '',
      evaluationCriteria: []
    },
    programManagement: {
      translationProcess: '',
      translationStakeholders: [],
      lifecycleStages: [],
      governanceFramework: '',
      riskManagement: '',
      resourceAllocation: '',
      priorityAssessment: '',
      feasibilityFramework: '',
      teamStructure: [],
      alignmentProcess: '',
      trackingMethodology: '',
      strategicFitWeight: '',
      costBenefitWeight: '',
      riskProfileWeight: '',
      resourceAvailabilityWeight: '',
      boardComposition: [],
      reviewProcess: '',
      approvalCriteria: '',
      intentTranslation: '',
      reviewFrequency: '',
      reviewScope: '',
      adaptationProcess: '',
      alignmentMetrics: []
    },
    parameters: {
      discountRate: 10,
      taxRate: 25,
      inflationRate: 3,
      baseCurrency: 'USD',
      exchangeRates: { EUR: 0.85, GBP: 0.73, JPY: 110 },
      inflationToggles: { rd: true, production: true, sales: false },
      projectDuration: 10
    },
    costs: {
      capex: [
        { name: 'Equipment', amount: 500000, year: 1 },
        { name: 'Facility Setup', amount: 200000, year: 1 }
      ],
      opex: [
        { name: 'Personnel', amount: 150000, recurring: true },
        { name: 'Utilities', amount: 25000, recurring: true }
      ],
      tools: [
        { name: 'Software Licenses', amount: 50000, lifespan: 3 },
        { name: 'Testing Equipment', amount: 75000, lifespan: 5 }
      ],
      machinery: [
        { name: 'Production Line A', amount: 800000, capacity: 10000, year: 1 },
        { name: 'Quality Control System', amount: 150000, capacity: 5000, year: 2 }
      ]
    },
    sales: {
      regions: [
        { name: 'North America', share: 40, growth: 5 },
        { name: 'Europe', share: 35, growth: 3 },
        { name: 'Asia Pacific', share: 25, growth: 8 }
      ],
      offers: [
        { name: 'Premium Package', price: 1200, margin: 35, volume: 5000 },
        { name: 'Standard Package', price: 800, margin: 25, volume: 8000 },
        { name: 'Basic Package', price: 500, margin: 15, volume: 12000 }
      ]
    },
    sensitivity: {
      salesPrice: { base: 1000, min: 800, max: 1200, impact: 'high' },
      salesVolume: { base: 10000, min: 8000, max: 12000, impact: 'high' },
      productionCost: { base: 600, min: 500, max: 700, impact: 'medium' },
      marketingCost: { base: 100000, min: 80000, max: 120000, impact: 'low' },
      discountRate: { base: 10, min: 8, max: 12, impact: 'medium' }
    },
    results: {},
    sensitivityResults: {},

    // Framework data
    framework: {
      currentStep: 1,
      completedSteps: [],
      projectIdentification: {
        businessProblem: 'Need to modernize legacy systems and improve operational efficiency',
        opportunityDescription: 'Digital transformation to enhance customer experience and reduce costs',
        strategicAlignment: 'Aligns with company digital strategy and growth objectives',
        stakeholders: ['CEO', 'CTO', 'Operations Director', 'Finance Director'],
        businessDrivers: ['Cost Reduction', 'Revenue Growth', 'Customer Satisfaction'],
        urgency: 'High',
        scope: 'Enterprise-wide digital transformation initiative',
        constraints: ['Budget limitations', 'Timeline constraints', 'Resource availability'],
        assumptions: ['Management support', 'Technology adoption', 'Market conditions']
      }
    },

    // Advanced modeling data
    modeling: {
      parameters: {
        revenueGrowthRate: 15,
        costInflationRate: 5,
        discountRate: 10,
        taxRate: 25,
        workingCapitalRate: 5,
        projectDuration: 5,
        currency: 'USD'
      },
      financialIndexes: {
        npv: 450000,
        irr: 18.5,
        paybackPeriod: 2.3,
        profitabilityIndex: 1.45,
        breakEvenPoint: 2.1
      }
    },

    // Excel analysis data
    excel: {
      rawData: [],
      processedData: [],
      analysisResults: {
        irr: 0,
        npv: 0,
        paybackPeriod: 0,
        roi: 0
      }
    },

    // Stakeholder data
    stakeholders: {
      stakeholders: [
        {
          id: 1,
          name: 'John Smith',
          role: 'CEO',
          department: 'Executive',
          email: '<EMAIL>',
          phone: '******-0101',
          influence: 'High',
          interest: 'High',
          engagement: 'Champion',
          priority: 'High',
          status: 'Active',
          lastContact: '2024-01-15',
          rating: 5,
          comments: []
        },
        {
          id: 2,
          name: 'Sarah Johnson',
          role: 'CTO',
          department: 'Technology',
          email: '<EMAIL>',
          phone: '******-0102',
          influence: 'High',
          interest: 'High',
          engagement: 'Supporter',
          priority: 'High',
          status: 'Active',
          lastContact: '2024-01-14',
          rating: 4,
          comments: []
        }
      ]
    },

    // PMI Compliance data
    compliance: {
      projectLifecycle: {
        initiating: { completion: 85, status: 'Completed' },
        planning: { completion: 70, status: 'In Progress' },
        executing: { completion: 0, status: 'Not Started' },
        monitoring: { completion: 0, status: 'Not Started' },
        closing: { completion: 0, status: 'Not Started' }
      },
      knowledgeAreas: {
        integration: { score: 8, status: 'Good' },
        scope: { score: 7, status: 'Good' },
        schedule: { score: 6, status: 'Needs Improvement' },
        cost: { score: 8, status: 'Good' },
        quality: { score: 7, status: 'Good' },
        resource: { score: 6, status: 'Needs Improvement' },
        communications: { score: 8, status: 'Good' },
        risk: { score: 7, status: 'Good' },
        procurement: { score: 5, status: 'Needs Improvement' },
        stakeholder: { score: 9, status: 'Excellent' }
      },
      overallCompliance: 71
    },

    // Change management data
    roadmap: {
      currentState: 'Traditional business case development with manual processes',
      futureState: 'Integrated digital platform with automated workflows',
      phases: [
        { name: 'Assessment', duration: '2 months', status: 'Completed' },
        { name: 'Design', duration: '3 months', status: 'In Progress' },
        { name: 'Implementation', duration: '6 months', status: 'Planned' },
        { name: 'Optimization', duration: '3 months', status: 'Planned' }
      ]
    },

    // Master business case data
    masterCase: {
      title: 'Digital Transformation Initiative',
      description: 'Comprehensive digital transformation project',
      status: 'In Development',
      lastUpdated: new Date().toISOString(),
      totalBudget: 2500000,
      expectedROI: 45,
      riskLevel: 'Medium'
    },

    // Manager data
    manager: {
      projects: [
        {
          id: 1,
          name: 'Digital Transformation Initiative',
          status: 'Active',
          budget: 2500000,
          completion: 35
        }
      ]
    }
  })

  const [calculator, setCalculator] = useState(null)
  const [sensitivityAnalyzer, setSensitivityAnalyzer] = useState(null)
  const [dataExporter] = useState(new DataExporter())
  const [isCalculating, setIsCalculating] = useState(false)

  // Initialize calculators when parameters change
  useEffect(() => {
    if (projectData.parameters.discountRate) {
      const calc = new FinancialCalculator(projectData.parameters)
      setCalculator(calc)
      setSensitivityAnalyzer(new SensitivityAnalyzer(calc))
    }
  }, [projectData.parameters])

  // Auto-calculate results when data changes
  useEffect(() => {
    if (calculator && Object.keys(projectData.costs).length > 0) {
      calculateResults()
    }
  }, [calculator, projectData.costs, projectData.sales])

  const calculateResults = async () => {
    if (!calculator) return

    setIsCalculating(true)
    try {
      // Calculate main financial metrics
      const results = calculator.calculateAllMetrics(projectData)

      // Run sensitivity analysis
      const sensitivityResults = sensitivityAnalyzer?.runSensitivityAnalysis(
        projectData,
        projectData.sensitivity
      )

      setProjectData(prev => ({
        ...prev,
        results,
        sensitivityResults
      }))
    } catch (error) {
      console.error('Calculation error:', error)
    } finally {
      setIsCalculating(false)
    }
  }

  const handleExport = (format) => {
    const exportData = {
      ...projectData,
      projectName: 'New Product Launch',
      generatedDate: new Date().toISOString()
    }

    switch (format) {
      case 'csv':
        dataExporter.exportToCSV(exportData)
        break
      case 'json':
        dataExporter.exportToJSON(exportData)
        break
      case 'pdf':
        dataExporter.exportToPDF(exportData)
        break
      case 'excel':
        dataExporter.exportToExcel(exportData)
        break
      default:
        console.log('Unknown export format')
    }
  }

  // Add navigation event listener for cross-component integration
  useEffect(() => {
    const handleNavigation = (event) => {
      setActiveTab(event.detail)
    }

    window.addEventListener('navigate', handleNavigation)
    return () => window.removeEventListener('navigate', handleNavigation)
  }, [])

  const tabs = [
    { id: 'strategic-vision', label: 'Strategic Vision', icon: '🎯' },
    { id: 'program-management', label: 'Program Management', icon: '👥' },
    { id: 'framework', label: 'Business Case Framework', icon: '📋' },
    { id: 'generator', label: 'AI Business Case Generator', icon: '🤖' },
    { id: 'manager', label: 'Business Case Manager', icon: '📊' },
    { id: 'master', label: 'Master Business Case', icon: '🎯' },
    { id: 'governance', label: 'PMI/PGMP Compliance', icon: '🏛️' },
    { id: 'stakeholders', label: 'Stakeholder Feedback', icon: '👥' },
    { id: 'modeling', label: 'Advanced Financial Modeling', icon: '🧮' },
    { id: 'excel', label: 'Excel Analysis & Augment AI', icon: '🔧' },
    { id: 'parameters', label: 'Financial Parameters', icon: '⚙️' },
    { id: 'costs', label: 'Cost & Sales Definition', icon: '💰' },
    { id: 'sensitivity', label: 'Sensitivity Analysis', icon: '📊' },
    { id: 'results', label: 'Financial Indexes', icon: '📈' },
    { id: 'roadmap', label: 'Change Management & Roadmap', icon: '🗺️' },
    { id: 'visualizations', label: 'Charts & Analytics', icon: '📊' },
    { id: 'cors-test', label: 'CORS Test', icon: '🔗' }
  ]

  const renderActiveComponent = () => {
    switch (activeTab) {
      case 'strategic-vision':
        return <StrategicVision
          data={projectData.strategicVision}
          onChange={(data) => setProjectData(prev => ({ ...prev, strategicVision: data }))}
        />
      case 'program-management':
        return <ProgramManagement
          data={projectData.programManagement}
          onChange={(data) => setProjectData(prev => ({ ...prev, programManagement: data }))}
        />
      case 'cors-test':
        return <CORSTest />
      case 'framework':
        return <BusinessCaseFramework
          data={projectData.framework}
          onChange={(data) => setProjectData(prev => ({ ...prev, framework: data }))}
        />
      case 'generator':
        return <BusinessCaseGenerator />
      case 'manager':
        return <MasterBusinessCaseManager
          data={projectData.manager}
          onChange={(data) => setProjectData(prev => ({ ...prev, manager: data }))}
        />
      case 'master':
        return <MasterBusinessCase
          data={projectData.masterCase}
          onChange={(data) => setProjectData(prev => ({ ...prev, masterCase: data }))}
        />
      case 'governance':
        return <EnhancedPMICompliance
          data={projectData.compliance}
          onChange={(data) => setProjectData(prev => ({ ...prev, compliance: data }))}
        />
      case 'stakeholders':
        return <EnhancedStakeholderFeedback
          data={projectData.stakeholders}
          onChange={(data) => setProjectData(prev => ({ ...prev, stakeholders: data }))}
        />
      case 'modeling':
        return <AdvancedFinancialModeling
          data={projectData.modeling}
          onChange={(data) => setProjectData(prev => ({ ...prev, modeling: data }))}
        />
      case 'excel':
        return <AugmentExcelAnalysis
          data={projectData.excel}
          onChange={(data) => setProjectData(prev => ({ ...prev, excel: data }))}
        />
      case 'parameters':
        return <ParametersSetup
          data={projectData.parameters}
          onChange={(data) => setProjectData(prev => ({ ...prev, parameters: data }))}
        />
      case 'costs':
        return <CostSalesDefinition
          data={projectData}
          onChange={(data) => setProjectData(prev => ({ ...prev, ...data }))}
        />
      case 'sensitivity':
        return <SensitivityAnalysis
          data={projectData.sensitivity}
          onChange={(data) => setProjectData(prev => ({ ...prev, sensitivity: data }))}
        />
      case 'results':
        return <FinancialIndexes data={projectData} />
      case 'roadmap':
        return <ChangeManagementRoadmap
          data={projectData.roadmap}
          onChange={(data) => setProjectData(prev => ({ ...prev, roadmap: data }))}
        />
      case 'visualizations':
        return <Visualizations data={projectData} />
      default:
        return <BusinessCaseFramework
          data={projectData.framework}
          onChange={(data) => setProjectData(prev => ({ ...prev, framework: data }))}
        />
    }
  }

  return (
    <div className="app">
      <header className="app-header">
        <div className="header-content">
          <h1>🎯 Master BC Framework - Governance Platform</h1>
          <div className="project-info">
            <span className="project-name">Program: Digital Transformation Initiative</span>
            <span className="last-saved">Last updated: {new Date().toLocaleString()}</span>
          </div>
        </div>
      </header>

      <nav className="tab-navigation">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
          >
            <span className="tab-icon">{tab.icon}</span>
            <span className="tab-label">{tab.label}</span>
          </button>
        ))}
      </nav>

      <main className="main-content">
        {isCalculating && (
          <div className="calculating-overlay">
            <div className="calculating-spinner">
              <div>⏳ Calculating Financial Metrics...</div>
              <div style={{ fontSize: '0.9rem', marginTop: '0.5rem', color: '#666' }}>
                This may take a few moments
              </div>
            </div>
          </div>
        )}
        {renderActiveComponent()}
      </main>

      <footer className="app-footer">
        <div className="footer-actions">
          <button className="btn btn-secondary" onClick={() => localStorage.setItem('businessCaseData', JSON.stringify(projectData))}>
            💾 Save Draft
          </button>
          <button
            className="btn btn-primary"
            onClick={calculateResults}
            disabled={isCalculating}
          >
            {isCalculating ? '⏳ Calculating...' : '🔄 Calculate Results'}
          </button>
          <div className="export-dropdown">
            <button className="btn btn-success">📊 Export Report ▼</button>
            <div className="export-menu">
              <button onClick={() => handleExport('pdf')}>📄 PDF Report</button>
              <button onClick={() => handleExport('csv')}>📊 CSV Data</button>
              <button onClick={() => handleExport('json')}>📋 JSON Data</button>
              <button onClick={() => handleExport('excel')}>📈 Excel File</button>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default App
