import React, { useState } from 'react'

const EnhancedPMICompliance = ({ data, onChange }) => {
  const [activeSection, setActiveSection] = useState('overview')
  const [complianceData, setComplianceData] = useState({
    overview: {
      overallCompliance: 78,
      projectPhase: 'Planning',
      lastAssessment: '2024-03-20',
      nextReview: '2024-04-15',
      complianceLevel: 'Good',
      criticalIssues: 3,
      recommendations: 8
    },
    projectLifecycle: {
      currentPhase: 'Planning',
      phases: [
        {
          name: 'Initiating',
          status: 'Completed',
          progress: 100,
          compliance: 95,
          startDate: '2024-01-01',
          endDate: '2024-01-31',
          processes: [
            { name: 'Develop Project Charter', status: 'Completed', compliance: 100 },
            { name: 'Identify Stakeholders', status: 'Completed', compliance: 90 }
          ],
          gates: [
            { name: 'Project Authorization Gate', status: 'Passed', date: '2024-01-15', approver: 'Steering Committee' }
          ]
        },
        {
          name: 'Planning',
          status: 'In Progress',
          progress: 65,
          compliance: 78,
          startDate: '2024-02-01',
          endDate: '2024-03-31',
          processes: [
            { name: 'Develop Project Management Plan', status: 'In Progress', compliance: 80 },
            { name: 'Plan Scope Management', status: 'Completed', compliance: 85 },
            { name: 'Collect Requirements', status: 'In Progress', compliance: 70 },
            { name: 'Define Scope', status: 'Completed', compliance: 90 },
            { name: 'Create WBS', status: 'In Progress', compliance: 75 },
            { name: 'Plan Schedule Management', status: 'In Progress', compliance: 65 },
            { name: 'Plan Cost Management', status: 'Completed', compliance: 95 },
            { name: 'Plan Quality Management', status: 'In Progress', compliance: 60 },
            { name: 'Plan Resource Management', status: 'Completed', compliance: 85 },
            { name: 'Plan Communications Management', status: 'Completed', compliance: 80 },
            { name: 'Plan Risk Management', status: 'Completed', compliance: 90 },
            { name: 'Plan Procurement Management', status: 'In Progress', compliance: 55 },
            { name: 'Plan Stakeholder Engagement', status: 'In Progress', compliance: 75 }
          ],
          gates: [
            { name: 'Planning Approval Gate', status: 'Pending', date: 'TBD', approver: 'Project Sponsor' }
          ]
        },
        {
          name: 'Executing',
          status: 'Not Started',
          progress: 0,
          compliance: 0,
          startDate: '2024-04-01',
          endDate: '2024-10-31',
          processes: [
            { name: 'Direct and Manage Project Work', status: 'Not Started', compliance: 0 },
            { name: 'Manage Project Knowledge', status: 'Not Started', compliance: 0 },
            { name: 'Manage Quality', status: 'Not Started', compliance: 0 },
            { name: 'Acquire Resources', status: 'Not Started', compliance: 0 },
            { name: 'Develop Team', status: 'Not Started', compliance: 0 },
            { name: 'Manage Team', status: 'Not Started', compliance: 0 },
            { name: 'Manage Communications', status: 'Not Started', compliance: 0 },
            { name: 'Implement Risk Responses', status: 'Not Started', compliance: 0 },
            { name: 'Conduct Procurements', status: 'Not Started', compliance: 0 },
            { name: 'Manage Stakeholder Engagement', status: 'Not Started', compliance: 0 }
          ],
          gates: []
        },
        {
          name: 'Monitoring & Controlling',
          status: 'Not Started',
          progress: 0,
          compliance: 0,
          startDate: '2024-04-01',
          endDate: '2024-10-31',
          processes: [
            { name: 'Monitor and Control Project Work', status: 'Not Started', compliance: 0 },
            { name: 'Perform Integrated Change Control', status: 'Not Started', compliance: 0 },
            { name: 'Validate Scope', status: 'Not Started', compliance: 0 },
            { name: 'Control Scope', status: 'Not Started', compliance: 0 },
            { name: 'Control Schedule', status: 'Not Started', compliance: 0 },
            { name: 'Control Costs', status: 'Not Started', compliance: 0 },
            { name: 'Control Quality', status: 'Not Started', compliance: 0 },
            { name: 'Control Resources', status: 'Not Started', compliance: 0 },
            { name: 'Monitor Communications', status: 'Not Started', compliance: 0 },
            { name: 'Monitor Risks', status: 'Not Started', compliance: 0 },
            { name: 'Control Procurements', status: 'Not Started', compliance: 0 },
            { name: 'Monitor Stakeholder Engagement', status: 'Not Started', compliance: 0 }
          ],
          gates: []
        },
        {
          name: 'Closing',
          status: 'Not Started',
          progress: 0,
          compliance: 0,
          startDate: '2024-11-01',
          endDate: '2024-11-30',
          processes: [
            { name: 'Close Project or Phase', status: 'Not Started', compliance: 0 }
          ],
          gates: [
            { name: 'Project Closure Gate', status: 'Not Started', date: 'TBD', approver: 'Steering Committee' }
          ]
        }
      ]
    },
    knowledgeAreas: [
      {
        area: 'Integration Management',
        compliance: 85,
        status: 'Good',
        processGroups: {
          initiating: { processes: 1, completed: 1, compliance: 100 },
          planning: { processes: 1, completed: 0, compliance: 80 },
          executing: { processes: 2, completed: 0, compliance: 0 },
          monitoring: { processes: 2, completed: 0, compliance: 0 },
          closing: { processes: 1, completed: 0, compliance: 0 }
        },
        artifacts: ['Project Charter', 'Project Management Plan', 'Change Requests', 'Project Documents Updates'],
        issues: ['Change control process needs improvement'],
        recommendations: ['Implement formal change control board', 'Establish change request templates']
      },
      {
        area: 'Scope Management',
        compliance: 78,
        status: 'Good',
        processGroups: {
          initiating: { processes: 0, completed: 0, compliance: 0 },
          planning: { processes: 4, completed: 2, compliance: 75 },
          executing: { processes: 0, completed: 0, compliance: 0 },
          monitoring: { processes: 2, completed: 0, compliance: 0 },
          closing: { processes: 0, completed: 0, compliance: 0 }
        },
        artifacts: ['Scope Management Plan', 'Requirements Documentation', 'Scope Statement', 'WBS', 'WBS Dictionary'],
        issues: ['Requirements traceability matrix incomplete'],
        recommendations: ['Complete requirements traceability matrix', 'Implement scope change control']
      },
      {
        area: 'Schedule Management',
        compliance: 72,
        status: 'Needs Attention',
        processGroups: {
          initiating: { processes: 0, completed: 0, compliance: 0 },
          planning: { processes: 5, completed: 1, compliance: 65 },
          executing: { processes: 0, completed: 0, compliance: 0 },
          monitoring: { processes: 1, completed: 0, compliance: 0 },
          closing: { processes: 0, completed: 0, compliance: 0 }
        },
        artifacts: ['Schedule Management Plan', 'Activity List', 'Activity Attributes', 'Milestone List', 'Project Schedule'],
        issues: ['Schedule baseline not established', 'Critical path analysis pending'],
        recommendations: ['Complete activity sequencing', 'Establish schedule baseline', 'Implement schedule monitoring tools']
      },
      {
        area: 'Cost Management',
        compliance: 90,
        status: 'Excellent',
        processGroups: {
          initiating: { processes: 0, completed: 0, compliance: 0 },
          planning: { processes: 3, completed: 2, compliance: 90 },
          executing: { processes: 0, completed: 0, compliance: 0 },
          monitoring: { processes: 1, completed: 0, compliance: 0 },
          closing: { processes: 0, completed: 0, compliance: 0 }
        },
        artifacts: ['Cost Management Plan', 'Cost Estimates', 'Cost Baseline', 'Project Funding Requirements'],
        issues: [],
        recommendations: ['Implement earned value management', 'Set up cost performance reporting']
      },
      {
        area: 'Quality Management',
        compliance: 68,
        status: 'Needs Attention',
        processGroups: {
          initiating: { processes: 0, completed: 0, compliance: 0 },
          planning: { processes: 1, completed: 0, compliance: 60 },
          executing: { processes: 1, completed: 0, compliance: 0 },
          monitoring: { processes: 1, completed: 0, compliance: 0 },
          closing: { processes: 0, completed: 0, compliance: 0 }
        },
        artifacts: ['Quality Management Plan', 'Quality Metrics', 'Quality Checklists', 'Process Improvement Plan'],
        issues: ['Quality metrics not defined', 'Quality assurance process incomplete'],
        recommendations: ['Define quality metrics', 'Establish quality assurance procedures', 'Implement quality control checkpoints']
      },
      {
        area: 'Resource Management',
        compliance: 82,
        status: 'Good',
        processGroups: {
          initiating: { processes: 0, completed: 0, compliance: 0 },
          planning: { processes: 2, completed: 1, compliance: 85 },
          executing: { processes: 3, completed: 0, compliance: 0 },
          monitoring: { processes: 1, completed: 0, compliance: 0 },
          closing: { processes: 0, completed: 0, compliance: 0 }
        },
        artifacts: ['Resource Management Plan', 'Team Charter', 'Resource Calendar', 'Team Performance Assessments'],
        issues: ['Resource allocation conflicts pending resolution'],
        recommendations: ['Complete resource leveling', 'Establish team development plan']
      },
      {
        area: 'Communications Management',
        compliance: 75,
        status: 'Good',
        processGroups: {
          initiating: { processes: 0, completed: 0, compliance: 0 },
          planning: { processes: 1, completed: 1, compliance: 80 },
          executing: { processes: 1, completed: 0, compliance: 0 },
          monitoring: { processes: 1, completed: 0, compliance: 0 },
          closing: { processes: 0, completed: 0, compliance: 0 }
        },
        artifacts: ['Communications Management Plan', 'Project Communications', 'Project Reports'],
        issues: ['Stakeholder communication frequency needs improvement'],
        recommendations: ['Implement regular stakeholder updates', 'Establish communication templates']
      },
      {
        area: 'Risk Management',
        compliance: 88,
        status: 'Excellent',
        processGroups: {
          initiating: { processes: 0, completed: 0, compliance: 0 },
          planning: { processes: 4, completed: 3, compliance: 90 },
          executing: { processes: 1, completed: 0, compliance: 0 },
          monitoring: { processes: 1, completed: 0, compliance: 0 },
          closing: { processes: 0, completed: 0, compliance: 0 }
        },
        artifacts: ['Risk Management Plan', 'Risk Register', 'Risk Report', 'Risk Response Plans'],
        issues: [],
        recommendations: ['Implement risk monitoring dashboard', 'Conduct regular risk reviews']
      },
      {
        area: 'Procurement Management',
        compliance: 65,
        status: 'Needs Attention',
        processGroups: {
          initiating: { processes: 0, completed: 0, compliance: 0 },
          planning: { processes: 1, completed: 0, compliance: 55 },
          executing: { processes: 1, completed: 0, compliance: 0 },
          monitoring: { processes: 1, completed: 0, compliance: 0 },
          closing: { processes: 0, completed: 0, compliance: 0 }
        },
        artifacts: ['Procurement Management Plan', 'Procurement Documents', 'Source Selection Criteria'],
        issues: ['Procurement strategy not finalized', 'Vendor evaluation criteria incomplete'],
        recommendations: ['Complete procurement planning', 'Establish vendor evaluation framework', 'Define contract management procedures']
      },
      {
        area: 'Stakeholder Management',
        compliance: 80,
        status: 'Good',
        processGroups: {
          initiating: { processes: 1, completed: 1, compliance: 90 },
          planning: { processes: 1, completed: 0, compliance: 75 },
          executing: { processes: 1, completed: 0, compliance: 0 },
          monitoring: { processes: 1, completed: 0, compliance: 0 },
          closing: { processes: 0, completed: 0, compliance: 0 }
        },
        artifacts: ['Stakeholder Register', 'Stakeholder Engagement Plan', 'Stakeholder Engagement Assessment Matrix'],
        issues: ['Stakeholder engagement levels need monitoring'],
        recommendations: ['Implement stakeholder engagement tracking', 'Regular stakeholder satisfaction surveys']
      }
    ],
    ...data
  })

  const sections = [
    { id: 'overview', label: 'Compliance Overview', icon: '📊' },
    { id: 'lifecycle', label: 'Project Lifecycle', icon: '🔄' },
    { id: 'knowledge', label: 'Knowledge Areas', icon: '📚' },
    { id: 'pgmp', label: 'PGMP Framework', icon: '🏛️' },
    { id: 'artifacts', label: 'Artifacts & Documents', icon: '📋' },
    { id: 'assessment', label: 'Assessment & Actions', icon: '✅' }
  ]

  const getComplianceColor = (score) => {
    if (score >= 90) return '#28a745'
    if (score >= 80) return '#17a2b8'
    if (score >= 70) return '#ffc107'
    if (score >= 60) return '#fd7e14'
    return '#dc3545'
  }

  const getStatusColor = (status) => {
    const colors = {
      'Completed': '#28a745',
      'In Progress': '#17a2b8',
      'Not Started': '#6c757d',
      'Needs Attention': '#fd7e14',
      'Excellent': '#28a745',
      'Good': '#17a2b8',
      'Pending': '#ffc107',
      'Passed': '#28a745'
    }
    return colors[status] || '#6c757d'
  }

  const renderOverview = () => (
    <div className="compliance-overview">
      <div className="overview-metrics">
        <div className="metric-card">
          <div className="metric-icon">📊</div>
          <div className="metric-content">
            <div className="metric-value">{complianceData.overview.overallCompliance}%</div>
            <div className="metric-label">Overall Compliance</div>
            <div className="metric-status" style={{ color: getComplianceColor(complianceData.overview.overallCompliance) }}>
              {complianceData.overview.complianceLevel}
            </div>
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-icon">🎯</div>
          <div className="metric-content">
            <div className="metric-value">{complianceData.overview.projectPhase}</div>
            <div className="metric-label">Current Phase</div>
            <div className="metric-status">Active</div>
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-icon">⚠️</div>
          <div className="metric-content">
            <div className="metric-value">{complianceData.overview.criticalIssues}</div>
            <div className="metric-label">Critical Issues</div>
            <div className="metric-status" style={{ color: '#fd7e14' }}>Needs Attention</div>
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-icon">💡</div>
          <div className="metric-content">
            <div className="metric-value">{complianceData.overview.recommendations}</div>
            <div className="metric-label">Recommendations</div>
            <div className="metric-status">Action Items</div>
          </div>
        </div>
      </div>

      <div className="compliance-summary">
        <h4>📈 Compliance Summary by Knowledge Area</h4>
        <div className="knowledge-areas-grid">
          {complianceData.knowledgeAreas.map((area, index) => (
            <div key={index} className="knowledge-area-summary">
              <div className="area-header">
                <span className="area-name">{area.area}</span>
                <span
                  className="area-score"
                  style={{ color: getComplianceColor(area.compliance) }}
                >
                  {area.compliance}%
                </span>
              </div>
              <div className="compliance-bar">
                <div
                  className="compliance-fill"
                  style={{
                    width: `${area.compliance}%`,
                    backgroundColor: getComplianceColor(area.compliance)
                  }}
                ></div>
              </div>
              <div className="area-status" style={{ color: getStatusColor(area.status) }}>
                {area.status}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )

  const renderLifecycle = () => (
    <div className="lifecycle-view">
      <h3>🔄 Project Lifecycle Management</h3>
      <div className="lifecycle-phases">
        {complianceData.projectLifecycle.phases.map((phase, index) => (
          <div key={index} className={`phase-card ${phase.status.toLowerCase().replace(' ', '-')}`}>
            <div className="phase-header">
              <div className="phase-info">
                <h4>{phase.name}</h4>
                <span className="phase-dates">{phase.startDate} - {phase.endDate}</span>
              </div>
              <div className="phase-status">
                <span
                  className="status-badge"
                  style={{ backgroundColor: getStatusColor(phase.status) }}
                >
                  {phase.status}
                </span>
                <span className="compliance-score" style={{ color: getComplianceColor(phase.compliance) }}>
                  {phase.compliance}%
                </span>
              </div>
            </div>

            <div className="phase-progress">
              <div className="progress-bar">
                <div
                  className="progress-fill"
                  style={{
                    width: `${phase.progress}%`,
                    backgroundColor: getStatusColor(phase.status)
                  }}
                ></div>
              </div>
              <span className="progress-text">{phase.progress}% Complete</span>
            </div>

            <div className="phase-processes">
              <h5>📋 Key Processes:</h5>
              <div className="processes-grid">
                {phase.processes.map((process, pIndex) => (
                  <div key={pIndex} className="process-item">
                    <span className="process-name">{process.name}</span>
                    <span
                      className="process-status"
                      style={{ color: getStatusColor(process.status) }}
                    >
                      {process.status} ({process.compliance}%)
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {phase.gates.length > 0 && (
              <div className="phase-gates">
                <h5>🚪 Decision Gates:</h5>
                {phase.gates.map((gate, gIndex) => (
                  <div key={gIndex} className="gate-item">
                    <span className="gate-name">{gate.name}</span>
                    <span
                      className="gate-status"
                      style={{ color: getStatusColor(gate.status) }}
                    >
                      {gate.status}
                    </span>
                    <span className="gate-date">{gate.date}</span>
                    {gate.approver && <span className="gate-approver">Approver: {gate.approver}</span>}
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )

  const renderKnowledgeAreas = () => (
    <div className="knowledge-areas-view">
      <h3>📚 PMI Knowledge Areas Compliance</h3>
      <div className="knowledge-areas-detailed">
        {complianceData.knowledgeAreas.map((area, index) => (
          <div key={index} className="knowledge-area-card">
            <div className="area-header-detailed">
              <h4>{area.area}</h4>
              <div className="area-metrics">
                <span
                  className="compliance-score"
                  style={{ color: getComplianceColor(area.compliance) }}
                >
                  {area.compliance}%
                </span>
                <span
                  className="status-badge"
                  style={{ backgroundColor: getStatusColor(area.status) }}
                >
                  {area.status}
                </span>
              </div>
            </div>

            <div className="process-groups-summary">
              <h5>Process Groups Status:</h5>
              <div className="process-groups-grid">
                {Object.entries(area.processGroups).map(([group, data]) => (
                  <div key={group} className="process-group-item">
                    <span className="group-name">{group.charAt(0).toUpperCase() + group.slice(1)}</span>
                    <span className="group-progress">{data.completed}/{data.processes}</span>
                    <span
                      className="group-compliance"
                      style={{ color: getComplianceColor(data.compliance) }}
                    >
                      {data.compliance}%
                    </span>
                  </div>
                ))}
              </div>
            </div>

            <div className="artifacts-list">
              <h5>📄 Key Artifacts:</h5>
              <div className="artifacts-tags">
                {area.artifacts.map((artifact, aIndex) => (
                  <span key={aIndex} className="artifact-tag">{artifact}</span>
                ))}
              </div>
            </div>

            {area.issues.length > 0 && (
              <div className="issues-section">
                <h5>⚠️ Issues:</h5>
                <ul className="issues-list">
                  {area.issues.map((issue, iIndex) => (
                    <li key={iIndex} className="issue-item">{issue}</li>
                  ))}
                </ul>
              </div>
            )}

            <div className="recommendations-section">
              <h5>💡 Recommendations:</h5>
              <ul className="recommendations-list">
                {area.recommendations.map((rec, rIndex) => (
                  <li key={rIndex} className="recommendation-item">{rec}</li>
                ))}
              </ul>
            </div>
          </div>
        ))}
      </div>
    </div>
  )

  const renderPGMPFramework = () => (
    <div className="pgmp-framework-view">
      <h3>🏛️ PGMP Framework Compliance</h3>

      <div className="pgmp-domains">
        <div className="domain-card">
          <h4>📈 Program Strategy Alignment</h4>
          <div className="strategy-metrics">
            <div className="strategy-item">
              <span>Strategic Alignment</span>
              <span className="score">85%</span>
              <span className="status good">Good</span>
            </div>
            <div className="strategy-item">
              <span>Benefits Realization</span>
              <span className="score">78%</span>
              <span className="status good">Good</span>
            </div>
            <div className="strategy-item">
              <span>Stakeholder Engagement</span>
              <span className="score">82%</span>
              <span className="status good">Good</span>
            </div>
            <div className="strategy-item">
              <span>Program Roadmap</span>
              <span className="score">75%</span>
              <span className="status good">Good</span>
            </div>
          </div>
        </div>

        <div className="domain-card">
          <h4>🔄 Program Lifecycle</h4>
          <div className="lifecycle-phases-pgmp">
            <div className="pgmp-phase completed">
              <div className="phase-header-pgmp">
                <span className="phase-name">Program Definition</span>
                <span className="phase-score">90%</span>
              </div>
              <div className="phase-status">Completed</div>
              <div className="phase-artifacts">
                <span>Program Charter</span>
                <span>Program Roadmap</span>
                <span>Program Architecture</span>
              </div>
            </div>

            <div className="pgmp-phase in-progress">
              <div className="phase-header-pgmp">
                <span className="phase-name">Benefits Delivery</span>
                <span className="phase-score">65%</span>
              </div>
              <div className="phase-status">In Progress</div>
              <div className="phase-artifacts">
                <span>Benefits Realization Plan</span>
                <span>Component Transition Plans</span>
              </div>
            </div>

            <div className="pgmp-phase not-started">
              <div className="phase-header-pgmp">
                <span className="phase-name">Program Closure</span>
                <span className="phase-score">0%</span>
              </div>
              <div className="phase-status">Not Started</div>
              <div className="phase-artifacts">
                <span>Program Closure Report</span>
                <span>Lessons Learned</span>
              </div>
            </div>
          </div>
        </div>

        <div className="domain-card">
          <h4>🛠️ Supporting Activities</h4>
          <div className="supporting-activities-grid">
            <div className="activity-item">
              <span className="activity-name">Program Governance</span>
              <span className="activity-score">88%</span>
              <span className="activity-status excellent">Excellent</span>
            </div>
            <div className="activity-item">
              <span className="activity-name">Stakeholder Engagement</span>
              <span className="activity-score">80%</span>
              <span className="activity-status good">Good</span>
            </div>
            <div className="activity-item">
              <span className="activity-name">Communications</span>
              <span className="activity-score">75%</span>
              <span className="activity-status good">Good</span>
            </div>
            <div className="activity-item">
              <span className="activity-name">Financial Management</span>
              <span className="activity-score">92%</span>
              <span className="activity-status excellent">Excellent</span>
            </div>
            <div className="activity-item">
              <span className="activity-name">Quality Management</span>
              <span className="activity-score">70%</span>
              <span className="activity-status needs-attention">Needs Attention</span>
            </div>
            <div className="activity-item">
              <span className="activity-name">Resource Management</span>
              <span className="activity-score">77%</span>
              <span className="activity-status good">Good</span>
            </div>
            <div className="activity-item">
              <span className="activity-name">Risk Management</span>
              <span className="activity-score">85%</span>
              <span className="activity-status excellent">Excellent</span>
            </div>
            <div className="activity-item">
              <span className="activity-name">Procurement Management</span>
              <span className="activity-score">73%</span>
              <span className="activity-status good">Good</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  const renderArtifacts = () => (
    <div className="artifacts-view">
      <h3>📋 Artifacts & Documents Status</h3>
      <div className="artifacts-grid">
        <div className="artifact-card approved">
          <div className="artifact-header">
            <span className="artifact-name">Project Charter</span>
            <span className="artifact-status">Approved</span>
          </div>
          <div className="artifact-details">
            <div className="artifact-info">
              <span>Version: 1.0</span>
              <span>Owner: Project Manager</span>
              <span>Last Updated: 2024-01-15</span>
            </div>
          </div>
        </div>

        <div className="artifact-card in-review">
          <div className="artifact-header">
            <span className="artifact-name">Project Management Plan</span>
            <span className="artifact-status">In Review</span>
          </div>
          <div className="artifact-details">
            <div className="artifact-info">
              <span>Version: 0.8</span>
              <span>Owner: Project Manager</span>
              <span>Last Updated: 2024-03-10</span>
            </div>
          </div>
        </div>

        <div className="artifact-card approved">
          <div className="artifact-header">
            <span className="artifact-name">Stakeholder Register</span>
            <span className="artifact-status">Approved</span>
          </div>
          <div className="artifact-details">
            <div className="artifact-info">
              <span>Version: 1.2</span>
              <span>Owner: Business Analyst</span>
              <span>Last Updated: 2024-01-20</span>
            </div>
          </div>
        </div>

        <div className="artifact-card in-progress">
          <div className="artifact-header">
            <span className="artifact-name">Risk Register</span>
            <span className="artifact-status">In Progress</span>
          </div>
          <div className="artifact-details">
            <div className="artifact-info">
              <span>Version: 0.5</span>
              <span>Owner: Risk Manager</span>
              <span>Last Updated: 2024-03-15</span>
            </div>
          </div>
        </div>

        <div className="artifact-card draft">
          <div className="artifact-header">
            <span className="artifact-name">Requirements Document</span>
            <span className="artifact-status">Draft</span>
          </div>
          <div className="artifact-details">
            <div className="artifact-info">
              <span>Version: 0.3</span>
              <span>Owner: Business Analyst</span>
              <span>Last Updated: 2024-03-08</span>
            </div>
          </div>
        </div>

        <div className="artifact-card approved">
          <div className="artifact-header">
            <span className="artifact-name">Program Charter</span>
            <span className="artifact-status">Approved</span>
          </div>
          <div className="artifact-details">
            <div className="artifact-info">
              <span>Version: 1.0</span>
              <span>Owner: Program Manager</span>
              <span>Last Updated: 2024-01-10</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  const renderAssessment = () => (
    <div className="assessment-view">
      <h3>✅ Assessment & Action Items</h3>

      <div className="assessment-summary">
        <div className="summary-card critical">
          <h4>🚨 Critical Issues (3)</h4>
          <ul>
            <li>Schedule baseline not established</li>
            <li>Quality metrics not defined</li>
            <li>Procurement strategy incomplete</li>
          </ul>
        </div>

        <div className="summary-card warning">
          <h4>⚠️ Areas Needing Attention (5)</h4>
          <ul>
            <li>Requirements traceability matrix</li>
            <li>Resource allocation conflicts</li>
            <li>Stakeholder communication frequency</li>
            <li>Quality assurance process</li>
            <li>Vendor evaluation criteria</li>
          </ul>
        </div>

        <div className="summary-card success">
          <h4>✅ Strengths (4)</h4>
          <ul>
            <li>Strong cost management processes</li>
            <li>Excellent risk management framework</li>
            <li>Well-defined program governance</li>
            <li>Comprehensive stakeholder identification</li>
          </ul>
        </div>
      </div>

      <div className="action-items">
        <h4>📋 Recommended Actions</h4>
        <div className="actions-grid">
          <div className="action-item high-priority">
            <div className="action-header">
              <span className="action-title">Establish Schedule Baseline</span>
              <span className="action-priority">High Priority</span>
            </div>
            <div className="action-details">
              <span>Due: 2024-03-30</span>
              <span>Owner: Project Manager</span>
              <span>Knowledge Area: Schedule Management</span>
            </div>
          </div>

          <div className="action-item medium-priority">
            <div className="action-header">
              <span className="action-title">Define Quality Metrics</span>
              <span className="action-priority">Medium Priority</span>
            </div>
            <div className="action-details">
              <span>Due: 2024-04-15</span>
              <span>Owner: Quality Manager</span>
              <span>Knowledge Area: Quality Management</span>
            </div>
          </div>

          <div className="action-item high-priority">
            <div className="action-header">
              <span className="action-title">Complete Procurement Planning</span>
              <span className="action-priority">High Priority</span>
            </div>
            <div className="action-details">
              <span>Due: 2024-04-01</span>
              <span>Owner: Procurement Manager</span>
              <span>Knowledge Area: Procurement Management</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <div className="enhanced-pmi-compliance">
      <div className="compliance-header">
        <h2>🏛️ PMI Guidelines & PGMP Framework Compliance</h2>
        <div className="compliance-info">
          <span>Last Assessment: {complianceData.overview.lastAssessment}</span>
          <span>Next Review: {complianceData.overview.nextReview}</span>
        </div>
      </div>

      <div className="compliance-navigation">
        {sections.map((section) => (
          <button
            key={section.id}
            className={`compliance-nav-btn ${activeSection === section.id ? 'active' : ''}`}
            onClick={() => setActiveSection(section.id)}
          >
            <span className="nav-icon">{section.icon}</span>
            <span className="nav-label">{section.label}</span>
          </button>
        ))}
      </div>

      <div className="compliance-content">
        {activeSection === 'overview' && renderOverview()}
        {activeSection === 'lifecycle' && renderLifecycle()}
        {activeSection === 'knowledge' && renderKnowledgeAreas()}
        {activeSection === 'pgmp' && renderPGMPFramework()}
        {activeSection === 'artifacts' && renderArtifacts()}
        {activeSection === 'assessment' && renderAssessment()}
      </div>
    </div>
  )
}

export default EnhancedPMICompliance
