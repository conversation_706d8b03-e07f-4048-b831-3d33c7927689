// Mock Data Service - Simulates MongoDB CRUD Operations
// This demonstrates how the real MongoDB integration would work

class MockDataService {
  constructor() {
    this.businessCases = this.generateSampleData();
    this.nextId = this.businessCases.length + 1;
  }

  // Generate sample Master Business Cases
  generateSampleData() {
    return [
      {
        _id: '1',
        programInfo: {
          programName: 'Digital Transformation Initiative 2024',
          programManager: '<PERSON>',
          sponsor: '<PERSON> (CEO)',
          strategicAlignment: 'High',
          businessDriver: 'Digital Transformation',
          investmentCategory: 'Strategic',
          totalBudget: 2500000,
          duration: 18,
          startDate: '2024-01-15',
          endDate: '2025-07-15',
          currency: 'USD'
        },
        governance: {
          steeringCommittee: [
            { name: '<PERSON>', role: 'CEO', email: '<EMAIL>' },
            { name: '<PERSON>', role: 'CTO', email: '<EMAIL>' }
          ],
          decisionGates: [],
          riskTolerance: 'Medium',
          complianceFramework: ['PMI', 'PGMP']
        },
        useCases: [
          {
            id: 'UC001',
            name: 'Customer Portal Enhancement',
            priority: 'High',
            businessValue: 'Very High',
            complexity: 'Medium',
            budget: 800000,
            timeline: 8,
            status: 'Development',
            roi: 25.5,
            assignedTo: 'Development Team A'
          },
          {
            id: 'UC002',
            name: 'Mobile Application Development',
            priority: 'High',
            businessValue: 'High',
            complexity: 'High',
            budget: 1200000,
            timeline: 12,
            status: 'Planning',
            roi: 18.2,
            assignedTo: 'Development Team B'
          }
        ],
        milestones: [
          {
            id: 'MS001',
            name: 'Project Initiation',
            description: 'Project kickoff and team setup',
            targetDate: '2024-02-01',
            actualDate: '2024-01-28',
            status: 'Completed',
            budgetAllocated: 100000,
            actualCost: 95000,
            progress: 100,
            approvals: [
              { stakeholder: 'John Smith', role: 'CEO', status: 'Approved', date: '2024-01-25' },
              { stakeholder: 'Sarah Johnson', role: 'CTO', status: 'Approved', date: '2024-01-26' }
            ]
          },
          {
            id: 'MS002',
            name: 'Development Phase',
            description: 'Core development activities',
            targetDate: '2024-08-01',
            status: 'In Progress',
            budgetAllocated: 1500000,
            actualCost: 1200000,
            progress: 75,
            approvals: [
              { stakeholder: 'Sarah Johnson', role: 'CTO', status: 'Approved', date: '2024-03-15' },
              { stakeholder: 'Mike Chen', role: 'Head of Sales', status: 'Pending' }
            ]
          }
        ],
        financialMetrics: {
          totalInvestment: 2000000,
          expectedROI: 21.85,
          npv: 450000,
          irr: 15.2,
          paybackPeriod: 3.2,
          yieldIndex: 1.23
        },
        status: 'Active',
        createdBy: 'Sarah Johnson',
        lastModifiedBy: 'Sarah Johnson',
        tags: ['digital-transformation', 'customer-experience', 'mobile'],
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-03-20T14:30:00Z'
      },
      {
        _id: '2',
        programInfo: {
          programName: 'Data Analytics Platform Upgrade',
          programManager: 'Michael Chen',
          sponsor: 'Lisa Rodriguez (CFO)',
          strategicAlignment: 'High',
          businessDriver: 'Innovation',
          investmentCategory: 'Infrastructure',
          totalBudget: 1800000,
          duration: 12,
          startDate: '2024-02-01',
          endDate: '2025-02-01',
          currency: 'USD'
        },
        governance: {
          steeringCommittee: [
            { name: 'Lisa Rodriguez', role: 'CFO', email: '<EMAIL>' },
            { name: 'Michael Chen', role: 'Data Director', email: '<EMAIL>' }
          ],
          decisionGates: [],
          riskTolerance: 'Low',
          complianceFramework: ['PMI', 'ISO 21500']
        },
        useCases: [
          {
            id: 'UC003',
            name: 'Real-time Analytics Dashboard',
            priority: 'High',
            businessValue: 'Very High',
            complexity: 'High',
            budget: 900000,
            timeline: 8,
            status: 'Development',
            roi: 32.1,
            assignedTo: 'Analytics Team'
          },
          {
            id: 'UC004',
            name: 'Predictive Modeling Engine',
            priority: 'Medium',
            businessValue: 'High',
            complexity: 'Very High',
            budget: 700000,
            timeline: 10,
            status: 'Analysis',
            roi: 28.5,
            assignedTo: 'Data Science Team'
          }
        ],
        milestones: [
          {
            id: 'MS003',
            name: 'Infrastructure Setup',
            description: 'Cloud infrastructure and data pipeline setup',
            targetDate: '2024-04-01',
            status: 'In Progress',
            budgetAllocated: 400000,
            actualCost: 350000,
            progress: 60,
            approvals: [
              { stakeholder: 'Lisa Rodriguez', role: 'CFO', status: 'Approved', date: '2024-02-10' }
            ]
          }
        ],
        financialMetrics: {
          totalInvestment: 1600000,
          expectedROI: 30.3,
          npv: 520000,
          irr: 18.7,
          paybackPeriod: 2.8,
          yieldIndex: 1.35
        },
        status: 'Active',
        createdBy: 'Michael Chen',
        lastModifiedBy: 'Michael Chen',
        tags: ['analytics', 'data-science', 'infrastructure'],
        createdAt: '2024-02-01T09:00:00Z',
        updatedAt: '2024-03-18T11:15:00Z'
      },
      {
        _id: '3',
        programInfo: {
          programName: 'Customer Experience Enhancement',
          programManager: 'Emily Davis',
          sponsor: 'Robert Wilson (CMO)',
          strategicAlignment: 'Medium',
          businessDriver: 'Customer Experience',
          investmentCategory: 'Strategic',
          totalBudget: 1200000,
          duration: 15,
          startDate: '2024-03-01',
          endDate: '2025-06-01',
          currency: 'USD'
        },
        governance: {
          steeringCommittee: [
            { name: 'Robert Wilson', role: 'CMO', email: '<EMAIL>' },
            { name: 'Emily Davis', role: 'Customer Success Director', email: '<EMAIL>' }
          ],
          decisionGates: [],
          riskTolerance: 'Medium',
          complianceFramework: ['PMI', 'Agile']
        },
        useCases: [
          {
            id: 'UC005',
            name: 'Omnichannel Support Platform',
            priority: 'High',
            businessValue: 'High',
            complexity: 'Medium',
            budget: 600000,
            timeline: 9,
            status: 'Planning',
            roi: 22.8,
            assignedTo: 'Customer Success Team'
          },
          {
            id: 'UC006',
            name: 'AI-Powered Chatbot',
            priority: 'Medium',
            businessValue: 'Medium',
            complexity: 'High',
            budget: 400000,
            timeline: 6,
            status: 'Concept',
            roi: 15.3,
            assignedTo: 'AI Development Team'
          }
        ],
        milestones: [
          {
            id: 'MS004',
            name: 'Requirements Gathering',
            description: 'Stakeholder interviews and requirements documentation',
            targetDate: '2024-04-15',
            status: 'In Progress',
            budgetAllocated: 150000,
            actualCost: 120000,
            progress: 80,
            approvals: [
              { stakeholder: 'Robert Wilson', role: 'CMO', status: 'Approved', date: '2024-03-05' }
            ]
          }
        ],
        financialMetrics: {
          totalInvestment: 1000000,
          expectedROI: 19.05,
          npv: 280000,
          irr: 12.4,
          paybackPeriod: 4.1,
          yieldIndex: 1.15
        },
        status: 'Under Review',
        createdBy: 'Emily Davis',
        lastModifiedBy: 'Emily Davis',
        tags: ['customer-experience', 'support', 'ai'],
        createdAt: '2024-03-01T08:30:00Z',
        updatedAt: '2024-03-22T16:45:00Z'
      },
      {
        _id: '4',
        programInfo: {
          programName: 'Cybersecurity Infrastructure Upgrade',
          programManager: 'David Kim',
          sponsor: 'Jennifer Lee (CISO)',
          strategicAlignment: 'High',
          businessDriver: 'Risk Mitigation',
          investmentCategory: 'Mandatory',
          totalBudget: 3000000,
          duration: 24,
          startDate: '2024-01-01',
          endDate: '2026-01-01',
          currency: 'USD'
        },
        governance: {
          steeringCommittee: [
            { name: 'Jennifer Lee', role: 'CISO', email: '<EMAIL>' },
            { name: 'David Kim', role: 'Security Director', email: '<EMAIL>' }
          ],
          decisionGates: [],
          riskTolerance: 'Low',
          complianceFramework: ['PMI', 'ISO 21500', 'PRINCE2']
        },
        useCases: [
          {
            id: 'UC007',
            name: 'Zero Trust Network Architecture',
            priority: 'High',
            businessValue: 'Very High',
            complexity: 'Very High',
            budget: 1500000,
            timeline: 18,
            status: 'Development',
            roi: 0, // Security projects often don't have direct ROI
            assignedTo: 'Security Team A'
          },
          {
            id: 'UC008',
            name: 'Advanced Threat Detection System',
            priority: 'High',
            businessValue: 'High',
            complexity: 'High',
            budget: 1000000,
            timeline: 12,
            status: 'Testing',
            roi: 0,
            assignedTo: 'Security Team B'
          }
        ],
        milestones: [
          {
            id: 'MS005',
            name: 'Security Assessment',
            description: 'Comprehensive security audit and risk assessment',
            targetDate: '2024-03-01',
            actualDate: '2024-02-28',
            status: 'Completed',
            budgetAllocated: 200000,
            actualCost: 195000,
            progress: 100,
            approvals: [
              { stakeholder: 'Jennifer Lee', role: 'CISO', status: 'Approved', date: '2024-02-25' }
            ]
          }
        ],
        financialMetrics: {
          totalInvestment: 2500000,
          expectedROI: 0, // Risk mitigation value
          npv: -500000, // Cost center
          irr: 0,
          paybackPeriod: 0,
          yieldIndex: 0
        },
        status: 'Active',
        createdBy: 'David Kim',
        lastModifiedBy: 'David Kim',
        tags: ['cybersecurity', 'compliance', 'risk-mitigation'],
        createdAt: '2024-01-01T07:00:00Z',
        updatedAt: '2024-03-15T13:20:00Z'
      },
      {
        _id: '5',
        programInfo: {
          programName: 'Supply Chain Optimization',
          programManager: 'Amanda Foster',
          sponsor: 'Thomas Brown (COO)',
          strategicAlignment: 'Medium',
          businessDriver: 'Cost Reduction',
          investmentCategory: 'Operational',
          totalBudget: 1500000,
          duration: 20,
          startDate: '2024-04-01',
          endDate: '2025-12-01',
          currency: 'USD'
        },
        governance: {
          steeringCommittee: [
            { name: 'Thomas Brown', role: 'COO', email: '<EMAIL>' },
            { name: 'Amanda Foster', role: 'Supply Chain Director', email: '<EMAIL>' }
          ],
          decisionGates: [],
          riskTolerance: 'Medium',
          complianceFramework: ['PMI', 'SAFe']
        },
        useCases: [
          {
            id: 'UC009',
            name: 'Automated Inventory Management',
            priority: 'High',
            businessValue: 'High',
            complexity: 'Medium',
            budget: 800000,
            timeline: 12,
            status: 'Concept',
            roi: 35.2,
            assignedTo: 'Supply Chain Team'
          },
          {
            id: 'UC010',
            name: 'Supplier Portal Integration',
            priority: 'Medium',
            businessValue: 'Medium',
            complexity: 'Low',
            budget: 500000,
            timeline: 8,
            status: 'Concept',
            roi: 28.7,
            assignedTo: 'Integration Team'
          }
        ],
        milestones: [],
        financialMetrics: {
          totalInvestment: 1300000,
          expectedROI: 31.95,
          npv: 650000,
          irr: 22.1,
          paybackPeriod: 2.5,
          yieldIndex: 1.45
        },
        status: 'Draft',
        createdBy: 'Amanda Foster',
        lastModifiedBy: 'Amanda Foster',
        tags: ['supply-chain', 'automation', 'cost-reduction'],
        createdAt: '2024-03-25T12:00:00Z',
        updatedAt: '2024-03-25T12:00:00Z'
      }
    ];
  }

  // CRUD Operations

  // CREATE - Add new Master Business Case
  async create(data) {
    const newCase = {
      ...data,
      _id: String(this.nextId++),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    this.businessCases.push(newCase);
    
    return {
      success: true,
      message: 'Master business case created successfully',
      data: newCase
    };
  }

  // READ - Get all Master Business Cases with filtering and pagination
  async getAll(params = {}) {
    let filtered = [...this.businessCases];
    
    // Apply filters
    if (params.status) {
      filtered = filtered.filter(bc => bc.status === params.status);
    }
    
    if (params.businessDriver) {
      filtered = filtered.filter(bc => bc.programInfo.businessDriver === params.businessDriver);
    }
    
    if (params.investmentCategory) {
      filtered = filtered.filter(bc => bc.programInfo.investmentCategory === params.investmentCategory);
    }
    
    if (params.search) {
      const searchTerm = params.search.toLowerCase();
      filtered = filtered.filter(bc => 
        bc.programInfo.programName.toLowerCase().includes(searchTerm) ||
        bc.programInfo.programManager.toLowerCase().includes(searchTerm)
      );
    }
    
    // Apply sorting
    if (params.sortBy) {
      const sortField = params.sortBy;
      const sortOrder = params.sortOrder === 'desc' ? -1 : 1;
      
      filtered.sort((a, b) => {
        let aVal, bVal;
        
        if (sortField.includes('.')) {
          const fields = sortField.split('.');
          aVal = fields.reduce((obj, field) => obj?.[field], a);
          bVal = fields.reduce((obj, field) => obj?.[field], b);
        } else {
          aVal = a[sortField];
          bVal = b[sortField];
        }
        
        if (aVal < bVal) return -1 * sortOrder;
        if (aVal > bVal) return 1 * sortOrder;
        return 0;
      });
    } else {
      // Default sort by creation date (newest first)
      filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    }
    
    // Apply pagination
    const page = parseInt(params.page) || 1;
    const limit = parseInt(params.limit) || 10;
    const skip = (page - 1) * limit;
    
    const total = filtered.length;
    const paginatedData = filtered.slice(skip, skip + limit);
    
    return {
      success: true,
      data: paginatedData,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  // READ - Get single Master Business Case by ID
  async getById(id) {
    const businessCase = this.businessCases.find(bc => bc._id === id);
    
    if (!businessCase) {
      return {
        success: false,
        message: 'Master business case not found'
      };
    }
    
    return {
      success: true,
      data: businessCase
    };
  }

  // UPDATE - Update Master Business Case
  async update(id, data) {
    const index = this.businessCases.findIndex(bc => bc._id === id);
    
    if (index === -1) {
      return {
        success: false,
        message: 'Master business case not found'
      };
    }
    
    this.businessCases[index] = {
      ...this.businessCases[index],
      ...data,
      _id: id, // Preserve ID
      updatedAt: new Date().toISOString()
    };
    
    return {
      success: true,
      message: 'Master business case updated successfully',
      data: this.businessCases[index]
    };
  }

  // DELETE - Delete Master Business Case
  async delete(id) {
    const index = this.businessCases.findIndex(bc => bc._id === id);
    
    if (index === -1) {
      return {
        success: false,
        message: 'Master business case not found'
      };
    }
    
    this.businessCases.splice(index, 1);
    
    return {
      success: true,
      message: 'Master business case deleted successfully'
    };
  }

  // UPDATE STATUS - Update only the status
  async updateStatus(id, status, updatedBy) {
    const index = this.businessCases.findIndex(bc => bc._id === id);
    
    if (index === -1) {
      return {
        success: false,
        message: 'Master business case not found'
      };
    }
    
    this.businessCases[index].status = status;
    this.businessCases[index].lastModifiedBy = updatedBy;
    this.businessCases[index].updatedAt = new Date().toISOString();
    
    return {
      success: true,
      message: 'Status updated successfully',
      data: this.businessCases[index]
    };
  }

  // DUPLICATE - Create a copy of existing Master Business Case
  async duplicate(id, newProgramName) {
    const original = this.businessCases.find(bc => bc._id === id);
    
    if (!original) {
      return {
        success: false,
        message: 'Master business case not found'
      };
    }
    
    const duplicated = {
      ...original,
      _id: String(this.nextId++),
      programInfo: {
        ...original.programInfo,
        programName: newProgramName || `${original.programInfo.programName} (Copy)`
      },
      status: 'Draft',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    this.businessCases.push(duplicated);
    
    return {
      success: true,
      message: 'Master business case duplicated successfully',
      data: duplicated
    };
  }

  // STATISTICS - Get summary statistics
  async getStats() {
    const total = this.businessCases.length;
    const totalBudget = this.businessCases.reduce((sum, bc) => sum + bc.programInfo.totalBudget, 0);
    const avgBudget = total > 0 ? totalBudget / total : 0;
    const avgDuration = total > 0 ? this.businessCases.reduce((sum, bc) => sum + bc.programInfo.duration, 0) / total : 0;
    
    const statusStats = this.businessCases.reduce((acc, bc) => {
      acc[bc.status] = (acc[bc.status] || 0) + 1;
      return acc;
    }, {});
    
    const businessDriverStats = this.businessCases.reduce((acc, bc) => {
      const driver = bc.programInfo.businessDriver;
      acc[driver] = (acc[driver] || 0) + 1;
      return acc;
    }, {});
    
    return {
      success: true,
      data: {
        summary: {
          totalCases: total,
          totalBudget,
          avgBudget,
          avgDuration
        },
        statusDistribution: Object.entries(statusStats).map(([status, count]) => ({ _id: status, count })),
        businessDriverDistribution: Object.entries(businessDriverStats).map(([driver, count]) => ({ _id: driver, count }))
      }
    };
  }
}

// Create singleton instance
const mockDataService = new MockDataService();

export default mockDataService;
