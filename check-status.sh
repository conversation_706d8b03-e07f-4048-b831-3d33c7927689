#!/bin/bash

echo "🔍 Master BC Framework - System Status Check"
echo "============================================"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if port is in use
port_in_use() {
    lsof -i :$1 >/dev/null 2>&1
}

echo "📋 System Requirements Check"
echo "----------------------------"

# Check Node.js
if command_exists node; then
    NODE_VERSION=$(node --version)
    echo -e "✅ Node.js: ${GREEN}$NODE_VERSION${NC}"
else
    echo -e "❌ Node.js: ${RED}Not installed${NC}"
fi

# Check npm
if command_exists npm; then
    NPM_VERSION=$(npm --version)
    echo -e "✅ npm: ${GREEN}$NPM_VERSION${NC}"
else
    echo -e "❌ npm: ${RED}Not installed${NC}"
fi

# Check MongoDB
if command_exists mongosh; then
    echo -e "✅ MongoDB Shell: ${GREEN}Available${NC}"
elif command_exists mongo; then
    echo -e "✅ MongoDB Shell: ${GREEN}Available (legacy)${NC}"
else
    echo -e "⚠️  MongoDB Shell: ${YELLOW}Not found (optional for local development)${NC}"
fi

echo ""
echo "📁 Project Structure Check"
echo "--------------------------"

# Check key files
files_to_check=(
    "package.json"
    "src/App.jsx"
    "src/components/EnhancedStakeholderFeedback.jsx"
    "src/components/EnhancedPMICompliance.jsx"
    "src/components/ChangeManagementRoadmap.jsx"
    "server/package.json"
    "server/server.js"
    "demo.html"
    ".env.example"
    "QUICK_START.md"
)

for file in "${files_to_check[@]}"; do
    if [ -f "$file" ]; then
        echo -e "✅ $file: ${GREEN}Present${NC}"
    else
        echo -e "❌ $file: ${RED}Missing${NC}"
    fi
done

echo ""
echo "📦 Dependencies Check"
echo "--------------------"

# Check frontend dependencies
if [ -d "node_modules" ]; then
    echo -e "✅ Frontend dependencies: ${GREEN}Installed${NC}"
else
    echo -e "❌ Frontend dependencies: ${RED}Not installed${NC}"
    echo -e "   ${YELLOW}Run: npm install${NC}"
fi

# Check backend dependencies
if [ -d "server/node_modules" ]; then
    echo -e "✅ Backend dependencies: ${GREEN}Installed${NC}"
else
    echo -e "❌ Backend dependencies: ${RED}Not installed${NC}"
    echo -e "   ${YELLOW}Run: cd server && npm install${NC}"
fi

echo ""
echo "🌐 Port Status Check"
echo "-------------------"

# Check if ports are in use
if port_in_use 3000; then
    echo -e "⚠️  Port 3000: ${YELLOW}In use${NC}"
else
    echo -e "✅ Port 3000: ${GREEN}Available${NC}"
fi

if port_in_use 5000; then
    echo -e "⚠️  Port 5000: ${YELLOW}In use${NC}"
else
    echo -e "✅ Port 5000: ${GREEN}Available${NC}"
fi

if port_in_use 27017; then
    echo -e "✅ Port 27017 (MongoDB): ${GREEN}In use${NC}"
else
    echo -e "⚠️  Port 27017 (MongoDB): ${YELLOW}Not in use${NC}"
fi

echo ""
echo "🔧 Configuration Check"
echo "----------------------"

# Check .env files
if [ -f ".env" ]; then
    echo -e "✅ Frontend .env: ${GREEN}Present${NC}"
else
    echo -e "⚠️  Frontend .env: ${YELLOW}Missing (will use defaults)${NC}"
    echo -e "   ${BLUE}Run: cp .env.example .env${NC}"
fi

if [ -f "server/.env" ]; then
    echo -e "✅ Backend .env: ${GREEN}Present${NC}"
else
    echo -e "⚠️  Backend .env: ${YELLOW}Missing (will use defaults)${NC}"
fi

echo ""
echo "🎯 Feature Components Status"
echo "----------------------------"

# Check key components
components=(
    "src/components/EnhancedStakeholderFeedback.jsx:Multi-Stakeholder CRUD System"
    "src/components/EnhancedPMICompliance.jsx:PMI Guidelines & PGMP Framework"
    "src/components/ChangeManagementRoadmap.jsx:Change Management & Roadmap"
    "src/components/MasterBusinessCase.jsx:Master Business Case Management"
    "src/components/FinancialIndexes.jsx:Financial Analysis & Modeling"
    "src/components/Visualizations.jsx:Charts & Analytics"
)

for component in "${components[@]}"; do
    file="${component%%:*}"
    name="${component##*:}"
    if [ -f "$file" ]; then
        echo -e "✅ $name: ${GREEN}Ready${NC}"
    else
        echo -e "❌ $name: ${RED}Missing${NC}"
    fi
done

echo ""
echo "🚀 Quick Start Options"
echo "---------------------"
echo -e "${BLUE}Option 1 (Automated):${NC}"
echo "  ./start-full-stack.sh"
echo ""
echo -e "${BLUE}Option 2 (Manual):${NC}"
echo "  Terminal 1: cd server && npm run dev"
echo "  Terminal 2: npm run dev"
echo ""
echo -e "${BLUE}Option 3 (Demo):${NC}"
echo "  Open demo.html in browser"
echo ""
echo -e "${BLUE}Setup Help:${NC}"
echo "  ./dev-setup.sh"
echo ""

echo "📊 System Summary"
echo "----------------"
if [ -d "node_modules" ] && [ -d "server/node_modules" ]; then
    echo -e "Status: ${GREEN}Ready to run${NC}"
    echo -e "Next step: ${BLUE}./start-full-stack.sh${NC}"
else
    echo -e "Status: ${YELLOW}Setup required${NC}"
    echo -e "Next step: ${BLUE}./dev-setup.sh${NC}"
fi

echo ""
echo "🎯 Available Features:"
echo "  ✅ Master Business Case Management"
echo "  ✅ Enhanced PMI Guidelines & PGMP Framework"
echo "  ✅ Multi-Stakeholder Feedback System with CRUD"
echo "  ✅ Change Management & Roadmap"
echo "  ✅ Financial Modeling & Analysis"
echo "  ✅ Professional UI/UX Design"
echo "  ✅ MongoDB Integration"
echo "  ✅ RESTful API Backend"
echo ""
