@echo off
echo 🔧 Fixing Vite Error - Master BC Framework Setup
echo ================================================
echo.

echo 📋 Step 1: Checking Node.js installation...
node --version
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

npm --version
if %errorlevel% neq 0 (
    echo ❌ npm is not installed or not in PATH
    pause
    exit /b 1
)

echo ✅ Node.js and npm are installed
echo.

echo 📋 Step 2: Cleaning previous installation...
if exist node_modules (
    echo Removing old node_modules...
    rmdir /s /q node_modules
)

if exist package-lock.json (
    echo Removing package-lock.json...
    del package-lock.json
)

echo.
echo 📋 Step 3: Installing dependencies...
echo This may take a few minutes...
call npm install

if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    echo.
    echo 🔧 Trying alternative installation methods...
    echo.
    echo Method 1: Clear npm cache and retry...
    call npm cache clean --force
    call npm install
    
    if %errorlevel% neq 0 (
        echo.
        echo Method 2: Install with legacy peer deps...
        call npm install --legacy-peer-deps
        
        if %errorlevel% neq 0 (
            echo.
            echo Method 3: Install with force...
            call npm install --force
            
            if %errorlevel% neq 0 (
                echo ❌ All installation methods failed
                echo.
                echo 💡 Manual steps to try:
                echo 1. Delete node_modules folder manually
                echo 2. Run: npm cache clean --force
                echo 3. Run: npm install --verbose
                echo 4. Check your internet connection
                echo 5. Try using yarn instead: npm install -g yarn, then yarn install
                pause
                exit /b 1
            )
        )
    )
)

echo.
echo ✅ Dependencies installed successfully!
echo.

echo 📋 Step 4: Verifying Vite installation...
if exist "node_modules\.bin\vite.cmd" (
    echo ✅ Vite is installed correctly
) else (
    echo ❌ Vite not found, trying to install manually...
    call npm install vite@latest --save-dev
)

echo.
echo 📋 Step 5: Testing development server...
echo Starting development server (this may take a moment)...
echo.

start /b npm run dev

timeout /t 5 /nobreak > nul

echo.
echo 🎯 Setup Complete!
echo.
echo 📊 Your Master BC Framework should now be running at:
echo    http://localhost:3000
echo.
echo 🚀 If the browser doesn't open automatically, manually navigate to:
echo    http://localhost:3000
echo.
echo 💡 To stop the server, press Ctrl+C in the terminal
echo.
echo 🎯 Available Features:
echo    ✅ Business Case Framework (6-step process)
echo    ✅ Advanced Financial Modeling
echo    ✅ Excel Analysis & Augment AI
echo    ✅ PMI/PGMP Compliance Tracking
echo    ✅ Multi-Stakeholder Management
echo    ✅ Change Management & Roadmap
echo.
pause
