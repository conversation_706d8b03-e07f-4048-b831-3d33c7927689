import React, { useState, useEffect } from 'react'

const MasterBusinessCase = ({ data, onChange }) => {
  // State management
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [success, setSuccess] = useState(null)
  const [showSaveDialog, setShowSaveDialog] = useState(false)
  const [savedCases, setSavedCases] = useState([])
  const [selectedCaseId, setSelectedCaseId] = useState(null)

  const [masterCase, setMasterCase] = useState({
    _id: null,
    programInfo: {
      programName: 'Digital Transformation Initiative',
      programManager: '',
      sponsor: '',
      strategicAlignment: 'High',
      businessDriver: 'Market Expansion',
      investmentCategory: 'Strategic',
      totalBudget: 50000000,
      duration: 36,
      startDate: '2024-01-01',
      endDate: '2026-12-31'
    },
    governance: {
      steeringCommittee: [],
      decisionGates: [
        { gate: 'Gate 0', name: 'Strategic Alignment', status: 'Approved', date: '2024-01-15' },
        { gate: 'Gate 1', name: 'Business Case', status: 'In Review', date: '2024-02-15' },
        { gate: 'Gate 2', name: 'Solution Design', status: 'Pending', date: '2024-04-15' },
        { gate: 'Gate 3', name: 'Implementation', status: 'Pending', date: '2024-07-15' },
        { gate: 'Gate 4', name: 'Benefits Realization', status: 'Pending', date: '2025-01-15' }
      ],
      riskTolerance: 'Medium',
      complianceFramework: ['PMI', 'PGMP', 'ISO 21500']
    },
    useCases: [
      {
        id: 'UC001',
        name: 'Customer Portal Enhancement',
        priority: 'High',
        businessValue: 'High',
        complexity: 'Medium',
        budget: 5000000,
        timeline: 12,
        dependencies: ['UC002'],
        status: 'Planning',
        roi: 18.5,
        npv: 3200000
      },
      {
        id: 'UC002',
        name: 'Data Analytics Platform',
        priority: 'High',
        businessValue: 'Very High',
        complexity: 'High',
        budget: 8000000,
        timeline: 18,
        dependencies: [],
        status: 'Analysis',
        roi: 22.3,
        npv: 5800000
      },
      {
        id: 'UC003',
        name: 'Mobile Application',
        priority: 'Medium',
        businessValue: 'Medium',
        complexity: 'Medium',
        budget: 3000000,
        timeline: 9,
        dependencies: ['UC001'],
        status: 'Concept',
        roi: 15.2,
        npv: 1800000
      }
    ],
    ...data
  })

  const [activeView, setActiveView] = useState('overview')

  // API Base URL
  const API_BASE = process.env.REACT_APP_API_URL || 'http://localhost:5000/api'

  // Load saved business cases on component mount
  useEffect(() => {
    loadSavedCases()
  }, [])

  // API Functions
  const apiCall = async (endpoint, options = {}) => {
    try {
      const response = await fetch(`${API_BASE}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        ...options
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('API call failed:', error)
      throw error
    }
  }

  // CRUD Operations
  const loadSavedCases = async () => {
    setLoading(true)
    setError(null)

    try {
      const cases = await apiCall('/business-cases')
      setSavedCases(cases)
    } catch (err) {
      setError('Failed to load saved business cases')
      console.error('Load error:', err)
    } finally {
      setLoading(false)
    }
  }

  const saveBusinessCase = async () => {
    setLoading(true)
    setError(null)
    setSuccess(null)

    try {
      const caseToSave = {
        ...masterCase,
        lastModified: new Date().toISOString(),
        version: masterCase.version ? masterCase.version + 1 : 1
      }

      let result
      if (masterCase._id) {
        // Update existing case
        result = await apiCall(`/business-cases/${masterCase._id}`, {
          method: 'PUT',
          body: JSON.stringify(caseToSave)
        })
      } else {
        // Create new case
        result = await apiCall('/business-cases', {
          method: 'POST',
          body: JSON.stringify(caseToSave)
        })
      }

      setMasterCase(result)
      setSuccess('Business case saved successfully!')
      loadSavedCases() // Refresh the list

      if (onChange) {
        onChange(result)
      }
    } catch (err) {
      setError('Failed to save business case: ' + err.message)
      console.error('Save error:', err)
    } finally {
      setLoading(false)
    }
  }

  const loadBusinessCase = async (caseId) => {
    setLoading(true)
    setError(null)

    try {
      const businessCase = await apiCall(`/business-cases/${caseId}`)
      setMasterCase(businessCase)
      setSelectedCaseId(caseId)
      setSuccess('Business case loaded successfully!')

      if (onChange) {
        onChange(businessCase)
      }
    } catch (err) {
      setError('Failed to load business case: ' + err.message)
      console.error('Load error:', err)
    } finally {
      setLoading(false)
    }
  }

  const deleteBusinessCase = async (caseId) => {
    if (!window.confirm('Are you sure you want to delete this business case?')) {
      return
    }

    setLoading(true)
    setError(null)

    try {
      await apiCall(`/business-cases/${caseId}`, {
        method: 'DELETE'
      })

      setSavedCases(prev => prev.filter(c => c._id !== caseId))
      setSuccess('Business case deleted successfully!')

      // If we deleted the currently loaded case, reset to default
      if (selectedCaseId === caseId) {
        setMasterCase({
          _id: null,
          programInfo: {
            programName: 'New Business Case',
            programManager: '',
            sponsor: '',
            strategicAlignment: 'High',
            businessDriver: 'Market Expansion',
            investmentCategory: 'Strategic',
            totalBudget: 0,
            duration: 12,
            startDate: new Date().toISOString().split('T')[0],
            endDate: new Date(Date.now() + 365*24*60*60*1000).toISOString().split('T')[0]
          },
          governance: {
            steeringCommittee: [],
            decisionGates: [],
            riskTolerance: 'Medium',
            complianceFramework: ['PMI', 'PGMP']
          },
          useCases: []
        })
        setSelectedCaseId(null)
      }
    } catch (err) {
      setError('Failed to delete business case: ' + err.message)
      console.error('Delete error:', err)
    } finally {
      setLoading(false)
    }
  }

  const createNewCase = () => {
    setMasterCase({
      _id: null,
      programInfo: {
        programName: 'New Business Case',
        programManager: '',
        sponsor: '',
        strategicAlignment: 'High',
        businessDriver: 'Market Expansion',
        investmentCategory: 'Strategic',
        totalBudget: 0,
        duration: 12,
        startDate: new Date().toISOString().split('T')[0],
        endDate: new Date(Date.now() + 365*24*60*60*1000).toISOString().split('T')[0]
      },
      governance: {
        steeringCommittee: [],
        decisionGates: [
          { gate: 'Gate 0', name: 'Strategic Alignment', status: 'Pending', date: '' },
          { gate: 'Gate 1', name: 'Business Case', status: 'Pending', date: '' },
          { gate: 'Gate 2', name: 'Solution Design', status: 'Pending', date: '' },
          { gate: 'Gate 3', name: 'Implementation', status: 'Pending', date: '' },
          { gate: 'Gate 4', name: 'Benefits Realization', status: 'Pending', date: '' }
        ],
        riskTolerance: 'Medium',
        complianceFramework: ['PMI', 'PGMP']
      },
      useCases: []
    })
    setSelectedCaseId(null)
    setSuccess('New business case created!')
  }

  const updateMasterCase = (section, field, value) => {
    const updated = {
      ...masterCase,
      [section]: {
        ...masterCase[section],
        [field]: value
      }
    }
    setMasterCase(updated)
    onChange(updated)
  }

  const addUseCase = () => {
    const newUseCase = {
      id: `UC${String(masterCase.useCases.length + 1).padStart(3, '0')}`,
      name: 'New Use Case',
      priority: 'Medium',
      businessValue: 'Medium',
      complexity: 'Medium',
      budget: 1000000,
      timeline: 6,
      dependencies: [],
      status: 'Concept',
      roi: 0,
      npv: 0
    }

    setMasterCase(prev => ({
      ...prev,
      useCases: [...prev.useCases, newUseCase]
    }))
  }

  const getStatusColor = (status) => {
    const colors = {
      'Approved': '#28a745',
      'In Review': '#ffc107',
      'Pending': '#6c757d',
      'Planning': '#17a2b8',
      'Analysis': '#fd7e14',
      'Concept': '#6f42c1'
    }
    return colors[status] || '#6c757d'
  }

  const getPriorityColor = (priority) => {
    const colors = {
      'High': '#dc3545',
      'Medium': '#ffc107',
      'Low': '#28a745'
    }
    return colors[priority] || '#6c757d'
  }

  const renderOverview = () => (
    <div className="master-overview">
      <div className="card">
        <div className="card-header">
          <h2 className="card-title">🎯 Program Overview</h2>
          <div className="program-status">
            <span className="status-indicator status-success">Active</span>
          </div>
        </div>

        <div className="form-row">
          <div className="form-group">
            <label className="form-label">Program Name</label>
            <input
              type="text"
              className="form-input"
              value={masterCase.programInfo.programName}
              onChange={(e) => updateMasterCase('programInfo', 'programName', e.target.value)}
            />
          </div>
          <div className="form-group">
            <label className="form-label">Program Manager</label>
            <input
              type="text"
              className="form-input"
              value={masterCase.programInfo.programManager}
              onChange={(e) => updateMasterCase('programInfo', 'programManager', e.target.value)}
              placeholder="Enter program manager name"
            />
          </div>
          <div className="form-group">
            <label className="form-label">Executive Sponsor</label>
            <input
              type="text"
              className="form-input"
              value={masterCase.programInfo.sponsor}
              onChange={(e) => updateMasterCase('programInfo', 'sponsor', e.target.value)}
              placeholder="Enter sponsor name"
            />
          </div>
          <div className="form-group">
            <label className="form-label">Strategic Alignment</label>
            <select
              className="form-input"
              value={masterCase.programInfo.strategicAlignment}
              onChange={(e) => updateMasterCase('programInfo', 'strategicAlignment', e.target.value)}
            >
              <option value="High">High</option>
              <option value="Medium">Medium</option>
              <option value="Low">Low</option>
            </select>
          </div>
        </div>

        <div className="form-row">
          <div className="form-group">
            <label className="form-label">Business Driver</label>
            <select
              className="form-input"
              value={masterCase.programInfo.businessDriver}
              onChange={(e) => updateMasterCase('programInfo', 'businessDriver', e.target.value)}
            >
              <option value="Market Expansion">Market Expansion</option>
              <option value="Cost Reduction">Cost Reduction</option>
              <option value="Regulatory Compliance">Regulatory Compliance</option>
              <option value="Digital Transformation">Digital Transformation</option>
              <option value="Customer Experience">Customer Experience</option>
            </select>
          </div>
          <div className="form-group">
            <label className="form-label">Investment Category</label>
            <select
              className="form-input"
              value={masterCase.programInfo.investmentCategory}
              onChange={(e) => updateMasterCase('programInfo', 'investmentCategory', e.target.value)}
            >
              <option value="Strategic">Strategic</option>
              <option value="Operational">Operational</option>
              <option value="Mandatory">Mandatory</option>
              <option value="Infrastructure">Infrastructure</option>
            </select>
          </div>
          <div className="form-group">
            <label className="form-label">Total Budget ($)</label>
            <input
              type="number"
              className="form-input"
              value={masterCase.programInfo.totalBudget}
              onChange={(e) => updateMasterCase('programInfo', 'totalBudget', parseInt(e.target.value))}
            />
          </div>
          <div className="form-group">
            <label className="form-label">Duration (Months)</label>
            <input
              type="number"
              className="form-input"
              value={masterCase.programInfo.duration}
              onChange={(e) => updateMasterCase('programInfo', 'duration', parseInt(e.target.value))}
            />
          </div>
        </div>
      </div>

      <div className="card">
        <div className="card-header">
          <h2 className="card-title">📊 Program Metrics Dashboard</h2>
        </div>

        <div className="metrics-dashboard">
          <div className="metric-card">
            <div className="metric-icon">💰</div>
            <div className="metric-label">Total Investment</div>
            <div className="metric-value metric-neutral">
              ${(masterCase.programInfo.totalBudget / 1000000).toFixed(1)}M
            </div>
          </div>

          <div className="metric-card">
            <div className="metric-icon">📈</div>
            <div className="metric-label">Expected ROI</div>
            <div className="metric-value metric-positive">
              {masterCase.useCases.reduce((sum, uc) => sum + uc.roi, 0) / masterCase.useCases.length}%
            </div>
          </div>

          <div className="metric-card">
            <div className="metric-icon">🎯</div>
            <div className="metric-label">Use Cases</div>
            <div className="metric-value metric-neutral">
              {masterCase.useCases.length}
            </div>
          </div>

          <div className="metric-card">
            <div className="metric-icon">⏱️</div>
            <div className="metric-label">Timeline</div>
            <div className="metric-value metric-neutral">
              {masterCase.programInfo.duration} months
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  const renderGovernance = () => (
    <div className="governance-view">
      <div className="card">
        <div className="card-header">
          <h2 className="card-title">🏛️ Governance Framework</h2>
        </div>

        <div className="governance-section">
          <h3>📋 Decision Gates (PMI/PGMP Compliance)</h3>
          <div className="decision-gates">
            {masterCase.governance.decisionGates.map((gate, index) => (
              <div key={index} className="gate-item">
                <div className="gate-header">
                  <span className="gate-id">{gate.gate}</span>
                  <span className="gate-name">{gate.name}</span>
                  <span
                    className="gate-status"
                    style={{
                      backgroundColor: getStatusColor(gate.status),
                      color: 'white',
                      padding: '0.25rem 0.75rem',
                      borderRadius: '12px',
                      fontSize: '0.8rem'
                    }}
                  >
                    {gate.status}
                  </span>
                </div>
                <div className="gate-date">Target Date: {gate.date}</div>
              </div>
            ))}
          </div>
        </div>

        <div className="governance-section">
          <h3>⚖️ Risk & Compliance</h3>
          <div className="form-row">
            <div className="form-group">
              <label className="form-label">Risk Tolerance</label>
              <select
                className="form-input"
                value={masterCase.governance.riskTolerance}
                onChange={(e) => updateMasterCase('governance', 'riskTolerance', e.target.value)}
              >
                <option value="Low">Low</option>
                <option value="Medium">Medium</option>
                <option value="High">High</option>
              </select>
            </div>
            <div className="form-group">
              <label className="form-label">Compliance Frameworks</label>
              <div className="compliance-tags">
                {masterCase.governance.complianceFramework.map((framework, index) => (
                  <span key={index} className="compliance-tag">{framework}</span>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  const renderUseCases = () => (
    <div className="use-cases-view">
      <div className="card">
        <div className="card-header">
          <h2 className="card-title">📋 Use Cases Portfolio</h2>
          <button className="btn btn-primary" onClick={addUseCase}>
            + Add Use Case
          </button>
        </div>

        <div className="use-cases-grid">
          {masterCase.useCases.map((useCase, index) => (
            <div key={useCase.id} className="use-case-card">
              <div className="use-case-header">
                <div className="use-case-id">{useCase.id}</div>
                <div
                  className="use-case-priority"
                  style={{
                    backgroundColor: getPriorityColor(useCase.priority),
                    color: 'white',
                    padding: '0.25rem 0.5rem',
                    borderRadius: '4px',
                    fontSize: '0.8rem'
                  }}
                >
                  {useCase.priority}
                </div>
              </div>

              <h4 className="use-case-name">{useCase.name}</h4>

              <div className="use-case-metrics">
                <div className="metric-row">
                  <span>Budget:</span>
                  <span>${(useCase.budget / 1000000).toFixed(1)}M</span>
                </div>
                <div className="metric-row">
                  <span>Timeline:</span>
                  <span>{useCase.timeline} months</span>
                </div>
                <div className="metric-row">
                  <span>ROI:</span>
                  <span className="metric-positive">{useCase.roi}%</span>
                </div>
                <div className="metric-row">
                  <span>NPV:</span>
                  <span className="metric-positive">${(useCase.npv / 1000000).toFixed(1)}M</span>
                </div>
              </div>

              <div className="use-case-status">
                <span
                  className="status-badge"
                  style={{
                    backgroundColor: getStatusColor(useCase.status),
                    color: 'white',
                    padding: '0.5rem 1rem',
                    borderRadius: '6px',
                    fontSize: '0.9rem'
                  }}
                >
                  {useCase.status}
                </span>
              </div>

              {useCase.dependencies.length > 0 && (
                <div className="use-case-dependencies">
                  <small>Dependencies: {useCase.dependencies.join(', ')}</small>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  )

  const views = [
    { id: 'overview', label: 'Program Overview', icon: '🎯' },
    { id: 'governance', label: 'Governance', icon: '🏛️' },
    { id: 'usecases', label: 'Use Cases', icon: '📋' },
    { id: 'portfolio', label: 'Portfolio View', icon: '📊' }
  ]

  return (
    <div className="master-business-case">
      {/* CRUD Toolbar */}
      <div className="crud-toolbar">
        <div className="toolbar-section">
          <h2>💼 Master Business Case Management</h2>
          <div className="case-info">
            {masterCase._id ? (
              <span className="case-status">
                📄 Editing: {masterCase.programInfo.programName}
                {masterCase.version && <span className="version">v{masterCase.version}</span>}
              </span>
            ) : (
              <span className="case-status">📝 New Business Case</span>
            )}
          </div>
        </div>

        <div className="toolbar-actions">
          <button
            className="btn btn-secondary"
            onClick={createNewCase}
            disabled={loading}
          >
            📄 New Case
          </button>

          <button
            className="btn btn-primary"
            onClick={saveBusinessCase}
            disabled={loading}
          >
            {loading ? '💾 Saving...' : '💾 Save Case'}
          </button>

          <button
            className="btn btn-secondary"
            onClick={() => setShowSaveDialog(!showSaveDialog)}
            disabled={loading}
          >
            📂 Load Case
          </button>

          {masterCase._id && (
            <button
              className="btn btn-danger"
              onClick={() => deleteBusinessCase(masterCase._id)}
              disabled={loading}
            >
              🗑️ Delete
            </button>
          )}
        </div>
      </div>

      {/* Status Messages */}
      {error && (
        <div className="status-message error">
          <span className="status-icon">⚠️</span>
          <span>{error}</span>
          <button onClick={() => setError(null)}>✕</button>
        </div>
      )}

      {success && (
        <div className="status-message success">
          <span className="status-icon">✅</span>
          <span>{success}</span>
          <button onClick={() => setSuccess(null)}>✕</button>
        </div>
      )}

      {/* Load Dialog */}
      {showSaveDialog && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>📂 Load Business Case</h3>
              <button
                className="modal-close"
                onClick={() => setShowSaveDialog(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-body">
              {loading ? (
                <div className="loading-indicator">
                  <div className="spinner"></div>
                  <span>Loading cases...</span>
                </div>
              ) : savedCases.length === 0 ? (
                <div className="empty-state">
                  <p>No saved business cases found.</p>
                  <button
                    className="btn btn-primary"
                    onClick={() => setShowSaveDialog(false)}
                  >
                    Create New Case
                  </button>
                </div>
              ) : (
                <div className="saved-cases-list">
                  {savedCases.map((savedCase) => (
                    <div key={savedCase._id} className="saved-case-item">
                      <div className="case-info">
                        <h4>{savedCase.programInfo.programName}</h4>
                        <p>
                          Manager: {savedCase.programInfo.programManager || 'Not assigned'} |
                          Budget: ${(savedCase.programInfo.totalBudget / 1000000).toFixed(1)}M
                        </p>
                        <small>
                          Last modified: {new Date(savedCase.lastModified || savedCase.updatedAt).toLocaleDateString()}
                          {savedCase.version && ` | Version: ${savedCase.version}`}
                        </small>
                      </div>
                      <div className="case-actions">
                        <button
                          className="btn btn-primary btn-sm"
                          onClick={() => {
                            loadBusinessCase(savedCase._id)
                            setShowSaveDialog(false)
                          }}
                        >
                          📂 Load
                        </button>
                        <button
                          className="btn btn-danger btn-sm"
                          onClick={() => deleteBusinessCase(savedCase._id)}
                        >
                          🗑️ Delete
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      <div className="master-navigation">
        {views.map((view) => (
          <button
            key={view.id}
            className={`master-nav-btn ${activeView === view.id ? 'active' : ''}`}
            onClick={() => setActiveView(view.id)}
          >
            <span className="nav-icon">{view.icon}</span>
            <span className="nav-label">{view.label}</span>
          </button>
        ))}
      </div>

      <div className="master-content">
        {activeView === 'overview' && renderOverview()}
        {activeView === 'governance' && renderGovernance()}
        {activeView === 'usecases' && renderUseCases()}
        {activeView === 'portfolio' && (
          <div className="portfolio-view">
            <div className="card">
              <div className="card-header">
                <h2 className="card-title">📊 Portfolio Dashboard</h2>
              </div>
              <div className="chart-placeholder">
                📈 Portfolio Performance Analytics<br/>
                <small>(Interactive charts showing use case performance, dependencies, and ROI analysis)</small>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default MasterBusinessCase
