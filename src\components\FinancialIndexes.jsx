import React from 'react'

const FinancialIndexes = ({ data }) => {
  // Use calculated results from the financial engine
  const metrics = data.results || {}
  const yearlyData = data.results?.yearlyData || []
  const sensitivityResults = data.sensitivityResults || null

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value)
  }

  const formatPercentage = (value) => {
    return `${value.toFixed(1)}%`
  }

  const getMetricStatus = (metric, value) => {
    switch (metric) {
      case 'npv':
        return value > 0 ? 'positive' : 'negative'
      case 'irr':
        return value > 10 ? 'positive' : value > 5 ? 'neutral' : 'negative'
      case 'payback':
        return value < 3 ? 'positive' : value < 5 ? 'neutral' : 'negative'
      case 'yieldIndex':
        return value > 1.5 ? 'positive' : value > 1 ? 'neutral' : 'negative'
      default:
        return 'neutral'
    }
  }

  return (
    <div className="financial-indexes">
      <div className="card">
        <div className="card-header">
          <h2 className="card-title">📈 Key Financial Metrics</h2>
          <div className="calculation-date">
            Calculated: {new Date().toLocaleDateString()}
          </div>
        </div>

        <div className="financial-metrics">
          <div className="metric-card">
            <div className="metric-icon">💰</div>
            <div className="metric-label">Net Present Value</div>
            <div className={`metric-value metric-${getMetricStatus('npv', metrics.npv || 0)}`}>
              {formatCurrency(metrics.npv || 0)}
            </div>
            <div className="metric-description">
              Total value created by the project
            </div>
          </div>

          <div className="metric-card">
            <div className="metric-icon">📊</div>
            <div className="metric-label">Internal Rate of Return</div>
            <div className={`metric-value metric-${getMetricStatus('irr', metrics.irr || 0)}`}>
              {formatPercentage(metrics.irr || 0)}
            </div>
            <div className="metric-description">
              Expected annual return rate
            </div>
          </div>

          <div className="metric-card">
            <div className="metric-icon">⏱️</div>
            <div className="metric-label">Payback Period</div>
            <div className={`metric-value metric-${getMetricStatus('payback', metrics.payback || 0)}`}>
              {metrics.payback ? `${metrics.payback.toFixed(1)} years` : 'N/A'}
            </div>
            <div className="metric-description">
              Time to recover initial investment
            </div>
          </div>

          <div className="metric-card">
            <div className="metric-icon">📈</div>
            <div className="metric-label">Yield Index</div>
            <div className={`metric-value metric-${getMetricStatus('yieldIndex', metrics.yieldIndex || 0)}`}>
              {metrics.yieldIndex ? metrics.yieldIndex.toFixed(2) : 'N/A'}
            </div>
            <div className="metric-description">
              Return per dollar invested
            </div>
          </div>

          <div className="metric-card">
            <div className="metric-icon">💹</div>
            <div className="metric-label">Gross Margin</div>
            <div className="metric-value metric-positive">
              {formatPercentage(metrics.grossMargin || 0)}
            </div>
            <div className="metric-description">
              Profit margin on sales
            </div>
          </div>

          <div className="metric-card">
            <div className="metric-icon">🎯</div>
            <div className="metric-label">Total Revenue</div>
            <div className="metric-value metric-neutral">
              {formatCurrency(metrics.totalRevenue || 0)}
            </div>
            <div className="metric-description">
              Total project revenue
            </div>
          </div>
        </div>
      </div>

      <div className="card">
        <div className="card-header">
          <h2 className="card-title">📋 Cash Flow Analysis</h2>
        </div>

        <div className="cash-flow-table">
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <thead>
              <tr style={{ backgroundColor: '#f8f9fa' }}>
                <th style={{ padding: '1rem', textAlign: 'left', borderBottom: '2px solid #dee2e6' }}>Year</th>
                <th style={{ padding: '1rem', textAlign: 'right', borderBottom: '2px solid #dee2e6' }}>Revenue</th>
                <th style={{ padding: '1rem', textAlign: 'right', borderBottom: '2px solid #dee2e6' }}>Costs</th>
                <th style={{ padding: '1rem', textAlign: 'right', borderBottom: '2px solid #dee2e6' }}>Cash Flow</th>
                <th style={{ padding: '1rem', textAlign: 'right', borderBottom: '2px solid #dee2e6' }}>Cumulative</th>
              </tr>
            </thead>
            <tbody>
              {yearlyData.map((year, index) => (
                <tr key={index} style={{ borderBottom: '1px solid #dee2e6' }}>
                  <td style={{ padding: '1rem', fontWeight: '500' }}>Year {year.year}</td>
                  <td style={{ padding: '1rem', textAlign: 'right' }}>{formatCurrency(year.revenue)}</td>
                  <td style={{ padding: '1rem', textAlign: 'right' }}>{formatCurrency(year.costs)}</td>
                  <td style={{
                    padding: '1rem',
                    textAlign: 'right',
                    color: year.cashFlow >= 0 ? '#28a745' : '#dc3545',
                    fontWeight: '500'
                  }}>
                    {formatCurrency(year.cashFlow)}
                  </td>
                  <td style={{
                    padding: '1rem',
                    textAlign: 'right',
                    color: year.cumulative >= 0 ? '#28a745' : '#dc3545',
                    fontWeight: '500'
                  }}>
                    {formatCurrency(year.cumulative)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      <div className="card">
        <div className="card-header">
          <h2 className="card-title">⚠️ Break-Even Analysis</h2>
        </div>

        <div className="break-even-analysis">
          <div className="break-even-metric">
            <div className="metric-label">Drop in Sales Break Even</div>
            <div className="metric-value metric-neutral">
              {formatCurrency(metrics.breakEvenSales)}
            </div>
            <div className="metric-description">
              Minimum sales to break even (26% drop from mature year)
            </div>
          </div>

          <div className="risk-indicators" style={{ marginTop: '2rem' }}>
            <h3>🚨 Risk Assessment</h3>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem', marginTop: '1rem' }}>
              <div className="risk-card" style={{ padding: '1rem', backgroundColor: '#d4edda', borderRadius: '6px' }}>
                <strong>Low Risk</strong>
                <div>Strong NPV and IRR metrics</div>
              </div>
              <div className="risk-card" style={{ padding: '1rem', backgroundColor: '#fff3cd', borderRadius: '6px' }}>
                <strong>Medium Risk</strong>
                <div>Payback period over 4 years</div>
              </div>
              <div className="risk-card" style={{ padding: '1rem', backgroundColor: '#f8d7da', borderRadius: '6px' }}>
                <strong>Monitor</strong>
                <div>Market sensitivity to price changes</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FinancialIndexes
