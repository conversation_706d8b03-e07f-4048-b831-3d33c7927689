@echo off
echo 🚀 Starting Master Business Case Application
echo.

echo 📡 Starting Backend Server...
start "Backend Server" cmd /k "cd server && node server.js"

echo.
echo ⏳ Waiting 5 seconds for backend to start...
timeout /t 5 /nobreak > nul

echo.
echo 🎨 Starting Frontend Application...
start "Frontend App" cmd /k "npm run dev"

echo.
echo ✅ Both servers are starting!
echo.
echo 📍 Access points:
echo   Frontend: http://localhost:3000
echo   Backend API: http://localhost:5000
echo.
echo Press any key to continue...
pause > nul
