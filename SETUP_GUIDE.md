# 🚀 Master BC Framework - Complete Setup Guide

## ✅ Quick Start (Recommended)

### Option 1: Automated Setup
Simply double-click the `run-app.bat` file in the project directory. This will:
- Check Node.js installation
- Install all dependencies automatically
- Start the development server
- Open your browser to http://localhost:3000

### Option 2: Manual Setup
If you prefer manual control, follow these steps:

```bash
# 1. Install dependencies
npm install

# 2. Start development server
npm run dev

# 3. Open browser to http://localhost:3000
```

## 🔧 Troubleshooting Common Issues

### Issue 1: "vite is not recognized"
**Solution:**
```bash
# Clear cache and reinstall
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

### Issue 2: Port 3000 is busy
**Solution:**
```bash
# Kill process on port 3000
npx kill-port 3000

# Or use different port
npm run dev -- --port 3001
```

### Issue 3: Permission errors
**Solution:**
```bash
# Fix npm permissions (macOS/Linux)
npm config set prefix ~/.npm-global

# Or use sudo (not recommended)
sudo npm install
```

### Issue 4: Node.js not installed
**Solution:**
1. Download Node.js from https://nodejs.org/
2. Install the LTS version
3. Restart your terminal
4. Verify: `node --version`

## 📋 System Requirements

### Minimum Requirements
- **Node.js**: 16.0 or higher
- **npm**: 8.0 or higher
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 1GB free space
- **Browser**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+

### Recommended Setup
- **Node.js**: 18.x LTS
- **npm**: Latest version
- **RAM**: 16GB
- **SSD**: For faster builds
- **Browser**: Latest Chrome or Edge

## 🎯 Application Features

### 📋 Business Case Framework
- 6-step disciplined development process
- Project identification and alternatives analysis
- Benefit analysis and risk assessment
- System integration view

### 🧮 Advanced Financial Modeling
- Real-time IRR, NPV, ROI calculations
- Scenario analysis and sensitivity testing
- Excel integration and data import
- Auto-calculation engine

### 🤖 Excel Analysis & Augment AI
- Excel file upload and processing
- Advanced financial calculations
- AI-powered analysis and insights
- Export capabilities

### 🏛️ PMI/PGMP Compliance
- Project lifecycle tracking
- Knowledge areas assessment
- Compliance scoring and monitoring
- Governance framework

### 👥 Multi-Stakeholder Management
- Full CRUD operations
- Advanced commenting system
- Multiple view modes
- Analytics and reporting

### 🗺️ Change Management & Roadmap
- Current state analysis
- Proposed governance models
- Implementation timeline
- Master BC integration

## 🌐 Accessing the Application

Once running, the application will be available at:
- **Local**: http://localhost:3000
- **Network**: http://[your-ip]:3000 (if using --host flag)

## 📊 Available Scripts

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build

# Utilities
npm run setup        # Automated setup script
npm run start-full   # Start full stack (if backend available)
npm run build-prod   # Production build with optimizations
```

## 🔄 Development Workflow

1. **Start Development**: `npm run dev`
2. **Make Changes**: Edit files in `src/` directory
3. **Hot Reload**: Changes automatically refresh browser
4. **Test Features**: Navigate between tabs to test functionality
5. **Build**: `npm run build` when ready for production

## 📁 Project Structure

```
mybc/
├── src/
│   ├── components/          # React components
│   ├── utils/              # Utility functions
│   ├── App.jsx             # Main application
│   ├── App.css             # Styles
│   └── main.jsx            # Entry point
├── public/                 # Static assets
├── package.json            # Dependencies
├── vite.config.js          # Vite configuration
├── index.html              # HTML template
├── run-app.bat             # Quick start script
└── README.md               # Documentation
```

## 🎯 Next Steps

1. **Explore Features**: Navigate through all tabs
2. **Test Functionality**: Try uploading Excel files, creating stakeholders
3. **Customize**: Modify components to fit your needs
4. **Deploy**: Build and deploy to your preferred hosting platform

## 💡 Tips for Best Experience

- **Use Chrome or Edge** for best compatibility
- **Enable JavaScript** (required for React)
- **Allow file uploads** for Excel integration
- **Use latest browser version** for optimal performance
- **Clear cache** if you encounter issues

## 🆘 Getting Help

If you encounter issues:

1. **Check Console**: Open browser DevTools (F12) for error messages
2. **Clear Cache**: Ctrl+Shift+R to hard refresh
3. **Restart Server**: Stop (Ctrl+C) and restart `npm run dev`
4. **Reinstall**: Delete `node_modules` and run `npm install`
5. **Check Logs**: Look at terminal output for error details

## 🎉 Success!

When everything is working, you should see:
- ✅ Development server running on port 3000
- ✅ Browser opens automatically
- ✅ Master BC Framework loads with all tabs
- ✅ No console errors
- ✅ All features interactive and functional

Enjoy using the Master Business Case Framework! 🎯
