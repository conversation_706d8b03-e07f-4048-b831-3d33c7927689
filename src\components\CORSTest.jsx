import React, { useState } from 'react'

const CORSTest = () => {
  const [testResult, setTestResult] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  const API_BASE = process.env.REACT_APP_API_URL || 'http://localhost:5000/api'

  const testCORS = async () => {
    setLoading(true)
    setError(null)
    setTestResult(null)

    try {
      // Test GET request
      const getResponse = await fetch(`${API_BASE}/cors-test`)
      const getData = await getResponse.json()

      // Test POST request
      const postResponse = await fetch(`${API_BASE}/cors-test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          test: 'CORS POST test',
          timestamp: new Date().toISOString()
        })
      })
      const postData = await postResponse.json()

      setTestResult({
        get: getData,
        post: postData,
        success: true
      })
    } catch (err) {
      setError(err.message)
      setTestResult({
        success: false,
        error: err.message
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div style={{ padding: '2rem', maxWidth: '800px', margin: '0 auto' }}>
      <h2>🔗 CORS Test</h2>
      <p>Test CORS configuration between frontend and backend</p>
      
      <button 
        onClick={testCORS}
        disabled={loading}
        style={{
          padding: '1rem 2rem',
          backgroundColor: loading ? '#ccc' : '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: loading ? 'not-allowed' : 'pointer',
          fontSize: '1rem',
          marginBottom: '2rem'
        }}
      >
        {loading ? '🔄 Testing...' : '🚀 Test CORS'}
      </button>

      {error && (
        <div style={{
          padding: '1rem',
          backgroundColor: '#f8d7da',
          color: '#721c24',
          border: '1px solid #f5c6cb',
          borderRadius: '4px',
          marginBottom: '1rem'
        }}>
          <strong>❌ CORS Error:</strong> {error}
        </div>
      )}

      {testResult && (
        <div style={{
          padding: '1rem',
          backgroundColor: testResult.success ? '#d4edda' : '#f8d7da',
          color: testResult.success ? '#155724' : '#721c24',
          border: `1px solid ${testResult.success ? '#c3e6cb' : '#f5c6cb'}`,
          borderRadius: '4px',
          marginBottom: '1rem'
        }}>
          <h3>{testResult.success ? '✅ CORS Test Successful!' : '❌ CORS Test Failed'}</h3>
          
          {testResult.success && (
            <div>
              <h4>GET Request Result:</h4>
              <pre style={{ 
                backgroundColor: '#f8f9fa', 
                padding: '1rem', 
                borderRadius: '4px',
                overflow: 'auto',
                fontSize: '0.9rem'
              }}>
                {JSON.stringify(testResult.get, null, 2)}
              </pre>
              
              <h4>POST Request Result:</h4>
              <pre style={{ 
                backgroundColor: '#f8f9fa', 
                padding: '1rem', 
                borderRadius: '4px',
                overflow: 'auto',
                fontSize: '0.9rem'
              }}>
                {JSON.stringify(testResult.post, null, 2)}
              </pre>
            </div>
          )}
          
          {!testResult.success && (
            <p><strong>Error:</strong> {testResult.error}</p>
          )}
        </div>
      )}

      <div style={{
        padding: '1rem',
        backgroundColor: '#e9ecef',
        borderRadius: '4px',
        fontSize: '0.9rem'
      }}>
        <h4>📋 Test Information:</h4>
        <ul>
          <li><strong>Frontend URL:</strong> {window.location.origin}</li>
          <li><strong>Backend API:</strong> {API_BASE}</li>
          <li><strong>Test Endpoints:</strong> GET/POST {API_BASE}/cors-test</li>
        </ul>
      </div>
    </div>
  )
}

export default CORSTest
