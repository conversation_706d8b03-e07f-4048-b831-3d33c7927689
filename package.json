{"name": "mybc", "version": "1.0.0", "description": "Master BC Framework - Business Case Governance Platform", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "start": "vite preview", "setup": "chmod +x dev-setup.sh && ./dev-setup.sh", "start-full": "chmod +x start-full-stack.sh && ./start-full-stack.sh", "build-prod": "chmod +x build-production.sh && ./build-production.sh", "lint": "echo '<PERSON><PERSON> not configured yet'", "test": "echo 'Tests not configured yet'"}, "keywords": ["business-case", "financial-modeling", "analysis"], "author": "", "license": "ISC", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "uuid": "^9.0.1", "xlsx": "^0.18.5", "chart.js": "^4.4.0", "react-chartjs-2": "^5.2.0"}, "devDependencies": {"@vitejs/plugin-react": "^4.0.0", "vite": "^4.3.0"}}