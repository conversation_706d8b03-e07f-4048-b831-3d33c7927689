<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Business Case Analysis Tool - UI Mockup</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }

        .app {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .app-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
        }

        .header-content h1 {
            font-size: 1.8rem;
            font-weight: 600;
        }

        .project-info {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 0.25rem;
        }

        .tab-navigation {
            background: white;
            border-bottom: 1px solid #e1e5e9;
            padding: 0 2rem;
            display: flex;
            gap: 0;
            max-width: 1400px;
            margin: 0 auto;
            width: 100%;
        }

        .tab-button {
            background: none;
            border: none;
            padding: 1rem 1.5rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.95rem;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.2s ease;
        }

        .tab-button.active {
            color: #667eea;
            border-bottom-color: #667eea;
            background-color: #f8f9fa;
        }

        .main-content {
            flex: 1;
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
            width: 100%;
        }

        .card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e1e5e9;
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #333;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #555;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.95rem;
        }

        .financial-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .metric-card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 1px solid #e1e5e9;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin: 0.5rem 0;
        }

        .metric-positive { color: #28a745; }
        .metric-negative { color: #dc3545; }
        .metric-neutral { color: #6c757d; }

        .app-footer {
            background: white;
            border-top: 1px solid #e1e5e9;
            padding: 1rem 2rem;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
        }

        .footer-actions {
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-primary { background-color: #667eea; color: white; }
        .btn-secondary { background-color: #6c757d; color: white; }
        .btn-success { background-color: #28a745; color: white; }

        /* Tab Content */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Section Navigation */
        .section-navigation {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .section-btn {
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 0.75rem 1rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s ease;
        }

        .section-btn:hover {
            background-color: #f8f9fa;
            border-color: #667eea;
        }

        .section-btn.active {
            background-color: #667eea;
            color: white;
            border-color: #667eea;
        }

        /* Cost Items */
        .cost-section {
            padding: 1rem 0;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .cost-item {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 6px;
            border: 1px solid #e9ecef;
            margin-bottom: 1rem;
        }

        /* Sensitivity Variables */
        .sensitivity-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .variable-card {
            background: white;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            padding: 1.5rem;
        }

        .variable-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .variable-name {
            font-weight: 600;
            color: #333;
        }

        .impact-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            color: white;
        }

        .impact-high { background-color: #dc3545; }
        .impact-medium { background-color: #ffc107; }
        .impact-low { background-color: #28a745; }

        /* Scenario Results */
        .scenario-results {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            margin-top: 2rem;
        }

        .scenario-card {
            text-align: center;
            padding: 1.5rem;
            border-radius: 6px;
        }

        .scenario-best {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
        }

        .scenario-base {
            background-color: #e2e3e5;
            border: 1px solid #d6d8db;
        }

        .scenario-worst {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
        }

        /* Charts */
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .chart-placeholder {
            height: 300px;
            background: linear-gradient(45deg, #f8f9fa 25%, transparent 25%),
                        linear-gradient(-45deg, #f8f9fa 25%, transparent 25%),
                        linear-gradient(45deg, transparent 75%, #f8f9fa 75%),
                        linear-gradient(-45deg, transparent 75%, #f8f9fa 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 1.1rem;
            border-radius: 4px;
        }

        /* Table Styles */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }

        .data-table th,
        .data-table td {
            border: 1px solid #ddd;
            padding: 0.75rem;
            text-align: left;
        }

        .data-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }

        .data-table .positive { color: #28a745; font-weight: 500; }
        .data-table .negative { color: #dc3545; font-weight: 500; }

        /* Roadmap Styles */
        .roadmap-navigation {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .roadmap-btn {
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 0.75rem 1rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s ease;
            font-size: 0.9rem;
        }

        .roadmap-btn:hover {
            background-color: #f8f9fa;
            border-color: #667eea;
        }

        .roadmap-btn.active {
            background-color: #667eea;
            color: white;
            border-color: #667eea;
        }

        .roadmap-section {
            margin-top: 2rem;
        }

        .governance-table {
            margin: 1.5rem 0;
        }

        .current-issues, .proposed-benefits {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 1.5rem;
            margin-top: 1.5rem;
        }

        .sequential-approach {
            margin: 2rem 0;
        }

        .sequence-step {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border-left: 4px solid #667eea;
        }

        .step-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .step-number {
            background: #667eea;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .key-principle {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 2rem;
        }

        .principle-box {
            background: white;
            border-radius: 6px;
            padding: 1rem;
            margin-top: 1rem;
        }

        .program-structure {
            margin: 1.5rem 0;
        }

        .structure-level {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #28a745;
        }

        .structure-item {
            background: white;
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .recommendations {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 1.5rem;
        }

        .recommendation-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            border-left: 4px solid #ffc107;
        }

        .use-cases-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 1.5rem;
        }

        .use-case-card {
            background: white;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            padding: 1.5rem;
            transition: box-shadow 0.2s ease;
        }

        .use-case-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .use-case-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .use-case-id {
            font-family: monospace;
            font-weight: bold;
            color: #667eea;
        }

        .use-case-name {
            margin: 0.5rem 0 1rem 0;
            color: #333;
        }

        .use-case-metrics {
            margin: 1rem 0;
        }

        .metric-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .use-case-status {
            margin-top: 1rem;
            text-align: center;
        }

        .use-case-dependencies {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #e1e5e9;
            color: #666;
        }

        .program-status {
            display: flex;
            align-items: center;
        }

        .status-indicator {
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        /* Budget Tracking Styles */
        .budget-chart-container {
            background: white;
            border-radius: 8px;
            padding: 2rem;
            margin: 1.5rem 0;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .chart-legend {
            display: flex;
            gap: 1rem;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 2px;
        }

        .chart-area {
            display: flex;
            gap: 1rem;
        }

        .y-axis {
            display: flex;
            flex-direction: column-reverse;
            justify-content: space-between;
            height: 300px;
            padding-right: 1rem;
        }

        .y-label {
            font-size: 0.8rem;
            color: #666;
            text-align: right;
        }

        .chart-content {
            flex: 1;
            position: relative;
        }

        .budget-chart {
            width: 100%;
            height: 300px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
        }

        .x-axis-labels {
            display: flex;
            justify-content: space-around;
            margin-top: 1rem;
            font-size: 0.9rem;
            color: #666;
        }

        .milestones-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 1.5rem;
            margin-top: 1.5rem;
        }

        .milestone-card {
            background: white;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            padding: 1.5rem;
            transition: box-shadow 0.2s ease;
        }

        .milestone-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .milestone-completed {
            border-left: 4px solid #28a745;
        }

        .milestone-current {
            border-left: 4px solid #17a2b8;
        }

        .milestone-upcoming {
            border-left: 4px solid #ffc107;
        }

        .milestone-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .milestone-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #667eea;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .milestone-info {
            flex: 1;
        }

        .milestone-info h4 {
            margin: 0 0 0.25rem 0;
            color: #333;
        }

        .milestone-date {
            font-size: 0.9rem;
            color: #666;
        }

        .milestone-metrics {
            margin: 1rem 0;
        }

        .metric-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .metric-label {
            color: #666;
        }

        .metric-value {
            font-weight: 500;
        }

        .metric-value.positive {
            color: #28a745;
        }

        .metric-value.negative {
            color: #dc3545;
        }

        .metric-value.warning {
            color: #ffc107;
        }

        .milestone-progress {
            margin: 1rem 0;
        }

        .milestone-approvals {
            margin-top: 1.5rem;
        }

        .milestone-approvals h5 {
            margin: 0 0 0.75rem 0;
            color: #333;
            font-size: 0.9rem;
        }

        .approval-list {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .approval-item {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
            padding: 0.5rem;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .approval-item.approved {
            background: #d4edda;
            color: #155724;
        }

        .approval-item.pending {
            background: #fff3cd;
            color: #856404;
        }

        .approval-item.not-started {
            background: #f8f9fa;
            color: #6c757d;
        }

        .approval-item small {
            font-size: 0.8rem;
            opacity: 0.8;
        }

        .status-completed {
            background: #28a745;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.8rem;
        }

        .status-in-progress {
            background: #17a2b8;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.8rem;
        }

        .status-upcoming {
            background: #ffc107;
            color: #212529;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.8rem;
        }

        .budget-summary {
            margin: 1.5rem 0;
        }

        .summary-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .summary-card {
            background: white;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            padding: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .summary-icon {
            font-size: 2rem;
        }

        .summary-content {
            flex: 1;
        }

        .summary-label {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 0.25rem;
        }

        .summary-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 0.25rem;
        }

        .summary-detail {
            font-size: 0.8rem;
            color: #666;
        }

        .budget-alerts {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
        }

        .budget-alerts h4 {
            margin-top: 0;
            color: #333;
        }

        .alert-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .alert-item {
            display: flex;
            gap: 1rem;
            padding: 1rem;
            border-radius: 6px;
        }

        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
        }

        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
        }

        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
        }

        .alert-icon {
            font-size: 1.2rem;
        }

        .alert-content {
            flex: 1;
        }

        .alert-content strong {
            color: #333;
            margin-bottom: 0.5rem;
            display: block;
        }

        .alert-content p {
            margin: 0.5rem 0;
            color: #666;
        }

        .alert-content small {
            color: #666;
            font-size: 0.8rem;
        }
    </style>
    <script>
        function showTab(tabId) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // Remove active class from all tab buttons
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => button.classList.remove('active'));

            // Show selected tab content
            document.getElementById(tabId).classList.add('active');

            // Add active class to clicked tab button
            event.target.classList.add('active');
        }

        function showSection(sectionId) {
            // Hide all sections
            const sections = document.querySelectorAll('.section-content');
            sections.forEach(section => section.style.display = 'none');

            // Remove active class from all section buttons
            const sectionButtons = document.querySelectorAll('.section-btn');
            sectionButtons.forEach(button => button.classList.remove('active'));

            // Show selected section
            document.getElementById(sectionId).style.display = 'block';

            // Add active class to clicked section button
            event.target.classList.add('active');
        }

        // Save Draft functionality
        function saveDraft() {
            // Collect all form data
            const projectData = {
                parameters: {
                    discountRate: document.querySelector('input[value="10"]').value,
                    taxRate: document.querySelector('input[value="25"]').value,
                    inflationRate: document.querySelector('input[value="3"]').value,
                    projectDuration: document.querySelector('input[value="10"]:last-of-type').value,
                    baseCurrency: document.querySelector('select').value
                },
                timestamp: new Date().toISOString(),
                projectName: 'New Product Launch'
            };

            // Save to localStorage
            localStorage.setItem('businessCaseData', JSON.stringify(projectData));

            // Show success message
            showNotification('💾 Draft saved successfully!', 'success');
        }

        // Calculate Results functionality
        function calculateResults() {
            // Show loading state
            showNotification('⏳ Calculating financial metrics...', 'info');

            // Simulate calculation delay
            setTimeout(() => {
                // Update metrics with "calculated" values
                updateCalculatedMetrics();
                showNotification('✅ Results calculated successfully!', 'success');

                // Auto-switch to results tab
                showTab('results');
                document.querySelector('[onclick="showTab(\'results\')"]').classList.add('active');
                document.querySelectorAll('.tab-button').forEach(btn => {
                    if (!btn.onclick.toString().includes('results')) {
                        btn.classList.remove('active');
                    }
                });
            }, 2000);
        }

        // Export Report functionality
        function exportReport() {
            const exportData = {
                projectName: 'New Product Launch',
                generatedDate: new Date().toLocaleDateString(),
                metrics: {
                    npv: '$1,800,000',
                    irr: '15.2%',
                    payback: '4.1 years',
                    yieldIndex: '1.85'
                },
                recommendation: 'RECOMMENDED'
            };

            // Create and download CSV
            const csvContent = generateCSVReport(exportData);
            downloadFile(csvContent, 'business_case_report.csv', 'text/csv');

            showNotification('📊 Report exported successfully!', 'success');
        }

        // Helper functions
        function showNotification(message, type) {
            // Remove existing notifications
            const existing = document.querySelector('.notification');
            if (existing) existing.remove();

            // Create notification
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <div style="
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: ${type === 'success' ? '#d4edda' : type === 'info' ? '#d1ecf1' : '#fff3cd'};
                    color: ${type === 'success' ? '#155724' : type === 'info' ? '#0c5460' : '#856404'};
                    padding: 1rem 1.5rem;
                    border-radius: 6px;
                    border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'info' ? '#bee5eb' : '#ffeaa7'};
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    z-index: 10000;
                    font-weight: 500;
                    max-width: 300px;
                ">
                    ${message}
                </div>
            `;

            document.body.appendChild(notification);

            // Auto-remove after 3 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 3000);
        }

        function updateCalculatedMetrics() {
            // Simulate updated calculations with slight variations
            const variations = [
                { npv: '$1,850,000', irr: '15.7%', payback: '3.9 years' },
                { npv: '$1,780,000', irr: '14.8%', payback: '4.3 years' },
                { npv: '$1,820,000', irr: '15.4%', payback: '4.0 years' }
            ];

            const randomVariation = variations[Math.floor(Math.random() * variations.length)];

            // Update the metrics in the Financial Indexes tab
            const metricsCards = document.querySelectorAll('#results .metric-value');
            if (metricsCards.length >= 3) {
                metricsCards[0].textContent = randomVariation.npv;
                metricsCards[1].textContent = randomVariation.irr;
                metricsCards[2].textContent = randomVariation.payback;
            }
        }

        function generateCSVReport(data) {
            return `Business Case Analysis Report
Generated: ${data.generatedDate}
Project: ${data.projectName}

FINANCIAL METRICS
Metric,Value
Net Present Value,${data.metrics.npv}
Internal Rate of Return,${data.metrics.irr}
Payback Period,${data.metrics.payback}
Yield Index,${data.metrics.yieldIndex}

RECOMMENDATION
${data.recommendation} - Strong financial metrics indicate a viable investment.

CASH FLOW ANALYSIS
Year,Revenue,Costs,Cash Flow,Cumulative
Year 1,$5000000,$4200000,-$2000000,-$2000000
Year 2,$8500000,$6800000,$1700000,-$300000
Year 3,$12000000,$8400000,$3600000,$3300000
Year 4,$18000000,$12600000,$5400000,$8700000
Year 5,$25000000,$17500000,$7500000,$16200000

SENSITIVITY ANALYSIS
Scenario,NPV,IRR,Payback
Best Case,$2500000,18.5%,3.2 years
Base Case,$1800000,15.2%,4.1 years
Worst Case,$950000,11.8%,5.8 years`;
        }

        function downloadFile(content, filename, mimeType) {
            const blob = new Blob([content], { type: mimeType });
            const url = URL.createObjectURL(blob);

            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            link.style.display = 'none';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            URL.revokeObjectURL(url);
        }

        // Roadmap section navigation
        function showRoadmapSection(sectionId) {
            // Hide all roadmap sections
            const sections = document.querySelectorAll('.roadmap-section');
            sections.forEach(section => section.style.display = 'none');

            // Remove active class from all roadmap buttons
            const buttons = document.querySelectorAll('.roadmap-btn');
            buttons.forEach(button => button.classList.remove('active'));

            // Show selected section
            document.getElementById(sectionId).style.display = 'block';

            // Add active class to clicked button
            event.target.classList.add('active');
        }

        // Load saved data on page load
        window.onload = function() {
            const savedData = localStorage.getItem('businessCaseData');
            if (savedData) {
                showNotification('📂 Previous draft loaded', 'info');
            }
        }
    </script>
</head>
<body>
    <div class="app">
        <header class="app-header">
            <div class="header-content">
                <h1>🎯 Master Business Case Governance Platform</h1>
                <div class="project-info">
                    <span class="project-name">Program: Digital Transformation Initiative</span>
                    <span class="last-saved">Last updated: December 19, 2024</span>
                </div>
            </div>
        </header>

        <nav class="tab-navigation">
            <button class="tab-button active" onclick="showTab('master')">
                <span>🎯</span>
                <span>Master Business Case</span>
            </button>
            <button class="tab-button" onclick="showTab('governance')">
                <span>🏛️</span>
                <span>PMI/PGMP Compliance</span>
            </button>
            <button class="tab-button" onclick="showTab('stakeholders')">
                <span>👥</span>
                <span>Stakeholder Feedback</span>
            </button>
            <button class="tab-button" onclick="showTab('parameters')">
                <span>⚙️</span>
                <span>Financial Parameters</span>
            </button>
            <button class="tab-button" onclick="showTab('costs')">
                <span>💰</span>
                <span>Cost & Sales Definition</span>
            </button>
            <button class="tab-button" onclick="showTab('sensitivity')">
                <span>📊</span>
                <span>Sensitivity Analysis</span>
            </button>
            <button class="tab-button" onclick="showTab('results')">
                <span>📈</span>
                <span>Financial Indexes</span>
            </button>
            <button class="tab-button" onclick="showTab('roadmap')">
                <span>🗺️</span>
                <span>Change Management & Roadmap</span>
            </button>
            <button class="tab-button" onclick="showTab('budget-tracking')">
                <span>📊</span>
                <span>Budget vs Cost Tracking</span>
            </button>
        </nav>

        <main class="main-content">
            <!-- Master Business Case Tab -->
            <div id="master" class="tab-content active">
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">🎯 Program Overview</h2>
                        <div class="program-status">
                            <span class="status-indicator status-success">Active</span>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Program Name</label>
                            <input type="text" class="form-input" value="Digital Transformation Initiative">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Program Manager</label>
                            <input type="text" class="form-input" value="Sarah Johnson" placeholder="Enter program manager name">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Executive Sponsor</label>
                            <input type="text" class="form-input" value="John Smith - CEO" placeholder="Enter sponsor name">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Strategic Alignment</label>
                            <select class="form-input">
                                <option value="High" selected>High</option>
                                <option value="Medium">Medium</option>
                                <option value="Low">Low</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Business Driver</label>
                            <select class="form-input">
                                <option value="Market Expansion" selected>Market Expansion</option>
                                <option value="Cost Reduction">Cost Reduction</option>
                                <option value="Regulatory Compliance">Regulatory Compliance</option>
                                <option value="Digital Transformation">Digital Transformation</option>
                                <option value="Customer Experience">Customer Experience</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Investment Category</label>
                            <select class="form-input">
                                <option value="Strategic" selected>Strategic</option>
                                <option value="Operational">Operational</option>
                                <option value="Mandatory">Mandatory</option>
                                <option value="Infrastructure">Infrastructure</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Total Budget ($)</label>
                            <input type="number" class="form-input" value="50000000">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Duration (Months)</label>
                            <input type="number" class="form-input" value="36">
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">📊 Program Metrics Dashboard</h2>
                    </div>

                    <div class="financial-metrics">
                        <div class="metric-card">
                            <div class="metric-icon">💰</div>
                            <div class="form-label">Total Investment</div>
                            <div class="metric-value metric-neutral">$50.0M</div>
                            <div style="font-size: 0.9rem; color: #666;">Program budget allocation</div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-icon">📈</div>
                            <div class="form-label">Expected ROI</div>
                            <div class="metric-value metric-positive">18.6%</div>
                            <div style="font-size: 0.9rem; color: #666;">Weighted average across use cases</div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-icon">🎯</div>
                            <div class="form-label">Use Cases</div>
                            <div class="metric-value metric-neutral">3</div>
                            <div style="font-size: 0.9rem; color: #666;">Active business cases</div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-icon">⏱️</div>
                            <div class="form-label">Timeline</div>
                            <div class="metric-value metric-neutral">36 months</div>
                            <div style="font-size: 0.9rem; color: #666;">Program duration</div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">📋 Use Cases Portfolio</h2>
                        <button class="btn btn-primary">+ Add Use Case</button>
                    </div>

                    <div class="use-cases-grid">
                        <div class="use-case-card">
                            <div class="use-case-header">
                                <div class="use-case-id">UC001</div>
                                <div class="use-case-priority" style="background-color: #dc3545; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">High</div>
                            </div>

                            <h4 class="use-case-name">Customer Portal Enhancement</h4>

                            <div class="use-case-metrics">
                                <div class="metric-row">
                                    <span>Budget:</span>
                                    <span>$5.0M</span>
                                </div>
                                <div class="metric-row">
                                    <span>Timeline:</span>
                                    <span>12 months</span>
                                </div>
                                <div class="metric-row">
                                    <span>ROI:</span>
                                    <span class="metric-positive">18.5%</span>
                                </div>
                                <div class="metric-row">
                                    <span>NPV:</span>
                                    <span class="metric-positive">$3.2M</span>
                                </div>
                            </div>

                            <div class="use-case-status">
                                <span class="status-badge" style="background-color: #17a2b8; color: white; padding: 0.5rem 1rem; border-radius: 6px; font-size: 0.9rem;">Planning</span>
                            </div>

                            <div class="use-case-dependencies">
                                <small>Dependencies: UC002</small>
                            </div>
                        </div>

                        <div class="use-case-card">
                            <div class="use-case-header">
                                <div class="use-case-id">UC002</div>
                                <div class="use-case-priority" style="background-color: #dc3545; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">High</div>
                            </div>

                            <h4 class="use-case-name">Data Analytics Platform</h4>

                            <div class="use-case-metrics">
                                <div class="metric-row">
                                    <span>Budget:</span>
                                    <span>$8.0M</span>
                                </div>
                                <div class="metric-row">
                                    <span>Timeline:</span>
                                    <span>18 months</span>
                                </div>
                                <div class="metric-row">
                                    <span>ROI:</span>
                                    <span class="metric-positive">22.3%</span>
                                </div>
                                <div class="metric-row">
                                    <span>NPV:</span>
                                    <span class="metric-positive">$5.8M</span>
                                </div>
                            </div>

                            <div class="use-case-status">
                                <span class="status-badge" style="background-color: #fd7e14; color: white; padding: 0.5rem 1rem; border-radius: 6px; font-size: 0.9rem;">Analysis</span>
                            </div>
                        </div>

                        <div class="use-case-card">
                            <div class="use-case-header">
                                <div class="use-case-id">UC003</div>
                                <div class="use-case-priority" style="background-color: #ffc107; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">Medium</div>
                            </div>

                            <h4 class="use-case-name">Mobile Application</h4>

                            <div class="use-case-metrics">
                                <div class="metric-row">
                                    <span>Budget:</span>
                                    <span>$3.0M</span>
                                </div>
                                <div class="metric-row">
                                    <span>Timeline:</span>
                                    <span>9 months</span>
                                </div>
                                <div class="metric-row">
                                    <span>ROI:</span>
                                    <span class="metric-positive">15.2%</span>
                                </div>
                                <div class="metric-row">
                                    <span>NPV:</span>
                                    <span class="metric-positive">$1.8M</span>
                                </div>
                            </div>

                            <div class="use-case-status">
                                <span class="status-badge" style="background-color: #6f42c1; color: white; padding: 0.5rem 1rem; border-radius: 6px; font-size: 0.9rem;">Concept</span>
                            </div>

                            <div class="use-case-dependencies">
                                <small>Dependencies: UC001</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Parameters Setup Tab -->
            <div id="parameters" class="tab-content">
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">⚙️ Financial Parameters</h2>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Discount Rate (%)</label>
                            <input type="number" class="form-input" value="10" step="0.1">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Tax Rate (%)</label>
                            <input type="number" class="form-input" value="25" step="0.1">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Inflation Rate (%)</label>
                            <input type="number" class="form-input" value="3" step="0.1">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Project Duration (Years)</label>
                            <input type="number" class="form-input" value="10">
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">💱 Currency & Exchange Rates</h2>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Base Currency</label>
                            <select class="form-input">
                                <option value="USD">USD - US Dollar</option>
                                <option value="EUR">EUR - Euro</option>
                                <option value="GBP">GBP - British Pound</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">EUR Exchange Rate</label>
                            <input type="number" class="form-input" value="0.85" step="0.01">
                        </div>
                        <div class="form-group">
                            <label class="form-label">GBP Exchange Rate</label>
                            <input type="number" class="form-input" value="0.73" step="0.01">
                        </div>
                        <div class="form-group">
                            <label class="form-label">JPY Exchange Rate</label>
                            <input type="number" class="form-input" value="110" step="0.01">
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">📈 Inflation Adjustments</h2>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                        <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                            <input type="checkbox" checked>
                            <span>Apply inflation to R&D costs</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                            <input type="checkbox" checked>
                            <span>Apply inflation to Production costs</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                            <input type="checkbox">
                            <span>Apply inflation to Sales prices</span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Cost & Sales Definition Tab -->
            <div id="costs" class="tab-content">
                <div class="section-navigation">
                    <button class="section-btn active" onclick="showSection('capex')">
                        <span>🏗️</span>
                        <span>CAPEX</span>
                    </button>
                    <button class="section-btn" onclick="showSection('opex')">
                        <span>🔄</span>
                        <span>OPEX</span>
                    </button>
                    <button class="section-btn" onclick="showSection('tools')">
                        <span>🛠️</span>
                        <span>Tools</span>
                    </button>
                    <button class="section-btn" onclick="showSection('machinery')">
                        <span>⚙️</span>
                        <span>Machinery</span>
                    </button>
                    <button class="section-btn" onclick="showSection('sales')">
                        <span>💰</span>
                        <span>Sales</span>
                    </button>
                </div>

                <div id="capex" class="section-content card" style="display: block;">
                    <div class="section-header">
                        <h3>🏗️ CAPEX Items</h3>
                        <button class="btn btn-primary">+ Add Item</button>
                    </div>

                    <div class="cost-item">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Name</label>
                                <input type="text" class="form-input" value="Equipment">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Amount ($)</label>
                                <input type="number" class="form-input" value="500000">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Year</label>
                                <input type="number" class="form-input" value="1">
                            </div>
                        </div>
                    </div>

                    <div class="cost-item">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Name</label>
                                <input type="text" class="form-input" value="Facility Setup">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Amount ($)</label>
                                <input type="number" class="form-input" value="200000">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Year</label>
                                <input type="number" class="form-input" value="1">
                            </div>
                        </div>
                    </div>
                </div>

                <div id="opex" class="section-content card" style="display: none;">
                    <div class="section-header">
                        <h3>🔄 OPEX Items</h3>
                        <button class="btn btn-primary">+ Add Item</button>
                    </div>

                    <div class="cost-item">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Name</label>
                                <input type="text" class="form-input" value="Personnel">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Amount ($)</label>
                                <input type="number" class="form-input" value="150000">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Recurring</label>
                                <input type="checkbox" checked>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="tools" class="section-content card" style="display: none;">
                    <div class="section-header">
                        <h3>🛠️ Tools & Equipment</h3>
                        <button class="btn btn-primary">+ Add Item</button>
                    </div>

                    <div class="cost-item">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Name</label>
                                <input type="text" class="form-input" value="Software Licenses">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Amount ($)</label>
                                <input type="number" class="form-input" value="50000">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Lifespan (Years)</label>
                                <input type="number" class="form-input" value="3">
                            </div>
                        </div>
                    </div>
                </div>

                <div id="machinery" class="section-content card" style="display: none;">
                    <div class="section-header">
                        <h3>⚙️ Machinery</h3>
                        <button class="btn btn-primary">+ Add Item</button>
                    </div>

                    <div class="cost-item">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Name</label>
                                <input type="text" class="form-input" value="Production Line A">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Amount ($)</label>
                                <input type="number" class="form-input" value="800000">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Capacity</label>
                                <input type="number" class="form-input" value="10000">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Year</label>
                                <input type="number" class="form-input" value="1">
                            </div>
                        </div>
                    </div>
                </div>

                <div id="sales" class="section-content card" style="display: none;">
                    <div class="section-header">
                        <h3>💰 Sales Configuration</h3>
                    </div>

                    <h4>🌍 Sales by Region</h4>
                    <div class="cost-item">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Region</label>
                                <input type="text" class="form-input" value="North America" readonly>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Market Share (%)</label>
                                <input type="number" class="form-input" value="40">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Growth Rate (%)</label>
                                <input type="number" class="form-input" value="5">
                            </div>
                        </div>
                    </div>

                    <h4 style="margin-top: 2rem;">📦 Product Offers</h4>
                    <div class="cost-item">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Offer Name</label>
                                <input type="text" class="form-input" value="Premium Package">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Price ($)</label>
                                <input type="number" class="form-input" value="1200">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Margin (%)</label>
                                <input type="number" class="form-input" value="35">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Volume (Units)</label>
                                <input type="number" class="form-input" value="5000">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sensitivity Analysis Tab -->
            <div id="sensitivity" class="tab-content">
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">📊 Sensitivity Variables</h2>
                        <button class="btn btn-primary">🔄 Recalculate Scenarios</button>
                    </div>

                    <div class="sensitivity-grid">
                        <div class="variable-card">
                            <div class="variable-header">
                                <span class="variable-name">Sales Price</span>
                                <span class="impact-badge impact-high">high impact</span>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Base Value</label>
                                <input type="number" class="form-input" value="1000">
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">Minimum</label>
                                    <input type="number" class="form-input" value="800">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Maximum</label>
                                    <input type="number" class="form-input" value="1200">
                                </div>
                            </div>
                        </div>

                        <div class="variable-card">
                            <div class="variable-header">
                                <span class="variable-name">Sales Volume</span>
                                <span class="impact-badge impact-high">high impact</span>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Base Value</label>
                                <input type="number" class="form-input" value="10000">
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">Minimum</label>
                                    <input type="number" class="form-input" value="8000">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Maximum</label>
                                    <input type="number" class="form-input" value="12000">
                                </div>
                            </div>
                        </div>

                        <div class="variable-card">
                            <div class="variable-header">
                                <span class="variable-name">Production Cost</span>
                                <span class="impact-badge impact-medium">medium impact</span>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Base Value</label>
                                <input type="number" class="form-input" value="600">
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">Minimum</label>
                                    <input type="number" class="form-input" value="500">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Maximum</label>
                                    <input type="number" class="form-input" value="700">
                                </div>
                            </div>
                        </div>

                        <div class="variable-card">
                            <div class="variable-header">
                                <span class="variable-name">Discount Rate</span>
                                <span class="impact-badge impact-medium">medium impact</span>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Base Value</label>
                                <input type="number" class="form-input" value="10">
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">Minimum</label>
                                    <input type="number" class="form-input" value="8">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Maximum</label>
                                    <input type="number" class="form-input" value="12">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">🎯 Scenario Analysis Results</h2>
                    </div>

                    <div class="scenario-results">
                        <div class="scenario-card scenario-best">
                            <h3>🟢 Best Case</h3>
                            <div style="margin-top: 1rem;">
                                <div><strong>NPV:</strong> $2,500,000</div>
                                <div><strong>IRR:</strong> 18.5%</div>
                                <div><strong>Payback:</strong> 3.2 years</div>
                            </div>
                        </div>

                        <div class="scenario-card scenario-base">
                            <h3>🟡 Base Case</h3>
                            <div style="margin-top: 1rem;">
                                <div><strong>NPV:</strong> $1,800,000</div>
                                <div><strong>IRR:</strong> 15.2%</div>
                                <div><strong>Payback:</strong> 4.1 years</div>
                            </div>
                        </div>

                        <div class="scenario-card scenario-worst">
                            <h3>🔴 Worst Case</h3>
                            <div style="margin-top: 1rem;">
                                <div><strong>NPV:</strong> $950,000</div>
                                <div><strong>IRR:</strong> 11.8%</div>
                                <div><strong>Payback:</strong> 5.8 years</div>
                            </div>
                        </div>
                    </div>

                    <div style="margin-top: 2rem;">
                        <h3>📋 Key Insights</h3>
                        <ul style="padding-left: 1.5rem; line-height: 1.6;">
                            <li>Sales price has the highest impact on project profitability</li>
                            <li>Even in worst-case scenario, NPV remains positive</li>
                            <li>IRR exceeds discount rate in all scenarios</li>
                            <li>Payback period varies from 3.2 to 5.8 years across scenarios</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Financial Indexes Tab -->
            <div id="results" class="tab-content">
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">📈 Key Financial Metrics</h2>
                        <div style="font-size: 0.9rem; color: #666;">
                            Calculated: December 19, 2024
                        </div>
                    </div>

                    <div class="financial-metrics">
                        <div class="metric-card">
                            <div style="font-size: 1.5rem;">💰</div>
                            <div class="form-label">Net Present Value</div>
                            <div class="metric-value metric-positive">$1,800,000</div>
                            <div style="font-size: 0.9rem; color: #666;">Total value created by the project</div>
                        </div>

                        <div class="metric-card">
                            <div style="font-size: 1.5rem;">📊</div>
                            <div class="form-label">Internal Rate of Return</div>
                            <div class="metric-value metric-positive">15.2%</div>
                            <div style="font-size: 0.9rem; color: #666;">Expected annual return rate</div>
                        </div>

                        <div class="metric-card">
                            <div style="font-size: 1.5rem;">⏱️</div>
                            <div class="form-label">Payback Period</div>
                            <div class="metric-value metric-neutral">4.1 years</div>
                            <div style="font-size: 0.9rem; color: #666;">Time to recover initial investment</div>
                        </div>

                        <div class="metric-card">
                            <div style="font-size: 1.5rem;">📈</div>
                            <div class="form-label">Yield Index</div>
                            <div class="metric-value metric-positive">1.85</div>
                            <div style="font-size: 0.9rem; color: #666;">Return per dollar invested</div>
                        </div>

                        <div class="metric-card">
                            <div style="font-size: 1.5rem;">💹</div>
                            <div class="form-label">Gross Margin</div>
                            <div class="metric-value metric-positive">28.5%</div>
                            <div style="font-size: 0.9rem; color: #666;">Profit margin on sales</div>
                        </div>

                        <div class="metric-card">
                            <div style="font-size: 1.5rem;">🎯</div>
                            <div class="form-label">Total Revenue</div>
                            <div class="metric-value metric-neutral">$125,000,000</div>
                            <div style="font-size: 0.9rem; color: #666;">Total project revenue</div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">📋 Cash Flow Analysis</h2>
                    </div>

                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Year</th>
                                <th>Revenue</th>
                                <th>Costs</th>
                                <th>Cash Flow</th>
                                <th>Cumulative</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Year 1</td>
                                <td>$5,000,000</td>
                                <td>$4,200,000</td>
                                <td class="negative">-$2,000,000</td>
                                <td class="negative">-$2,000,000</td>
                            </tr>
                            <tr>
                                <td>Year 2</td>
                                <td>$8,500,000</td>
                                <td>$6,800,000</td>
                                <td class="positive">$1,700,000</td>
                                <td class="negative">-$300,000</td>
                            </tr>
                            <tr>
                                <td>Year 3</td>
                                <td>$12,000,000</td>
                                <td>$8,400,000</td>
                                <td class="positive">$3,600,000</td>
                                <td class="positive">$3,300,000</td>
                            </tr>
                            <tr>
                                <td>Year 4</td>
                                <td>$18,000,000</td>
                                <td>$12,600,000</td>
                                <td class="positive">$5,400,000</td>
                                <td class="positive">$8,700,000</td>
                            </tr>
                            <tr>
                                <td>Year 5</td>
                                <td>$25,000,000</td>
                                <td>$17,500,000</td>
                                <td class="positive">$7,500,000</td>
                                <td class="positive">$16,200,000</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">📊 Interactive Charts</h2>
                    </div>

                    <div class="chart-container">
                        <h3>📊 Cash Flow Visualization</h3>
                        <div class="chart-placeholder">
                            📈 Interactive Cash Flow Chart<br>
                            <small>(Available in full React application)</small>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                        <div class="chart-container">
                            <h3>📈 NPV Sensitivity</h3>
                            <div class="chart-placeholder">
                                🎯 Sensitivity Analysis Chart<br>
                                <small>(Available in full React application)</small>
                            </div>
                        </div>

                        <div class="chart-container">
                            <h3>🎯 Performance Radar</h3>
                            <div class="chart-placeholder">
                                🕸️ Performance Radar Chart<br>
                                <small>(Available in full React application)</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">⚠️ Risk Assessment</h2>
                    </div>

                    <div style="margin-bottom: 2rem;">
                        <div style="font-size: 1.1rem; margin-bottom: 1rem;">
                            <strong>Investment Recommendation:</strong>
                            <span class="positive" style="font-weight: bold;">RECOMMENDED</span> - Strong financial metrics indicate a viable investment.
                        </div>
                    </div>

                    <div>
                        <h3>🚨 Key Risk Factors</h3>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 1rem;">
                            <div style="padding: 1rem; background-color: #d4edda; border-radius: 6px;">
                                <strong>Low Risk</strong>
                                <div>Strong NPV and IRR metrics</div>
                            </div>
                            <div style="padding: 1rem; background-color: #fff3cd; border-radius: 6px;">
                                <strong>Medium Risk</strong>
                                <div>Payback period over 4 years</div>
                            </div>
                            <div style="padding: 1rem; background-color: #f8d7da; border-radius: 6px;">
                                <strong>Monitor</strong>
                                <div>Market sensitivity to price changes</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Change Management & Roadmap Tab -->
            <div id="roadmap" class="tab-content">
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">🗺️ Governance Evolution Roadmap</h2>
                        <div style="font-size: 0.9rem; color: #666;">
                            Transformation from Current State to Proposed Governance Models
                        </div>
                    </div>

                    <div class="roadmap-navigation">
                        <button class="roadmap-btn active" onclick="showRoadmapSection('current')">
                            <span>📍</span>
                            <span>Current State</span>
                        </button>
                        <button class="roadmap-btn" onclick="showRoadmapSection('proposed1')">
                            <span>🔄</span>
                            <span>Proposed 1 - Medium Change</span>
                        </button>
                        <button class="roadmap-btn" onclick="showRoadmapSection('proposed2')">
                            <span>🚀</span>
                            <span>Proposed 2 - Big Change</span>
                        </button>
                    </div>

                    <!-- Current State -->
                    <div id="current" class="roadmap-section active">
                        <h3>📍 Current State</h3>
                        <div class="governance-table">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>Master BC Framework</th>
                                        <th>PPM</th>
                                        <th>Change Management</th>
                                        <th>Milestone for CoA</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><strong>BC1</strong></td>
                                        <td><strong>EPIC</strong></td>
                                        <td>-</td>
                                        <td>-</td>
                                    </tr>
                                    <tr>
                                        <td style="padding-left: 2rem;">Offer1</td>
                                        <td>NA</td>
                                        <td>-</td>
                                        <td>-</td>
                                    </tr>
                                    <tr>
                                        <td style="padding-left: 2rem;">Offer2</td>
                                        <td>NA</td>
                                        <td>-</td>
                                        <td>-</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="current-issues">
                            <h4>🚨 Current Issues:</h4>
                            <ul>
                                <li>Multiple offers per business case without clear governance</li>
                                <li>No structured change management process</li>
                                <li>Lack of milestone-based approvals</li>
                                <li>Disconnected WebOnyx and PPM systems</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Proposed 1 - Medium Change -->
                    <div id="proposed1" class="roadmap-section" style="display: none;">
                        <h3>🔄 Proposed 1 - Medium Change Management Required</h3>
                        <div class="governance-table">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>Master BC Framework</th>
                                        <th>PPM</th>
                                        <th>Change Management</th>
                                        <th>Milestone for CoA</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><strong>BC1</strong></td>
                                        <td><strong>Offer/Program</strong></td>
                                        <td>Medium</td>
                                        <td><span class="status-badge" style="background: #28a745; color: white; padding: 0.25rem 0.5rem; border-radius: 4px;">Approve</span></td>
                                    </tr>
                                    <tr>
                                        <td style="padding-left: 2rem;">Offer1</td>
                                        <td>EPIC/Project</td>
                                        <td>-</td>
                                        <td>No CoA for individual EPIC/Project</td>
                                    </tr>
                                    <tr>
                                        <td style="padding-left: 2rem;">Offer2</td>
                                        <td>EPIC/Project</td>
                                        <td>-</td>
                                        <td>No CoA for individual EPIC/Project</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="proposed-benefits">
                            <h4>✅ Benefits:</h4>
                            <ul>
                                <li>Structured approval process at program level</li>
                                <li>Clear separation between program and project governance</li>
                                <li>Reduced approval overhead for individual projects</li>
                                <li>Better alignment between Master BC Framework and PPM</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Proposed 2 - Big Change -->
                    <div id="proposed2" class="roadmap-section" style="display: none;">
                        <h3>🚀 Proposed 2 - Big Change Management</h3>

                        <div class="sequential-approach">
                            <h4>📋 Sequential Implementation Approach</h4>

                            <div class="sequence-step">
                                <div class="step-header">
                                    <span class="step-number">Seq1</span>
                                    <h5>BC Master - Program Level</h5>
                                </div>
                                <div class="governance-table">
                                    <table class="data-table">
                                        <thead>
                                            <tr>
                                                <th>WebOnyx</th>
                                                <th>PPM</th>
                                                <th>Milestone for CoA</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><strong>BC Master</strong></td>
                                                <td><strong>Offer/Program</strong></td>
                                                <td><span class="status-badge" style="background: #28a745; color: white; padding: 0.25rem 0.5rem; border-radius: 4px;">Approve</span></td>
                                            </tr>
                                            <tr>
                                                <td style="padding-left: 2rem;">BC1</td>
                                                <td>-</td>
                                                <td>-</td>
                                            </tr>
                                            <tr>
                                                <td style="padding-left: 2rem;">BC2</td>
                                                <td>-</td>
                                                <td>-</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <div class="sequence-step">
                                <div class="step-header">
                                    <span class="step-number">Seq2</span>
                                    <h5>BC1 - Check Pulse MVP1</h5>
                                </div>
                                <div class="governance-table">
                                    <table class="data-table">
                                        <thead>
                                            <tr>
                                                <th>Master BC Framework</th>
                                                <th>PPM</th>
                                                <th>Milestone for CoA</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><strong>BC1 (check pulse MVP1)</strong></td>
                                                <td><strong>EPIC/Project</strong></td>
                                                <td><span class="status-badge" style="background: #28a745; color: white; padding: 0.25rem 0.5rem; border-radius: 4px;">Approve</span></td>
                                            </tr>
                                            <tr>
                                                <td style="padding-left: 2rem;">Offer1</td>
                                                <td>NA</td>
                                                <td>-</td>
                                            </tr>
                                            <tr>
                                                <td style="padding-left: 2rem;">Offer2</td>
                                                <td>NA</td>
                                                <td>-</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <div class="sequence-step">
                                <div class="step-header">
                                    <span class="step-number">Seq3</span>
                                    <h5>BC2 - Grow Phase</h5>
                                </div>
                                <div class="governance-table">
                                    <table class="data-table">
                                        <thead>
                                            <tr>
                                                <th>Master BC Framework</th>
                                                <th>PPM</th>
                                                <th>Milestone for CoA</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><strong>BC2 (Grow)</strong></td>
                                                <td><strong>EPIC/Project</strong></td>
                                                <td><span class="status-badge" style="background: #28a745; color: white; padding: 0.25rem 0.5rem; border-radius: 4px;">Approve</span></td>
                                            </tr>
                                            <tr>
                                                <td style="padding-left: 2rem;">Offer1</td>
                                                <td>NA</td>
                                                <td>-</td>
                                            </tr>
                                            <tr>
                                                <td style="padding-left: 2rem;">Offer2</td>
                                                <td>NA</td>
                                                <td>-</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="key-principle">
                            <h4>🎯 Key Principle:</h4>
                            <div class="principle-box">
                                <strong>No more multiple offers as BC is connected with only one EPIC/Program/Offer</strong>
                                <p>This ensures clear governance boundaries and eliminates confusion in the approval process.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">📊 Program Structure & Timeline</h2>
                    </div>

                    <div class="program-structure">
                        <div class="structure-level">
                            <h4>🏢 Program Level (Multi-year)</h4>
                            <div class="structure-details">
                                <div class="structure-item">
                                    <strong>Program:</strong> Digital Transformation Initiative
                                    <div class="timeline">Duration: Multi-year strategic initiative</div>
                                </div>
                            </div>
                        </div>

                        <div class="structure-level">
                            <h4>📋 Project Level (6-12 months)</h4>
                            <div class="structure-details">
                                <div class="structure-item">
                                    <strong>Project 1:</strong> Customer Portal Enhancement
                                    <div class="scope">Scope: Frontend redesign, UX improvements, mobile optimization</div>
                                    <div class="timeline">Time: 12 months | Cost: $5M</div>
                                </div>
                                <div class="structure-item">
                                    <strong>Project 2:</strong> Data Analytics Platform
                                    <div class="scope">Scope: Data warehouse, analytics tools, reporting dashboard</div>
                                    <div class="timeline">Time: 18 months | Cost: $8M</div>
                                </div>
                            </div>
                        </div>

                        <div class="structure-level">
                            <h4>🔄 PI Level (1 Quarter)</h4>
                            <div class="structure-details">
                                <div class="structure-item">
                                    <strong>PI2:</strong> Optimization Value
                                    <div class="pi-versions">
                                        <div class="version-item">
                                            <span>V3.1 Maintenance</span>
                                            <span>V3.2 GLL (Global Language Localization)</span>
                                        </div>
                                    </div>
                                    <div class="business-justification">Business Justification: Performance optimization and global expansion</div>
                                </div>
                            </div>
                        </div>

                        <div class="structure-level">
                            <h4>⚡ Sprint Level (1-4 weeks)</h4>
                            <div class="sprint-timeline">
                                <div class="sprint-phase">
                                    <div class="phase-duration">6 months</div>
                                    <div class="phase-breakdown">
                                        <div class="requirement">
                                            <span>Req1:</span>
                                            <div class="req-phases">
                                                <span>Implement (3 weeks)</span>
                                                <span>Verification (3 months)</span>
                                                <span>Validation</span>
                                            </div>
                                        </div>
                                        <div class="requirement">
                                            <span>Req2:</span>
                                            <div class="req-phases">
                                                <span>Implement</span>
                                                <span>Verification</span>
                                                <span>DevOps, Automation</span>
                                            </div>
                                        </div>
                                        <div class="requirement">
                                            <span>Req3:</span>
                                            <div class="req-phases">
                                                <span>Implement</span>
                                                <span>Verification</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="sprint-phase">
                                    <div class="phase-duration">1.5 months</div>
                                    <div class="phase-breakdown">
                                        <div class="requirement">
                                            <span>Req1:</span>
                                            <div class="req-phases">
                                                <span>Implement</span>
                                                <span>Verification</span>
                                                <span>Validation</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">🎯 Implementation Recommendations</h2>
                    </div>

                    <div class="recommendations">
                        <div class="recommendation-item">
                            <h4>🔄 Phase 1: Medium Change (Proposed 1)</h4>
                            <ul>
                                <li>Implement program-level approvals in Master BC Framework</li>
                                <li>Establish clear governance between programs and projects</li>
                                <li>Reduce approval overhead for individual EPICs</li>
                                <li>Timeline: 3-6 months</li>
                            </ul>
                        </div>

                        <div class="recommendation-item">
                            <h4>🚀 Phase 2: Big Change (Proposed 2)</h4>
                            <ul>
                                <li>Implement Master Business Case concept</li>
                                <li>Sequential approval process (Seq1 → Seq2 → Seq3)</li>
                                <li>One-to-one BC to EPIC/Program mapping</li>
                                <li>Timeline: 6-12 months</li>
                            </ul>
                        </div>

                        <div class="recommendation-item">
                            <h4>📊 Success Metrics</h4>
                            <ul>
                                <li>Reduced approval cycle time by 40%</li>
                                <li>Improved governance compliance to 95%</li>
                                <li>Enhanced stakeholder satisfaction scores</li>
                                <li>Better alignment between strategic and operational planning</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Budget vs Cost Tracking Tab -->
            <div id="budget-tracking" class="tab-content">
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">📊 Budget vs Cost Approval vs Milestones</h2>
                        <div style="font-size: 0.9rem; color: #666;">
                            Real-time tracking of budget allocation, actual costs, and milestone-based approvals
                        </div>
                    </div>

                    <!-- Chart Visualization -->
                    <div class="budget-chart-container">
                        <div class="chart-header">
                            <h3>Budget vs Actual Cost Progression</h3>
                            <div class="chart-legend">
                                <div class="legend-item">
                                    <div class="legend-color" style="background: #4285f4;"></div>
                                    <span>Budget</span>
                                </div>
                                <div class="legend-item">
                                    <div class="legend-color" style="background: #ea4335;"></div>
                                    <span>Actual Cost</span>
                                </div>
                            </div>
                        </div>

                        <div class="chart-area">
                            <div class="y-axis">
                                <div class="y-label">450</div>
                                <div class="y-label">400</div>
                                <div class="y-label">350</div>
                                <div class="y-label">300</div>
                                <div class="y-label">250</div>
                                <div class="y-label">200</div>
                                <div class="y-label">150</div>
                                <div class="y-label">100</div>
                                <div class="y-label">50</div>
                                <div class="y-label">0</div>
                            </div>

                            <div class="chart-content">
                                <svg viewBox="0 0 400 300" class="budget-chart">
                                    <!-- Grid lines -->
                                    <defs>
                                        <pattern id="grid" width="40" height="30" patternUnits="userSpaceOnUse">
                                            <path d="M 40 0 L 0 0 0 30" fill="none" stroke="#e0e0e0" stroke-width="1"/>
                                        </pattern>
                                    </defs>
                                    <rect width="100%" height="100%" fill="url(#grid)" />

                                    <!-- Budget line (blue) -->
                                    <polyline points="40,240 160,200 280,160"
                                             fill="none" stroke="#4285f4" stroke-width="3"
                                             marker-end="url(#blueDot)"/>

                                    <!-- Actual Cost line (red) -->
                                    <polyline points="40,240 160,180 280,60"
                                             fill="none" stroke="#ea4335" stroke-width="3"
                                             marker-end="url(#redDot)"/>

                                    <!-- Data points -->
                                    <circle cx="40" cy="240" r="4" fill="#4285f4"/>
                                    <circle cx="160" cy="200" r="4" fill="#4285f4"/>
                                    <circle cx="280" cy="160" r="4" fill="#4285f4"/>

                                    <circle cx="40" cy="240" r="4" fill="#ea4335"/>
                                    <circle cx="160" cy="180" r="4" fill="#ea4335"/>
                                    <circle cx="280" cy="60" r="4" fill="#ea4335"/>

                                    <!-- Milestone markers -->
                                    <rect x="35" y="10" width="80" height="40" fill="#f8f9fa" stroke="#667eea" stroke-width="1" rx="4"/>
                                    <text x="75" y="25" text-anchor="middle" font-size="10" fill="#333">Stakeholder</text>
                                    <text x="75" y="35" text-anchor="middle" font-size="10" fill="#333">Approval</text>
                                    <text x="75" y="45" text-anchor="middle" font-size="10" fill="#333">Milestone1</text>

                                    <rect x="320" y="10" width="70" height="40" fill="#f8f9fa" stroke="#667eea" stroke-width="1" rx="4"/>
                                    <text x="355" y="25" text-anchor="middle" font-size="10" fill="#333">Stakeholder</text>
                                    <text x="355" y="35" text-anchor="middle" font-size="10" fill="#333">Approval</text>
                                    <text x="355" y="45" text-anchor="middle" font-size="10" fill="#333">Milestone3</text>

                                    <rect x="200" y="120" width="70" height="30" fill="#f8f9fa" stroke="#667eea" stroke-width="1" rx="4"/>
                                    <text x="235" y="135" text-anchor="middle" font-size="10" fill="#333">Stakeholder</text>
                                    <text x="235" y="145" text-anchor="middle" font-size="10" fill="#333">Approval</text>
                                </svg>

                                <div class="x-axis-labels">
                                    <span>Milestone 1</span>
                                    <span>Milestone 2</span>
                                    <span>Milestone 3</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">📋 Milestone Details & Approval Status</h2>
                    </div>

                    <div class="milestones-grid">
                        <div class="milestone-card milestone-completed">
                            <div class="milestone-header">
                                <div class="milestone-number">1</div>
                                <div class="milestone-info">
                                    <h4>Project Initiation</h4>
                                    <div class="milestone-date">Completed: Jan 15, 2024</div>
                                </div>
                                <div class="milestone-status">
                                    <span class="status-badge status-completed">✅ Completed</span>
                                </div>
                            </div>

                            <div class="milestone-metrics">
                                <div class="metric-item">
                                    <span class="metric-label">Budget Allocated:</span>
                                    <span class="metric-value">$100K</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-label">Actual Cost:</span>
                                    <span class="metric-value">$95K</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-label">Variance:</span>
                                    <span class="metric-value positive">-$5K (5% under)</span>
                                </div>
                            </div>

                            <div class="milestone-approvals">
                                <h5>Stakeholder Approvals:</h5>
                                <div class="approval-list">
                                    <div class="approval-item approved">
                                        <span>✅ John Smith (CEO)</span>
                                        <small>Approved: Jan 12, 2024</small>
                                    </div>
                                    <div class="approval-item approved">
                                        <span>✅ Sarah Johnson (CTO)</span>
                                        <small>Approved: Jan 14, 2024</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="milestone-card milestone-current">
                            <div class="milestone-header">
                                <div class="milestone-number">2</div>
                                <div class="milestone-info">
                                    <h4>Development Phase</h4>
                                    <div class="milestone-date">Target: Mar 15, 2024</div>
                                </div>
                                <div class="milestone-status">
                                    <span class="status-badge status-in-progress">🔄 In Progress</span>
                                </div>
                            </div>

                            <div class="milestone-metrics">
                                <div class="metric-item">
                                    <span class="metric-label">Budget Allocated:</span>
                                    <span class="metric-value">$200K</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-label">Actual Cost:</span>
                                    <span class="metric-value">$180K</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-label">Variance:</span>
                                    <span class="metric-value positive">-$20K (10% under)</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-label">Progress:</span>
                                    <span class="metric-value">75% Complete</span>
                                </div>
                            </div>

                            <div class="milestone-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 75%; background: #28a745;"></div>
                                </div>
                            </div>

                            <div class="milestone-approvals">
                                <h5>Pending Approvals:</h5>
                                <div class="approval-list">
                                    <div class="approval-item pending">
                                        <span>⏳ Mike Chen (Head of Sales)</span>
                                        <small>Pending review</small>
                                    </div>
                                    <div class="approval-item approved">
                                        <span>✅ Lisa Rodriguez (Customer Success)</span>
                                        <small>Approved: Feb 28, 2024</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="milestone-card milestone-upcoming">
                            <div class="milestone-header">
                                <div class="milestone-number">3</div>
                                <div class="milestone-info">
                                    <h4>Production Deployment</h4>
                                    <div class="milestone-date">Target: May 15, 2024</div>
                                </div>
                                <div class="milestone-status">
                                    <span class="status-badge status-upcoming">📅 Upcoming</span>
                                </div>
                            </div>

                            <div class="milestone-metrics">
                                <div class="metric-item">
                                    <span class="metric-label">Budget Allocated:</span>
                                    <span class="metric-value">$400K</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-label">Projected Cost:</span>
                                    <span class="metric-value">$420K</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-label">Variance:</span>
                                    <span class="metric-value negative">+$20K (5% over)</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-label">Risk Level:</span>
                                    <span class="metric-value warning">Medium</span>
                                </div>
                            </div>

                            <div class="milestone-approvals">
                                <h5>Required Approvals:</h5>
                                <div class="approval-list">
                                    <div class="approval-item not-started">
                                        <span>⭕ John Smith (CEO)</span>
                                        <small>Approval required</small>
                                    </div>
                                    <div class="approval-item not-started">
                                        <span>⭕ Sarah Johnson (CTO)</span>
                                        <small>Technical sign-off required</small>
                                    </div>
                                    <div class="approval-item not-started">
                                        <span>⭕ Finance Team</span>
                                        <small>Budget approval required</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">📊 Budget Summary & Alerts</h2>
                    </div>

                    <div class="budget-summary">
                        <div class="summary-metrics">
                            <div class="summary-card">
                                <div class="summary-icon">💰</div>
                                <div class="summary-content">
                                    <div class="summary-label">Total Budget</div>
                                    <div class="summary-value">$700K</div>
                                    <div class="summary-detail">Across all milestones</div>
                                </div>
                            </div>

                            <div class="summary-card">
                                <div class="summary-icon">💸</div>
                                <div class="summary-content">
                                    <div class="summary-label">Actual Spent</div>
                                    <div class="summary-value">$275K</div>
                                    <div class="summary-detail">39% of total budget</div>
                                </div>
                            </div>

                            <div class="summary-card">
                                <div class="summary-icon">📈</div>
                                <div class="summary-content">
                                    <div class="summary-label">Projected Total</div>
                                    <div class="summary-value">$695K</div>
                                    <div class="summary-detail">1% under budget</div>
                                </div>
                            </div>

                            <div class="summary-card">
                                <div class="summary-icon">⚠️</div>
                                <div class="summary-content">
                                    <div class="summary-label">Budget Variance</div>
                                    <div class="summary-value positive">-$5K</div>
                                    <div class="summary-detail">0.7% under budget</div>
                                </div>
                            </div>
                        </div>

                        <div class="budget-alerts">
                            <h4>🚨 Budget Alerts & Recommendations</h4>
                            <div class="alert-list">
                                <div class="alert-item alert-warning">
                                    <div class="alert-icon">⚠️</div>
                                    <div class="alert-content">
                                        <strong>Milestone 3 Budget Risk</strong>
                                        <p>Projected 5% over budget for production deployment. Consider cost optimization measures.</p>
                                        <small>Risk Level: Medium | Action Required: Yes</small>
                                    </div>
                                </div>

                                <div class="alert-item alert-success">
                                    <div class="alert-icon">✅</div>
                                    <div class="alert-content">
                                        <strong>Milestone 1 & 2 Performance</strong>
                                        <p>Both milestones completed under budget with excellent cost control.</p>
                                        <small>Savings: $25K | Performance: Excellent</small>
                                    </div>
                                </div>

                                <div class="alert-item alert-info">
                                    <div class="alert-icon">ℹ️</div>
                                    <div class="alert-content">
                                        <strong>Approval Pending</strong>
                                        <p>Mike Chen's approval required for Milestone 2 completion.</p>
                                        <small>Due Date: Mar 10, 2024 | Priority: High</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <footer class="app-footer">
            <div class="footer-actions">
                <button class="btn btn-secondary" onclick="saveDraft()">💾 Save Draft</button>
                <button class="btn btn-primary" onclick="calculateResults()">🔄 Calculate Results</button>
                <button class="btn btn-success" onclick="exportReport()">📊 Export Report</button>
            </div>
        </footer>
    </div>
</body>
</html>
