@echo off
echo 🚀 Master BC Framework - Quick Start
echo ===================================
echo.

echo 📋 Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed!
    echo.
    echo 💡 Please install Node.js from: https://nodejs.org/
    echo    Download the LTS version and run the installer
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js is installed
echo.

echo 📦 Installing dependencies...
echo This may take a few minutes on first run...
call npm install --silent

if %errorlevel% neq 0 (
    echo ❌ Installation failed, trying alternative method...
    call npm install --legacy-peer-deps --silent
    
    if %errorlevel% neq 0 (
        echo ❌ Installation still failed, trying with force...
        call npm install --force --silent
        
        if %errorlevel% neq 0 (
            echo ❌ Could not install dependencies
            echo.
            echo 💡 Try these manual steps:
            echo 1. Delete node_modules folder
            echo 2. Run: npm cache clean --force
            echo 3. Run: npm install
            echo.
            pause
            exit /b 1
        )
    )
)

echo ✅ Dependencies installed successfully!
echo.

echo 🚀 Starting development server...
echo.
echo 📊 Your Master BC Framework will open at: http://localhost:3000
echo.
echo 💡 Press Ctrl+C to stop the server when done
echo.

start /b npm run dev

timeout /t 3 /nobreak > nul

echo.
echo 🎯 Master BC Framework Features:
echo ✅ Business Case Framework (6-step process)
echo ✅ Advanced Financial Modeling
echo ✅ Excel Analysis & Augment AI
echo ✅ PMI/PGMP Compliance Tracking
echo ✅ Multi-Stakeholder Management
echo ✅ Change Management & Roadmap
echo.
echo 🌐 Opening browser...

timeout /t 2 /nobreak > nul
start http://localhost:3000

echo.
echo 🎉 Application is now running!
echo.
pause
