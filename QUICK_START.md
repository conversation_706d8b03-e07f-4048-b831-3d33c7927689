# 🚀 Master BC Framework - Governance Platform
## Quick Start Guide

### 📋 Overview
The Master BC Framework is a comprehensive governance platform that provides:
- **Master Business Case Management** with full CRUD operations
- **Enhanced PMI Guidelines & PGMP Framework** compliance tracking
- **Multi-Stakeholder Feedback System** with advanced commenting
- **Change Management & Roadmap** planning
- **Financial Modeling & Analysis** tools

### 🛠️ Prerequisites
- **Node.js** (v16 or higher)
- **npm** (v8 or higher)
- **MongoDB** (local or Atlas)

### ⚡ Quick Start

#### Option 1: Automated Setup (Recommended)
```bash
# Windows
start-full-stack.bat

# Linux/Mac
chmod +x start-full-stack.sh
./start-full-stack.sh
```

#### Option 2: Manual Setup
```bash
# 1. Install frontend dependencies
npm install

# 2. Install backend dependencies
cd server
npm install
cd ..

# 3. Start backend server
cd server
npm run dev &

# 4. Start frontend application
npm run dev
```

### 🌐 Access Points
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000
- **API Health**: http://localhost:5000/api/health

### 🎯 Key Features

#### 👥 Multi-Stakeholder Feedback System
- **Full CRUD Operations**: Create, Read, Update, Delete stakeholders
- **Multiple Views**: Table, Details, Analytics, Comments
- **Advanced Filtering**: By status, priority, engagement level
- **Comment Management**: 8 different comment types with full tracking
- **Real-time Updates**: Instant synchronization across views

#### 🏛️ PMI Guidelines & PGMP Framework
- **Project Lifecycle Tracking**: All 5 project phases with detailed processes
- **Knowledge Areas**: Complete coverage of 10 PMI knowledge areas
- **Compliance Scoring**: Real-time compliance percentage tracking
- **Artifacts Management**: Document status and version control
- **Assessment & Actions**: Critical issues and recommendations

#### 🗺️ Change Management & Roadmap
- **Current State Analysis**: BC1→EPIC mapping
- **Proposed Models**: Medium and big change scenarios
- **Implementation Timeline**: Phased approach with milestones
- **Governance Evolution**: From current to proposed states

#### 📊 Financial Modeling
- **Parameters Setup**: Comprehensive financial parameters
- **Cost & Sales Definition**: Detailed cost structure modeling
- **Sensitivity Analysis**: What-if scenario planning
- **Financial Indexes**: IRR, NPV, Payback calculations
- **Visualizations**: Charts and analytics

### 📁 Project Structure
```
master-bc-framework/
├── src/
│   ├── components/
│   │   ├── EnhancedStakeholderFeedback.jsx    # Multi-stakeholder CRUD system
│   │   ├── EnhancedPMICompliance.jsx          # PMI/PGMP framework
│   │   ├── ChangeManagementRoadmap.jsx        # Governance roadmap
│   │   ├── MasterBusinessCase.jsx             # Core business case
│   │   └── ...
│   ├── services/
│   │   ├── api.js                             # API integration
│   │   └── mockDataService.js                 # Mock data for development
│   └── utils/
│       ├── financialCalculations.js           # Financial modeling
│       └── dataExport.js                      # Export functionality
├── server/
│   ├── models/                                # MongoDB models
│   ├── routes/                                # API routes
│   └── server.js                              # Express server
└── demo.html                                  # Static demo version
```

### 🔧 Configuration

#### Environment Variables
Copy `.env.example` to `.env` and configure:
```bash
# Application
REACT_APP_APP_NAME=Master BC Framework - Governance Platform
REACT_APP_API_BASE_URL=http://localhost:5000/api

# Database
MONGODB_URI=mongodb://localhost:27017/master-bc-framework

# Server
PORT=5000
NODE_ENV=development
```

#### MongoDB Setup
1. **Local MongoDB**: Install and start MongoDB service
2. **MongoDB Atlas**: Update connection string in `.env`

### 🚀 Production Deployment

#### Build for Production
```bash
# Windows
build-production.bat

# Linux/Mac
chmod +x build-production.sh
./build-production.sh
```

#### Deploy
1. Copy `./dist/` to web server
2. Copy `./server/` to application server
3. Configure environment variables
4. Start backend: `cd server && npm start`

### 📊 Usage Examples

#### Adding a New Stakeholder
1. Navigate to **Stakeholder Feedback** tab
2. Click **➕ Add Stakeholder**
3. Fill in stakeholder details
4. Set engagement level and priority
5. Save and start adding comments

#### Tracking PMI Compliance
1. Go to **PMI/PGMP Compliance** tab
2. Review **Compliance Overview** metrics
3. Check **Project Lifecycle** progress
4. Examine **Knowledge Areas** details
5. Review **Assessment & Actions**

#### Managing Change Roadmap
1. Access **Change Management & Roadmap** tab
2. Review **Current State** analysis
3. Explore **Proposed** governance models
4. Check **Implementation Recommendations**

### 🆘 Troubleshooting

#### Common Issues
- **Port conflicts**: Change ports in configuration
- **MongoDB connection**: Verify MongoDB is running
- **Dependencies**: Run `npm install` in both root and server directories
- **Build errors**: Check Node.js version compatibility

#### Support
- Check `demo.html` for feature preview
- Review component documentation in source files
- Verify all dependencies are installed correctly

### 🎯 Next Steps
1. **Customize**: Modify components for your specific needs
2. **Integrate**: Connect with your existing systems
3. **Extend**: Add new features and functionality
4. **Deploy**: Move to production environment

---

**Master BC Framework - Governance Platform**  
*Professional Business Case Management with PMI/PGMP Compliance*
