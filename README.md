# 🎯 Master BC Framework - Governance Platform

A comprehensive **Master Business Case Governance Platform** with enhanced PMI Guidelines, PGMP Framework compliance, and multi-stakeholder feedback management system with full CRUD operations.

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)

**Windows:**
```bash
start-full-stack.bat
```

**Linux/Mac:**
```bash
./start-full-stack.sh
```

### Option 2: Manual Setup

1. **Install Dependencies:**
   ```bash
   npm install
   cd server && npm install && cd ..
   ```

2. **Start Backend:**
   ```bash
   cd server && npm run dev
   ```

3. **Start Frontend (new terminal):**
   ```bash
   npm run dev
   ```

## 📊 Features

### ✅ **Complete CRUD Operations**
- **Create** new Master Business Cases with full validation
- **Read** all business cases with advanced filtering and pagination
- **Update** existing business cases with real-time sync
- **Delete** business cases with confirmation dialogs

### ✅ **Advanced Data Management**
- **Search** across program names and content
- **Filter** by status, business driver, investment category
- **Sort** by any field (name, date, budget, etc.)
- **Pagination** for handling large datasets
- **Export** data in JSON and CSV formats

### ✅ **Business Case Features**
- **Program Information** management
- **Use Cases** portfolio tracking
- **Milestones** with budget vs actual cost monitoring
- **Stakeholder** approval workflows
- **Financial Metrics** calculation (NPV, IRR, ROI)
- **Status Management** with workflow transitions

### ✅ **MongoDB Integration**
- **Mongoose ODM** for data modeling
- **Validation** at database level
- **Indexing** for performance optimization
- **Aggregation** for statistics and reporting

## 🏗️ Architecture

### **Frontend (React + Vite)**
- Modern React 18 with hooks
- Responsive design with CSS Grid/Flexbox
- Component-based architecture
- Real-time state management
- Professional UI/UX design

### **Backend (Node.js + Express)**
- RESTful API design
- MongoDB integration with Mongoose
- Input validation with express-validator
- Error handling and logging
- Security middleware (CORS, Helmet, Rate Limiting)

### **Database (MongoDB)**
- Document-based storage
- Flexible schema design
- Indexing for performance
- Aggregation pipelines for analytics

## 📋 API Endpoints

### Master Business Cases
```
GET    /api/master-business-cases           # Get all with pagination/filtering
GET    /api/master-business-cases/:id       # Get single business case
POST   /api/master-business-cases           # Create new business case
PUT    /api/master-business-cases/:id       # Update business case
DELETE /api/master-business-cases/:id       # Delete business case
PATCH  /api/master-business-cases/:id/status # Update status only
GET    /api/master-business-cases/stats/summary # Get statistics
```

### Query Parameters
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 10)
- `status` - Filter by status
- `businessDriver` - Filter by business driver
- `investmentCategory` - Filter by category
- `search` - Search in program names
- `sortBy` - Sort field
- `sortOrder` - Sort direction (asc/desc)

## 🗄️ Data Model

### Master Business Case Schema
```javascript
{
  programInfo: {
    programName: String,        // Required
    programManager: String,     // Required
    sponsor: String,           // Required
    strategicAlignment: String, // High/Medium/Low
    businessDriver: String,    // Market Expansion, Cost Reduction, etc.
    investmentCategory: String, // Strategic/Operational/Mandatory/Infrastructure
    totalBudget: Number,       // Required, min: 0
    duration: Number,          // Required, min: 1
    startDate: Date,
    endDate: Date,
    currency: String           // USD/EUR/GBP/JPY/CAD/AUD
  },
  governance: {
    steeringCommittee: Array,
    decisionGates: Array,
    riskTolerance: String,     // Low/Medium/High
    complianceFramework: Array // PMI/PGMP/ISO 21500/PRINCE2/Agile/SAFe
  },
  useCases: Array,
  milestones: Array,
  financialMetrics: Object,
  status: String,              // Draft/Under Review/Approved/Active/On Hold/Completed/Cancelled
  createdBy: String,           // Required
  lastModifiedBy: String,
  tags: Array,
  attachments: Array,
  createdAt: Date,
  updatedAt: Date
}
```

## 🎯 User Interface

### **Business Case Manager Tab**
- Grid view of all business cases
- Search and filter controls
- Create/Edit/Delete actions
- Status management dropdown
- Duplicate functionality
- Pagination controls

### **Individual Business Case Tabs**
- Master Business Case overview
- PMI/PGMP Compliance tracking
- Stakeholder Feedback management
- Financial Parameters setup
- Cost & Sales Definition
- Sensitivity Analysis
- Financial Indexes and reporting
- Change Management & Roadmap
- Budget vs Cost Tracking

## 🔧 Development

### **Project Structure**
```
mybc/
├── src/                    # Frontend React application
│   ├── components/         # React components
│   ├── services/          # API service layer
│   ├── utils/             # Utility functions
│   └── App.jsx            # Main application
├── server/                # Backend Node.js application
│   ├── models/            # MongoDB models
│   ├── routes/            # API routes
│   └── server.js          # Express server
├── demo.html              # Static demo
└── setup.md               # Detailed setup guide
```

### **Available Scripts**

**Frontend:**
```bash
npm run dev        # Start development server
npm run build      # Build for production
npm run preview    # Preview production build
```

**Backend:**
```bash
npm run dev        # Start with nodemon (auto-restart)
npm start          # Start production server
npm test           # Run tests
```

## 🔒 Security Features

- **Input Validation** on both frontend and backend
- **Rate Limiting** to prevent API abuse
- **CORS Configuration** for cross-origin requests
- **Helmet** for security headers
- **Data Sanitization** to prevent injection attacks
- **Error Handling** without exposing sensitive information

## 📈 Performance Optimizations

- **Database Indexing** for faster queries
- **Pagination** to handle large datasets
- **Lazy Loading** for React components
- **Caching** with appropriate headers
- **Compression** for API responses

## 🌐 Access Points

- **Frontend Application**: http://localhost:3000
- **Backend API**: http://localhost:5000
- **API Health Check**: http://localhost:5000/api/health
- **Static Demo**: Open `demo.html` in browser

## 📊 Sample Data

The application includes sample Master Business Cases for testing:
- Digital Transformation Initiative
- Customer Portal Enhancement
- Data Analytics Platform
- Mobile Application Development

## 🚀 Production Deployment

### **Frontend (Netlify/Vercel)**
1. Build: `npm run build`
2. Deploy `dist` folder
3. Set environment variables

### **Backend (Heroku/Railway)**
1. Set up MongoDB Atlas
2. Configure environment variables
3. Deploy server folder

## 🐛 Troubleshooting

**Common Issues:**
1. **MongoDB Connection**: Ensure MongoDB is running or Atlas connection string is correct
2. **CORS Errors**: Check FRONTEND_URL in server/.env
3. **Port Conflicts**: Ensure ports 3000 and 5000 are available
4. **Build Errors**: Clear node_modules and reinstall dependencies

## 📞 Support

For detailed setup instructions, see `setup.md`

## 🎉 Success Metrics

✅ **Full CRUD Operations** with MongoDB
✅ **Real-time Data Synchronization**
✅ **Professional Enterprise UI**
✅ **PMI/PGMP Compliance Features**
✅ **Advanced Search & Filtering**
✅ **Export Capabilities**
✅ **Responsive Design**
✅ **Production-Ready Architecture**

**Ready for enterprise deployment!** 🚀
