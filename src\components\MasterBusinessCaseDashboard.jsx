import React, { useState, useEffect } from 'react'

const MasterBusinessCaseDashboard = () => {
  const [businessCases, setBusinessCases] = useState([
    {
      id: 1,
      programName: 'Digital Transformation Initiative',
      businessDriver: 'Market Expansion',
      investmentCategory: 'Technology',
      status: 'Active',
      totalBudget: 2500000,
      approvedBudget: 2000000,
      spentBudget: 750000,
      startDate: '2024-01-15',
      endDate: '2024-12-31',
      sponsor: '<PERSON>',
      manager: '<PERSON>',
      description: 'Comprehensive digital transformation to modernize business processes and improve customer experience.',
      // Auto-calculated fields
      remainingBudget: 0,
      budgetUtilization: 0,
      projectedROI: 0,
      riskScore: 0,
      benefitValue: 0,
      // Cost breakdown
      costs: {
        personnel: 1200000,
        technology: 800000,
        training: 300000,
        consulting: 200000
      },
      // Sales projections
      sales: {
        year1: 500000,
        year2: 800000,
        year3: 1200000,
        totalProjected: 0
      },
      useCases: [
        { id: 1, name: 'Customer Portal Enhancement', priority: 'High', status: 'In Progress', budget: 800000, completion: 65 },
        { id: 2, name: 'Process Automation', priority: 'Medium', status: 'Planning', budget: 600000, completion: 15 }
      ],
      risks: [
        { id: 1, description: 'Technology integration challenges', impact: 'High', probability: 'Medium', score: 7 },
        { id: 2, description: 'Resource availability', impact: 'Medium', probability: 'Low', score: 4 }
      ],
      benefits: [
        { id: 1, description: 'Improved customer satisfaction', value: 500000, timeline: '6 months', probability: 85 },
        { id: 2, description: 'Operational efficiency gains', value: 300000, timeline: '12 months', probability: 90 }
      ]
    },
    {
      id: 2,
      programName: 'Supply Chain Optimization',
      businessDriver: 'Cost Reduction',
      investmentCategory: 'Operations',
      status: 'Planning',
      totalBudget: 1800000,
      approvedBudget: 1500000,
      spentBudget: 0,
      startDate: '2024-03-01',
      endDate: '2024-11-30',
      sponsor: 'Mike Chen',
      manager: 'Lisa Rodriguez',
      description: 'Optimize supply chain processes to reduce costs and improve delivery times.',
      remainingBudget: 0,
      budgetUtilization: 0,
      projectedROI: 0,
      riskScore: 0,
      benefitValue: 0,
      costs: {
        personnel: 600000,
        technology: 700000,
        training: 200000,
        consulting: 300000
      },
      sales: {
        year1: 200000,
        year2: 400000,
        year3: 600000,
        totalProjected: 0
      },
      useCases: [
        { id: 1, name: 'Inventory Management System', priority: 'High', status: 'Planning', budget: 900000, completion: 0 },
        { id: 2, name: 'Supplier Integration Platform', priority: 'High', status: 'Planning', budget: 600000, completion: 0 }
      ],
      risks: [
        { id: 1, description: 'Supplier resistance to change', impact: 'Medium', probability: 'Medium', score: 5 }
      ],
      benefits: [
        { id: 1, description: 'Reduced inventory costs', value: 400000, timeline: '9 months', probability: 80 },
        { id: 2, description: 'Faster delivery times', value: 200000, timeline: '6 months', probability: 75 }
      ]
    },
    {
      id: 3,
      programName: 'AI-Powered Analytics Platform',
      businessDriver: 'Innovation',
      investmentCategory: 'Technology',
      status: 'Approved',
      totalBudget: 3200000,
      approvedBudget: 3200000,
      spentBudget: 320000,
      startDate: '2024-02-01',
      endDate: '2025-01-31',
      sponsor: 'David Park',
      manager: 'Emily Watson',
      description: 'Develop AI-powered analytics platform to enhance decision-making capabilities.',
      remainingBudget: 0,
      budgetUtilization: 0,
      projectedROI: 0,
      riskScore: 0,
      benefitValue: 0,
      costs: {
        personnel: 1600000,
        technology: 1200000,
        training: 200000,
        consulting: 200000
      },
      sales: {
        year1: 300000,
        year2: 800000,
        year3: 1500000,
        totalProjected: 0
      },
      useCases: [
        { id: 1, name: 'Predictive Analytics Engine', priority: 'High', status: 'In Progress', budget: 1500000, completion: 25 },
        { id: 2, name: 'Real-time Dashboard', priority: 'Medium', status: 'Planning', budget: 800000, completion: 5 }
      ],
      risks: [
        { id: 1, description: 'Data quality issues', impact: 'High', probability: 'Medium', score: 8 },
        { id: 2, description: 'AI model accuracy concerns', impact: 'High', probability: 'Low', score: 6 }
      ],
      benefits: [
        { id: 1, description: 'Improved decision accuracy', value: 600000, timeline: '12 months', probability: 85 },
        { id: 2, description: 'Reduced analysis time', value: 400000, timeline: '8 months', probability: 90 }
      ]
    }
  ])

  const [selectedCase, setSelectedCase] = useState(null)
  const [isEditing, setIsEditing] = useState(false)
  const [isCreating, setIsCreating] = useState(false)
  const [activeView, setActiveView] = useState('dashboard')
  const [editForm, setEditForm] = useState({})

  // Auto-calculation functions
  const calculateMetrics = (businessCase) => {
    const remainingBudget = businessCase.approvedBudget - businessCase.spentBudget
    const budgetUtilization = (businessCase.spentBudget / businessCase.approvedBudget) * 100

    // Calculate total projected sales
    const totalProjected = businessCase.sales.year1 + businessCase.sales.year2 + businessCase.sales.year3

    // Calculate total benefit value
    const benefitValue = businessCase.benefits.reduce((total, benefit) => {
      return total + (benefit.value * (benefit.probability / 100))
    }, 0)

    // Calculate ROI
    const projectedROI = ((benefitValue - businessCase.totalBudget) / businessCase.totalBudget) * 100

    // Calculate risk score
    const riskScore = businessCase.risks.reduce((total, risk) => total + risk.score, 0) / businessCase.risks.length

    return {
      ...businessCase,
      remainingBudget,
      budgetUtilization: Math.round(budgetUtilization * 10) / 10,
      projectedROI: Math.round(projectedROI * 10) / 10,
      riskScore: Math.round(riskScore * 10) / 10,
      benefitValue: Math.round(benefitValue),
      sales: {
        ...businessCase.sales,
        totalProjected
      }
    }
  }

  // Auto-calculate metrics for all business cases
  useEffect(() => {
    const updatedCases = businessCases.map(calculateMetrics)
    setBusinessCases(updatedCases)
  }, [])

  // CRUD Operations
  const handleCreate = () => {
    setEditForm({
      programName: '',
      businessDriver: 'Market Expansion',
      investmentCategory: 'Technology',
      status: 'Planning',
      totalBudget: 0,
      approvedBudget: 0,
      spentBudget: 0,
      startDate: '',
      endDate: '',
      sponsor: '',
      manager: '',
      description: '',
      costs: { personnel: 0, technology: 0, training: 0, consulting: 0 },
      sales: { year1: 0, year2: 0, year3: 0 },
      useCases: [],
      risks: [],
      benefits: []
    })
    setIsCreating(true)
    setIsEditing(true)
  }

  const handleEdit = (businessCase) => {
    setEditForm({ ...businessCase })
    setSelectedCase(businessCase)
    setIsEditing(true)
    setIsCreating(false)
  }

  const handleSave = () => {
    const calculatedCase = calculateMetrics(editForm)

    if (isCreating) {
      const newId = Math.max(...businessCases.map(bc => bc.id)) + 1
      const newCase = { ...calculatedCase, id: newId }
      setBusinessCases([...businessCases, newCase])
    } else {
      setBusinessCases(businessCases.map(bc =>
        bc.id === editForm.id ? calculatedCase : bc
      ))
    }

    setIsEditing(false)
    setIsCreating(false)
    setEditForm({})
  }

  const handleDelete = (id) => {
    if (window.confirm('Are you sure you want to delete this business case?')) {
      setBusinessCases(businessCases.filter(bc => bc.id !== id))
      if (selectedCase && selectedCase.id === id) {
        setSelectedCase(null)
      }
    }
  }

  const handleUpdate = (field, value) => {
    const updatedForm = { ...editForm, [field]: value }
    setEditForm(updatedForm)
  }

  const views = [
    { id: 'dashboard', label: 'Dashboard Overview', icon: '📊' },
    { id: 'table', label: 'Business Cases Table', icon: '📋' },
    { id: 'analytics', label: 'Analytics & Metrics', icon: '📈' },
    { id: 'augment', label: 'Augment AI Assistant', icon: '🤖' }
  ]

  const getStatusColor = (status) => {
    const colors = {
      'Active': '#28a745',
      'Planning': '#17a2b8',
      'Approved': '#ffc107',
      'Completed': '#6f42c1',
      'On Hold': '#fd7e14',
      'Cancelled': '#dc3545'
    }
    return colors[status] || '#6c757d'
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const renderDashboard = () => (
    <div className="dashboard-overview">
      <div className="dashboard-header">
        <h2>📊 Master Business Case Dashboard</h2>
        <button className="btn btn-primary" onClick={handleCreate}>
          ➕ Create New Business Case
        </button>
      </div>

      <div className="dashboard-metrics">
        <div className="metric-card">
          <div className="metric-icon">💼</div>
          <div className="metric-content">
            <div className="metric-value">{businessCases.length}</div>
            <div className="metric-label">Total Business Cases</div>
          </div>
        </div>
        <div className="metric-card">
          <div className="metric-icon">💰</div>
          <div className="metric-content">
            <div className="metric-value">
              {formatCurrency(businessCases.reduce((sum, bc) => sum + bc.totalBudget, 0))}
            </div>
            <div className="metric-label">Total Investment</div>
          </div>
        </div>
        <div className="metric-card">
          <div className="metric-icon">📈</div>
          <div className="metric-content">
            <div className="metric-value">
              {Math.round(businessCases.reduce((sum, bc) => sum + bc.projectedROI, 0) / businessCases.length)}%
            </div>
            <div className="metric-label">Average ROI</div>
          </div>
        </div>
        <div className="metric-card">
          <div className="metric-icon">⚡</div>
          <div className="metric-content">
            <div className="metric-value">
              {businessCases.filter(bc => bc.status === 'Active').length}
            </div>
            <div className="metric-label">Active Projects</div>
          </div>
        </div>
      </div>

      <div className="business-cases-grid">
        {businessCases.map((businessCase) => (
          <div key={businessCase.id} className="business-case-card">
            <div className="card-header">
              <h3>{businessCase.programName}</h3>
              <span
                className="status-badge"
                style={{ backgroundColor: getStatusColor(businessCase.status) }}
              >
                {businessCase.status}
              </span>
            </div>

            <div className="card-content">
              <div className="info-row">
                <span className="label">Manager:</span>
                <span>{businessCase.manager}</span>
              </div>
              <div className="info-row">
                <span className="label">Budget:</span>
                <span>{formatCurrency(businessCase.totalBudget)}</span>
              </div>
              <div className="info-row">
                <span className="label">Spent:</span>
                <span>{formatCurrency(businessCase.spentBudget)}</span>
              </div>
              <div className="info-row">
                <span className="label">Remaining:</span>
                <span>{formatCurrency(businessCase.remainingBudget)}</span>
              </div>
              <div className="info-row">
                <span className="label">ROI:</span>
                <span className={businessCase.projectedROI > 0 ? 'positive' : 'negative'}>
                  {businessCase.projectedROI}%
                </span>
              </div>
              <div className="info-row">
                <span className="label">Risk Score:</span>
                <span className={businessCase.riskScore > 6 ? 'high-risk' : businessCase.riskScore > 4 ? 'medium-risk' : 'low-risk'}>
                  {businessCase.riskScore}/10
                </span>
              </div>
            </div>

            <div className="card-actions">
              <button
                className="btn btn-sm btn-info"
                onClick={() => {
                  setSelectedCase(businessCase)
                  setActiveView('details')
                }}
              >
                👁️ View
              </button>
              <button
                className="btn btn-sm btn-warning"
                onClick={() => handleEdit(businessCase)}
              >
                ✏️ Edit
              </button>
              <button
                className="btn btn-sm btn-danger"
                onClick={() => handleDelete(businessCase.id)}
              >
                🗑️ Delete
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  )

  return (
    <div className="master-business-case-dashboard">
      <div className="dashboard-navigation">
        {views.map((view) => (
          <button
            key={view.id}
            className={`nav-btn ${activeView === view.id ? 'active' : ''}`}
            onClick={() => setActiveView(view.id)}
          >
            <span className="nav-icon">{view.icon}</span>
            <span className="nav-label">{view.label}</span>
          </button>
        ))}
      </div>

      <div className="dashboard-content">
        {activeView === 'dashboard' && renderDashboard()}
        {activeView === 'table' && renderTable()}
        {activeView === 'analytics' && renderAnalytics()}
        {activeView === 'augment' && renderAugmentPromotion()}
      </div>
    </div>
  )
}

export default MasterBusinessCaseDashboard
