import React, { useState, useEffect } from 'react'

const BusinessCaseFramework = ({ data, onChange }) => {
  const [frameworkData, setFrameworkData] = useState({
    currentStep: 1,
    completedSteps: [],

    // Step 1: Project Identification
    projectIdentification: {
      businessProblem: '',
      opportunityDescription: '',
      strategicAlignment: '',
      stakeholders: [],
      businessDrivers: [],
      urgency: 'Medium',
      scope: '',
      constraints: [],
      assumptions: []
    },

    // Step 2: Determine Alternatives
    alternatives: [
      {
        id: 1,
        name: 'Do Nothing (Status Quo)',
        description: 'Continue with current processes and systems',
        pros: ['No immediate investment required', 'No disruption to current operations'],
        cons: ['Missed opportunities', 'Continued inefficiencies'],
        estimatedCost: 0,
        timeframe: 'N/A',
        riskLevel: 'Low'
      },
      {
        id: 2,
        name: 'Minimal Investment',
        description: 'Small improvements to existing processes',
        pros: ['Low cost', 'Quick implementation'],
        cons: ['Limited impact', 'May not address root causes'],
        estimatedCost: 50000,
        timeframe: '3-6 months',
        riskLevel: 'Low'
      },
      {
        id: 3,
        name: 'Recommended Solution',
        description: 'Comprehensive solution addressing all identified issues',
        pros: ['Maximum business value', 'Long-term solution'],
        cons: ['Higher investment', 'Longer implementation time'],
        estimatedCost: 500000,
        timeframe: '12-18 months',
        riskLevel: 'Medium'
      }
    ],

    // Step 3: Benefit Analysis
    benefitAnalysis: {
      quantifiableBenefits: [
        { category: 'Cost Savings', description: 'Operational efficiency gains', amount: 200000, timeline: '12 months' },
        { category: 'Revenue Generation', description: 'New market opportunities', amount: 300000, timeline: '18 months' },
        { category: 'Productivity', description: 'Process automation', amount: 150000, timeline: '6 months' }
      ],
      intangibleBenefits: [
        { category: 'Customer Satisfaction', description: 'Improved service quality', impact: 'High' },
        { category: 'Employee Morale', description: 'Better tools and processes', impact: 'Medium' },
        { category: 'Brand Reputation', description: 'Market leadership position', impact: 'High' }
      ],
      benefitRealization: {
        year1: 150000,
        year2: 400000,
        year3: 650000,
        totalBenefits: 1200000
      }
    },

    // Step 4: Risk Assessment
    riskAssessment: {
      risks: [
        {
          id: 1,
          category: 'Technical',
          description: 'Integration challenges with legacy systems',
          probability: 'Medium',
          impact: 'High',
          riskScore: 6,
          mitigation: 'Comprehensive testing and phased implementation',
          owner: 'Technical Lead'
        },
        {
          id: 2,
          category: 'Financial',
          description: 'Budget overruns due to scope creep',
          probability: 'Medium',
          impact: 'Medium',
          riskScore: 4,
          mitigation: 'Strict change control process',
          owner: 'Project Manager'
        },
        {
          id: 3,
          category: 'Operational',
          description: 'User adoption challenges',
          probability: 'Low',
          impact: 'Medium',
          riskScore: 3,
          mitigation: 'Comprehensive training and change management',
          owner: 'Change Manager'
        }
      ],
      overallRiskRating: 'Medium',
      contingencyPlan: 'Phased implementation with rollback capabilities'
    },

    // Step 5: Create Business Case
    businessCase: {
      executiveSummary: '',
      problemStatement: '',
      proposedSolution: '',
      financialAnalysis: {
        totalInvestment: 500000,
        expectedBenefits: 1200000,
        netPresentValue: 450000,
        internalRateOfReturn: 25.5,
        paybackPeriod: 2.1,
        returnOnInvestment: 140
      },
      implementationPlan: {
        phases: [
          { name: 'Planning & Design', duration: '3 months', cost: 100000 },
          { name: 'Development & Testing', duration: '6 months', cost: 250000 },
          { name: 'Deployment & Training', duration: '3 months', cost: 100000 },
          { name: 'Support & Optimization', duration: '6 months', cost: 50000 }
        ],
        totalDuration: '18 months',
        totalCost: 500000
      },
      successCriteria: [],
      assumptions: [],
      dependencies: []
    },

    // Step 6: Present Business Case
    presentation: {
      audienceType: 'Executive Committee',
      presentationDate: '',
      keyMessages: [],
      supportingDocuments: [],
      approvalStatus: 'Pending',
      feedback: [],
      nextSteps: []
    },

    // Master Business Case Integration
    masterBusinessCase: {
      programName: '',
      portfolioAlignment: '',
      strategicObjectives: [],
      governanceFramework: 'PMI',
      complianceRequirements: [],
      stakeholderMatrix: [],
      communicationPlan: [],
      changeManagement: {
        currentState: '',
        futureState: '',
        transitionPlan: '',
        riskMitigation: ''
      }
    },

    ...data
  })

  const [activeView, setActiveView] = useState('overview')

  // Business Case Development Steps
  const steps = [
    {
      id: 1,
      title: 'Project Identification',
      icon: '🔍',
      description: 'Identify business problems, opportunities, and strategic alignment',
      status: 'completed'
    },
    {
      id: 2,
      title: 'Determine Alternatives',
      icon: '🔄',
      description: 'Evaluate different approaches and solution options',
      status: 'completed'
    },
    {
      id: 3,
      title: 'Benefit Analysis',
      icon: '📈',
      description: 'Quantify and qualify expected business benefits',
      status: 'completed'
    },
    {
      id: 4,
      title: 'Risk Assessment',
      icon: '⚠️',
      description: 'Identify, assess, and plan mitigation for project risks',
      status: 'completed'
    },
    {
      id: 5,
      title: 'Create Business Case',
      icon: '📝',
      description: 'Compile comprehensive business case document',
      status: 'in-progress'
    },
    {
      id: 6,
      title: 'Present Business Case',
      icon: '👨‍💼',
      description: 'Present to stakeholders and obtain approval',
      status: 'pending'
    }
  ]

  const views = [
    { id: 'overview', label: 'Framework Overview', icon: '📋' },
    { id: 'process', label: '6-Step Process', icon: '🔄' },
    { id: 'alternatives', label: 'Alternatives Analysis', icon: '⚖️' },
    { id: 'benefits', label: 'Benefit Analysis', icon: '📈' },
    { id: 'risks', label: 'Risk Assessment', icon: '⚠️' },
    { id: 'financial', label: 'Financial Analysis', icon: '💰' },
    { id: 'integration', label: 'System Integration', icon: '🔗' },
    { id: 'master', label: 'Master Business Case', icon: '🎯' }
  ]

  const handleStepComplete = (stepId) => {
    setFrameworkData(prev => ({
      ...prev,
      completedSteps: [...prev.completedSteps, stepId],
      currentStep: stepId + 1
    }))
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const getStepStatus = (stepId) => {
    if (frameworkData.completedSteps.includes(stepId)) return 'completed'
    if (frameworkData.currentStep === stepId) return 'current'
    return 'pending'
  }

  const renderOverview = () => (
    <div className="framework-overview">
      <div className="overview-header">
        <h2>📋 Business Case Development Framework</h2>
        <p className="overview-description">
          A disciplined approach to examine opportunities, alternatives, project stages,
          and investment needed to formally recommend the best course of action that will create business value.
        </p>
      </div>

      <div className="framework-benefits">
        <h3>🎯 Framework Benefits</h3>
        <div className="benefits-grid">
          <div className="benefit-card">
            <div className="benefit-icon">🎯</div>
            <h4>Strategic Alignment</h4>
            <p>Ensures projects align with business strategy and objectives</p>
          </div>
          <div className="benefit-card">
            <div className="benefit-icon">💰</div>
            <h4>Financial Rigor</h4>
            <p>Comprehensive financial analysis with ROI, NPV, and payback calculations</p>
          </div>
          <div className="benefit-card">
            <div className="benefit-icon">⚖️</div>
            <h4>Objective Evaluation</h4>
            <p>Systematic comparison of alternatives and options</p>
          </div>
          <div className="benefit-card">
            <div className="benefit-icon">🛡️</div>
            <h4>Risk Management</h4>
            <p>Proactive identification and mitigation of project risks</p>
          </div>
        </div>
      </div>

      <div className="process-flow">
        <h3>🔄 The 6-Step Process</h3>
        <div className="steps-container">
          {steps.map((step, index) => (
            <div key={step.id} className={`step-card ${getStepStatus(step.id)}`}>
              <div className="step-number">{step.id}</div>
              <div className="step-icon">{step.icon}</div>
              <div className="step-content">
                <h4>{step.title}</h4>
                <p>{step.description}</p>
              </div>
              {index < steps.length - 1 && <div className="step-arrow">→</div>}
            </div>
          ))}
        </div>
      </div>

      <div className="master-bc-integration">
        <h3>🎯 Master Business Case Integration</h3>
        <div className="integration-content">
          <div className="integration-section">
            <h4>📊 Portfolio Management</h4>
            <p>Integrate individual business cases into a comprehensive portfolio view</p>
            <ul>
              <li>Strategic alignment across all initiatives</li>
              <li>Resource optimization and prioritization</li>
              <li>Cross-project dependencies management</li>
            </ul>
          </div>
          <div className="integration-section">
            <h4>🏛️ Governance Framework</h4>
            <p>Implement PMI/PGMP governance standards for enterprise-level oversight</p>
            <ul>
              <li>Standardized approval processes</li>
              <li>Stage-gate reviews and decision points</li>
              <li>Compliance and audit trails</li>
            </ul>
            <div className="compliance-quick-check">
              <div className="quick-check-score">
                <span className="score-text">Current Compliance: </span>
                <span className="score-value">87%</span>
              </div>
              <button
                className="check-compliance-btn"
                onClick={() => window.dispatchEvent(new CustomEvent('navigate', { detail: 'governance' }))}
              >
                🏛️ Check PMI/PGMP Compliance
              </button>
            </div>
          </div>
          <div className="integration-section">
            <h4>📈 Performance Monitoring</h4>
            <p>Track benefits realization and project performance</p>
            <ul>
              <li>Real-time dashboard monitoring</li>
              <li>Benefits tracking and validation</li>
              <li>Continuous improvement feedback</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )

  const renderProcessView = () => (
    <div className="process-view">
      <h3>🔄 The 6-Step Business Case Development Process</h3>

      <div className="detailed-steps">
        {steps.map((step) => (
          <div key={step.id} className={`detailed-step ${getStepStatus(step.id)}`}>
            <div className="step-header">
              <div className="step-indicator">
                <span className="step-number">{step.id}</span>
                <span className="step-icon">{step.icon}</span>
              </div>
              <div className="step-title">
                <h4>{step.title}</h4>
                <p>{step.description}</p>
              </div>
              <div className="step-status">
                {getStepStatus(step.id) === 'completed' && <span className="status-badge completed">✅ Completed</span>}
                {getStepStatus(step.id) === 'current' && <span className="status-badge current">🔄 In Progress</span>}
                {getStepStatus(step.id) === 'pending' && <span className="status-badge pending">⏳ Pending</span>}
              </div>
            </div>

            <div className="step-details">
              {step.id === 1 && (
                <div className="step-content">
                  <h5>Key Activities:</h5>
                  <ul>
                    <li>Define business problem or opportunity</li>
                    <li>Identify key stakeholders and their interests</li>
                    <li>Assess strategic alignment with business objectives</li>
                    <li>Document scope, constraints, and assumptions</li>
                  </ul>
                  <h5>Deliverables:</h5>
                  <ul>
                    <li>Problem/Opportunity Statement</li>
                    <li>Stakeholder Analysis</li>
                    <li>Strategic Alignment Assessment</li>
                  </ul>
                </div>
              )}

              {step.id === 2 && (
                <div className="step-content">
                  <h5>Key Activities:</h5>
                  <ul>
                    <li>Brainstorm potential solutions and approaches</li>
                    <li>Evaluate feasibility of each alternative</li>
                    <li>Assess pros, cons, and trade-offs</li>
                    <li>Estimate costs and timeframes</li>
                  </ul>
                  <h5>Deliverables:</h5>
                  <ul>
                    <li>Options Analysis Matrix</li>
                    <li>Feasibility Assessment</li>
                    <li>Preliminary Cost Estimates</li>
                  </ul>
                </div>
              )}

              {step.id === 3 && (
                <div className="step-content">
                  <h5>Key Activities:</h5>
                  <ul>
                    <li>Identify and quantify tangible benefits</li>
                    <li>Assess intangible benefits and their impact</li>
                    <li>Develop benefit realization timeline</li>
                    <li>Define success metrics and KPIs</li>
                  </ul>
                  <h5>Deliverables:</h5>
                  <ul>
                    <li>Benefits Register</li>
                    <li>Benefit Realization Plan</li>
                    <li>Success Criteria Definition</li>
                  </ul>
                </div>
              )}

              {step.id === 4 && (
                <div className="step-content">
                  <h5>Key Activities:</h5>
                  <ul>
                    <li>Identify potential risks and issues</li>
                    <li>Assess probability and impact</li>
                    <li>Develop mitigation strategies</li>
                    <li>Create contingency plans</li>
                  </ul>
                  <h5>Deliverables:</h5>
                  <ul>
                    <li>Risk Register</li>
                    <li>Risk Mitigation Plan</li>
                    <li>Contingency Planning</li>
                  </ul>
                </div>
              )}

              {step.id === 5 && (
                <div className="step-content">
                  <h5>Key Activities:</h5>
                  <ul>
                    <li>Compile comprehensive business case document</li>
                    <li>Perform detailed financial analysis</li>
                    <li>Develop implementation roadmap</li>
                    <li>Create executive summary</li>
                  </ul>
                  <h5>Deliverables:</h5>
                  <ul>
                    <li>Complete Business Case Document</li>
                    <li>Financial Analysis Report</li>
                    <li>Implementation Plan</li>
                  </ul>
                </div>
              )}

              {step.id === 6 && (
                <div className="step-content">
                  <h5>Key Activities:</h5>
                  <ul>
                    <li>Prepare presentation materials</li>
                    <li>Present to decision makers</li>
                    <li>Address questions and concerns</li>
                    <li>Obtain formal approval</li>
                  </ul>
                  <h5>Deliverables:</h5>
                  <ul>
                    <li>Executive Presentation</li>
                    <li>Approval Documentation</li>
                    <li>Next Steps Plan</li>
                  </ul>
                </div>
              )}
            </div>

            {getStepStatus(step.id) === 'current' && (
              <div className="step-actions">
                <button
                  className="btn btn-primary"
                  onClick={() => handleStepComplete(step.id)}
                >
                  Mark as Complete
                </button>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )

  const renderAlternativesView = () => (
    <div className="alternatives-view">
      <h3>⚖️ Alternatives Analysis</h3>
      <p>Systematic evaluation of different approaches to address the business problem or opportunity.</p>

      <div className="alternatives-grid">
        {frameworkData.alternatives.map((alternative) => (
          <div key={alternative.id} className="alternative-card">
            <div className="alternative-header">
              <h4>{alternative.name}</h4>
              <span className={`risk-badge ${alternative.riskLevel.toLowerCase()}`}>
                {alternative.riskLevel} Risk
              </span>
            </div>

            <div className="alternative-content">
              <p className="alternative-description">{alternative.description}</p>

              <div className="alternative-details">
                <div className="detail-item">
                  <strong>Estimated Cost:</strong> {formatCurrency(alternative.estimatedCost)}
                </div>
                <div className="detail-item">
                  <strong>Timeframe:</strong> {alternative.timeframe}
                </div>
              </div>

              <div className="pros-cons">
                <div className="pros">
                  <h5>✅ Pros</h5>
                  <ul>
                    {alternative.pros.map((pro, index) => (
                      <li key={index}>{pro}</li>
                    ))}
                  </ul>
                </div>

                <div className="cons">
                  <h5>❌ Cons</h5>
                  <ul>
                    {alternative.cons.map((con, index) => (
                      <li key={index}>{con}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>

            {alternative.id === 3 && (
              <div className="recommended-badge">
                ⭐ Recommended Solution
              </div>
            )}
          </div>
        ))}
      </div>

      <div className="alternatives-comparison">
        <h4>📊 Comparison Matrix</h4>
        <table className="comparison-table">
          <thead>
            <tr>
              <th>Alternative</th>
              <th>Cost</th>
              <th>Timeframe</th>
              <th>Risk Level</th>
              <th>Expected Impact</th>
              <th>Recommendation</th>
            </tr>
          </thead>
          <tbody>
            {frameworkData.alternatives.map((alternative) => (
              <tr key={alternative.id}>
                <td>{alternative.name}</td>
                <td>{formatCurrency(alternative.estimatedCost)}</td>
                <td>{alternative.timeframe}</td>
                <td>
                  <span className={`risk-badge ${alternative.riskLevel.toLowerCase()}`}>
                    {alternative.riskLevel}
                  </span>
                </td>
                <td>
                  {alternative.id === 1 && 'Minimal'}
                  {alternative.id === 2 && 'Low'}
                  {alternative.id === 3 && 'High'}
                </td>
                <td>
                  {alternative.id === 3 ? (
                    <span className="recommended">⭐ Recommended</span>
                  ) : (
                    <span className="not-recommended">Not Recommended</span>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )

  const renderIntegrationView = () => (
    <div className="integration-view">
      <h3>🔗 System Integration & Platform Connectivity</h3>
      <p>Seamless integration across all Master Business Case platform components</p>

      <div className="integration-overview">
        <div className="integration-diagram">
          <div className="central-hub">
            <div className="hub-icon">🎯</div>
            <div className="hub-title">Master Business Case</div>
          </div>

          <div className="integration-connections">
            <div className="connection-item" data-target="financial">
              <div className="connection-icon">🧮</div>
              <div className="connection-title">Financial Modeling</div>
              <div className="connection-status active">✅ Active</div>
            </div>

            <div className="connection-item" data-target="stakeholders">
              <div className="connection-icon">👥</div>
              <div className="connection-title">Stakeholder Management</div>
              <div className="connection-status active">✅ Active</div>
            </div>

            <div className="connection-item" data-target="compliance">
              <div className="connection-icon">🏛️</div>
              <div className="connection-title">PMI/PGMP Compliance</div>
              <div className="connection-status active">✅ Active</div>
            </div>

            <div className="connection-item" data-target="excel">
              <div className="connection-icon">🤖</div>
              <div className="connection-title">Excel & AI Analysis</div>
              <div className="connection-status active">✅ Active</div>
            </div>

            <div className="connection-item" data-target="roadmap">
              <div className="connection-icon">🗺️</div>
              <div className="connection-title">Change Management</div>
              <div className="connection-status active">✅ Active</div>
            </div>
          </div>
        </div>
      </div>

      <div className="integration-features">
        <h4>🔄 Cross-Component Data Flow</h4>
        <div className="data-flow-grid">
          <div className="flow-item">
            <div className="flow-source">📋 Business Case Framework</div>
            <div className="flow-arrow">→</div>
            <div className="flow-target">🧮 Financial Modeling</div>
            <div className="flow-description">
              Project parameters and assumptions automatically populate financial models
            </div>
          </div>

          <div className="flow-item">
            <div className="flow-source">🧮 Financial Analysis</div>
            <div className="flow-arrow">→</div>
            <div className="flow-target">👥 Stakeholder Reports</div>
            <div className="flow-description">
              Financial results and projections shared with stakeholder dashboards
            </div>
          </div>

          <div className="flow-item">
            <div className="flow-source">👥 Stakeholder Feedback</div>
            <div className="flow-arrow">→</div>
            <div className="flow-target">⚠️ Risk Assessment</div>
            <div className="flow-description">
              Stakeholder concerns automatically update risk registers
            </div>
          </div>

          <div className="flow-item">
            <div className="flow-source">🤖 Excel Analysis</div>
            <div className="flow-arrow">→</div>
            <div className="flow-target">📊 Master Dashboard</div>
            <div className="flow-description">
              Imported data enhances overall business case metrics
            </div>
          </div>
        </div>
      </div>

      <div className="integration-benefits">
        <h4>✨ Integration Benefits</h4>
        <div className="benefits-list">
          <div className="benefit-item">
            <div className="benefit-icon">🔄</div>
            <div className="benefit-content">
              <h5>Real-Time Synchronization</h5>
              <p>Changes in one component automatically update related modules</p>
            </div>
          </div>

          <div className="benefit-item">
            <div className="benefit-icon">📊</div>
            <div className="benefit-content">
              <h5>Unified Reporting</h5>
              <p>Comprehensive reports combining data from all platform components</p>
            </div>
          </div>

          <div className="benefit-item">
            <div className="benefit-icon">🎯</div>
            <div className="benefit-content">
              <h5>Strategic Alignment</h5>
              <p>Ensures all activities align with master business case objectives</p>
            </div>
          </div>

          <div className="benefit-item">
            <div className="benefit-icon">⚡</div>
            <div className="benefit-content">
              <h5>Efficiency Gains</h5>
              <p>Eliminates duplicate data entry and manual synchronization</p>
            </div>
          </div>
        </div>
      </div>

      <div className="integration-actions">
        <h4>🚀 Quick Integration Actions</h4>
        <div className="action-buttons">
          <button className="integration-btn financial" onClick={() => window.dispatchEvent(new CustomEvent('navigate', { detail: 'modeling' }))}>
            🧮 Open Financial Modeling
          </button>
          <button className="integration-btn stakeholders" onClick={() => window.dispatchEvent(new CustomEvent('navigate', { detail: 'stakeholders' }))}>
            👥 Manage Stakeholders
          </button>
          <button className="integration-btn compliance" onClick={() => window.dispatchEvent(new CustomEvent('navigate', { detail: 'governance' }))}>
            🏛️ PMI Compliance
          </button>
          <button className="integration-btn excel" onClick={() => window.dispatchEvent(new CustomEvent('navigate', { detail: 'excel' }))}>
            🤖 Excel Analysis
          </button>
        </div>
      </div>
    </div>
  )

  const renderBenefitsView = () => (
    <div className="benefits-view">
      <h3>📈 Benefit Analysis & Quantification</h3>
      <p>Systematic evaluation and quantification of expected project benefits</p>

      <div className="benefits-overview">
        <div className="benefits-categories">
          <h4>💰 Quantifiable Benefits</h4>
          <div className="benefits-grid">
            <div className="benefit-item">
              <div className="benefit-icon">💵</div>
              <div className="benefit-content">
                <h5>Cost Reduction</h5>
                <div className="benefit-value">$2.5M annually</div>
                <div className="benefit-description">Operational efficiency improvements and automation</div>
              </div>
            </div>

            <div className="benefit-item">
              <div className="benefit-icon">📈</div>
              <div className="benefit-content">
                <h5>Revenue Growth</h5>
                <div className="benefit-value">$4.2M annually</div>
                <div className="benefit-description">Enhanced customer experience and new market opportunities</div>
              </div>
            </div>

            <div className="benefit-item">
              <div className="benefit-icon">⚡</div>
              <div className="benefit-content">
                <h5>Productivity Gains</h5>
                <div className="benefit-value">35% improvement</div>
                <div className="benefit-description">Process automation and workflow optimization</div>
              </div>
            </div>

            <div className="benefit-item">
              <div className="benefit-icon">🎯</div>
              <div className="benefit-content">
                <h5>Quality Improvement</h5>
                <div className="benefit-value">50% error reduction</div>
                <div className="benefit-description">Automated validation and quality controls</div>
              </div>
            </div>
          </div>
        </div>

        <div className="intangible-benefits">
          <h4>🌟 Intangible Benefits</h4>
          <div className="intangible-list">
            <div className="intangible-item">
              <span className="intangible-icon">😊</span>
              <div className="intangible-content">
                <h5>Customer Satisfaction</h5>
                <p>Improved user experience and service quality</p>
              </div>
            </div>

            <div className="intangible-item">
              <span className="intangible-icon">👥</span>
              <div className="intangible-content">
                <h5>Employee Engagement</h5>
                <p>Modern tools and streamlined processes</p>
              </div>
            </div>

            <div className="intangible-item">
              <span className="intangible-icon">🏆</span>
              <div className="intangible-content">
                <h5>Competitive Advantage</h5>
                <p>Market leadership through innovation</p>
              </div>
            </div>

            <div className="intangible-item">
              <span className="intangible-icon">🔒</span>
              <div className="intangible-content">
                <h5>Risk Mitigation</h5>
                <p>Enhanced security and compliance</p>
              </div>
            </div>
          </div>
        </div>

        <div className="benefit-realization">
          <h4>📅 Benefit Realization Timeline</h4>
          <div className="timeline-chart">
            <div className="timeline-period">
              <div className="period-header">Year 1</div>
              <div className="period-benefits">
                <div className="benefit-bar" style={{ width: '30%' }}>30% of benefits</div>
              </div>
            </div>
            <div className="timeline-period">
              <div className="period-header">Year 2</div>
              <div className="period-benefits">
                <div className="benefit-bar" style={{ width: '70%' }}>70% of benefits</div>
              </div>
            </div>
            <div className="timeline-period">
              <div className="period-header">Year 3+</div>
              <div className="period-benefits">
                <div className="benefit-bar" style={{ width: '100%' }}>100% of benefits</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  const renderRisksView = () => (
    <div className="risks-view">
      <h3>⚠️ Risk Assessment & Mitigation</h3>
      <p>Comprehensive identification and management of project risks</p>

      <div className="risk-overview">
        <div className="risk-matrix">
          <h4>🎯 Risk Assessment Matrix</h4>
          <div className="matrix-grid">
            <div className="matrix-header">
              <div className="matrix-cell"></div>
              <div className="matrix-cell">Low Impact</div>
              <div className="matrix-cell">Medium Impact</div>
              <div className="matrix-cell">High Impact</div>
            </div>
            <div className="matrix-row">
              <div className="matrix-cell">High Probability</div>
              <div className="matrix-cell risk-medium">2 risks</div>
              <div className="matrix-cell risk-high">3 risks</div>
              <div className="matrix-cell risk-critical">1 risk</div>
            </div>
            <div className="matrix-row">
              <div className="matrix-cell">Medium Probability</div>
              <div className="matrix-cell risk-low">4 risks</div>
              <div className="matrix-cell risk-medium">5 risks</div>
              <div className="matrix-cell risk-high">2 risks</div>
            </div>
            <div className="matrix-row">
              <div className="matrix-cell">Low Probability</div>
              <div className="matrix-cell risk-low">6 risks</div>
              <div className="matrix-cell risk-low">3 risks</div>
              <div className="matrix-cell risk-medium">1 risk</div>
            </div>
          </div>
        </div>

        <div className="risk-categories">
          <h4>📊 Risk Categories</h4>
          <div className="risk-category-grid">
            <div className="risk-category">
              <div className="category-header">
                <span className="category-icon">💻</span>
                <h5>Technical Risks</h5>
              </div>
              <div className="risk-list">
                <div className="risk-item high">
                  <span className="risk-name">System Integration Complexity</span>
                  <span className="risk-level">High</span>
                </div>
                <div className="risk-item medium">
                  <span className="risk-name">Technology Obsolescence</span>
                  <span className="risk-level">Medium</span>
                </div>
                <div className="risk-item medium">
                  <span className="risk-name">Performance Issues</span>
                  <span className="risk-level">Medium</span>
                </div>
              </div>
            </div>

            <div className="risk-category">
              <div className="category-header">
                <span className="category-icon">💼</span>
                <h5>Business Risks</h5>
              </div>
              <div className="risk-list">
                <div className="risk-item high">
                  <span className="risk-name">Market Changes</span>
                  <span className="risk-level">High</span>
                </div>
                <div className="risk-item medium">
                  <span className="risk-name">Budget Overruns</span>
                  <span className="risk-level">Medium</span>
                </div>
                <div className="risk-item low">
                  <span className="risk-name">Competitive Response</span>
                  <span className="risk-level">Low</span>
                </div>
              </div>
            </div>

            <div className="risk-category">
              <div className="category-header">
                <span className="category-icon">👥</span>
                <h5>Organizational Risks</h5>
              </div>
              <div className="risk-list">
                <div className="risk-item medium">
                  <span className="risk-name">Change Resistance</span>
                  <span className="risk-level">Medium</span>
                </div>
                <div className="risk-item medium">
                  <span className="risk-name">Skill Gaps</span>
                  <span className="risk-level">Medium</span>
                </div>
                <div className="risk-item low">
                  <span className="risk-name">Resource Availability</span>
                  <span className="risk-level">Low</span>
                </div>
              </div>
            </div>

            <div className="risk-category">
              <div className="category-header">
                <span className="category-icon">🏛️</span>
                <h5>Regulatory Risks</h5>
              </div>
              <div className="risk-list">
                <div className="risk-item medium">
                  <span className="risk-name">Compliance Changes</span>
                  <span className="risk-level">Medium</span>
                </div>
                <div className="risk-item low">
                  <span className="risk-name">Data Privacy</span>
                  <span className="risk-level">Low</span>
                </div>
                <div className="risk-item low">
                  <span className="risk-name">Security Requirements</span>
                  <span className="risk-level">Low</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="mitigation-strategies">
          <h4>🛡️ Mitigation Strategies</h4>
          <div className="mitigation-list">
            <div className="mitigation-item">
              <div className="mitigation-header">
                <span className="mitigation-icon">🔧</span>
                <h5>Technical Mitigation</h5>
              </div>
              <ul>
                <li>Proof of concept development</li>
                <li>Phased implementation approach</li>
                <li>Regular architecture reviews</li>
                <li>Backup technology options</li>
              </ul>
            </div>

            <div className="mitigation-item">
              <div className="mitigation-header">
                <span className="mitigation-icon">📋</span>
                <h5>Project Management</h5>
              </div>
              <ul>
                <li>Detailed project planning</li>
                <li>Regular milestone reviews</li>
                <li>Contingency budget allocation</li>
                <li>Stakeholder communication plan</li>
              </ul>
            </div>

            <div className="mitigation-item">
              <div className="mitigation-header">
                <span className="mitigation-icon">🎓</span>
                <h5>Change Management</h5>
              </div>
              <ul>
                <li>Comprehensive training programs</li>
                <li>Change champion network</li>
                <li>User feedback mechanisms</li>
                <li>Gradual rollout strategy</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  const renderFinancialView = () => (
    <div className="financial-view">
      <h3>💰 Financial Analysis & Modeling</h3>
      <p>Comprehensive financial evaluation and investment analysis</p>

      <div className="financial-overview">
        <div className="financial-metrics">
          <h4>📊 Key Financial Metrics</h4>
          <div className="metrics-grid">
            <div className="metric-card">
              <div className="metric-icon">💵</div>
              <div className="metric-content">
                <div className="metric-value">$2.8M</div>
                <div className="metric-label">Total Investment</div>
                <div className="metric-description">Initial capital and operational costs</div>
              </div>
            </div>

            <div className="metric-card">
              <div className="metric-icon">📈</div>
              <div className="metric-content">
                <div className="metric-value">$1.2M</div>
                <div className="metric-label">Net Present Value</div>
                <div className="metric-description">Discounted future cash flows</div>
              </div>
            </div>

            <div className="metric-card">
              <div className="metric-icon">🎯</div>
              <div className="metric-content">
                <div className="metric-value">18.5%</div>
                <div className="metric-label">Internal Rate of Return</div>
                <div className="metric-description">Expected annual return rate</div>
              </div>
            </div>

            <div className="metric-card">
              <div className="metric-icon">⏱️</div>
              <div className="metric-content">
                <div className="metric-value">2.3 years</div>
                <div className="metric-label">Payback Period</div>
                <div className="metric-description">Time to recover investment</div>
              </div>
            </div>
          </div>
        </div>

        <div className="scenario-analysis">
          <h4>🎲 Scenario Analysis</h4>
          <div className="scenario-table">
            <div className="scenario-header">
              <div className="scenario-cell">Metric</div>
              <div className="scenario-cell">Pessimistic</div>
              <div className="scenario-cell">Realistic</div>
              <div className="scenario-cell">Optimistic</div>
            </div>
            <div className="scenario-row">
              <div className="scenario-cell">NPV</div>
              <div className="scenario-cell pessimistic">$0.8M</div>
              <div className="scenario-cell realistic">$1.2M</div>
              <div className="scenario-cell optimistic">$1.8M</div>
            </div>
            <div className="scenario-row">
              <div className="scenario-cell">IRR</div>
              <div className="scenario-cell pessimistic">14.2%</div>
              <div className="scenario-cell realistic">18.5%</div>
              <div className="scenario-cell optimistic">24.1%</div>
            </div>
            <div className="scenario-row">
              <div className="scenario-cell">Payback</div>
              <div className="scenario-cell pessimistic">3.1 years</div>
              <div className="scenario-cell realistic">2.3 years</div>
              <div className="scenario-cell optimistic">1.8 years</div>
            </div>
          </div>
        </div>

        <div className="financial-assumptions">
          <h4>📋 Key Assumptions</h4>
          <div className="assumptions-list">
            <div className="assumption-item">
              <span className="assumption-icon">📊</span>
              <div className="assumption-content">
                <h5>Revenue Growth</h5>
                <p>15% annual growth in revenue from improved capabilities</p>
              </div>
            </div>
            <div className="assumption-item">
              <span className="assumption-icon">💰</span>
              <div className="assumption-content">
                <h5>Cost Savings</h5>
                <p>25% reduction in operational costs through automation</p>
              </div>
            </div>
            <div className="assumption-item">
              <span className="assumption-icon">⏰</span>
              <div className="assumption-content">
                <h5>Implementation Timeline</h5>
                <p>18-month implementation with phased rollout</p>
              </div>
            </div>
            <div className="assumption-item">
              <span className="assumption-icon">🎯</span>
              <div className="assumption-content">
                <h5>Discount Rate</h5>
                <p>10% weighted average cost of capital</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  const renderMasterBusinessCaseView = () => (
    <div className="master-bc-view">
      <h3>🎯 Master Business Case Integration</h3>
      <p>Enterprise-level business case management and portfolio integration</p>

      <div className="master-bc-overview">
        <div className="compliance-check-section">
          <h4>🏛️ PMI/PGMP Compliance Check</h4>
          <div className="compliance-overview">
            <div className="compliance-status">
              <div className="compliance-score">
                <div className="score-circle">
                  <div className="score-value">87%</div>
                  <div className="score-label">Compliance</div>
                </div>
              </div>
              <div className="compliance-details">
                <div className="compliance-item good">
                  <span className="compliance-icon">✅</span>
                  <span className="compliance-text">Project Charter Defined</span>
                </div>
                <div className="compliance-item good">
                  <span className="compliance-icon">✅</span>
                  <span className="compliance-text">Stakeholder Analysis Complete</span>
                </div>
                <div className="compliance-item warning">
                  <span className="compliance-icon">⚠️</span>
                  <span className="compliance-text">Risk Register Needs Update</span>
                </div>
                <div className="compliance-item good">
                  <span className="compliance-icon">✅</span>
                  <span className="compliance-text">Financial Analysis Complete</span>
                </div>
                <div className="compliance-item pending">
                  <span className="compliance-icon">⏳</span>
                  <span className="compliance-text">Benefits Realization Plan</span>
                </div>
              </div>
            </div>
            <div className="compliance-actions">
              <button
                className="compliance-btn primary"
                onClick={() => window.dispatchEvent(new CustomEvent('navigate', { detail: 'governance' }))}
              >
                🏛️ Check Full PMI/PGMP Compliance
              </button>
              <button className="compliance-btn secondary">
                📋 Generate Compliance Report
              </button>
              <button className="compliance-btn secondary">
                🔄 Run Compliance Audit
              </button>
            </div>
          </div>
        </div>

        <div className="integration-status">
          <h4>🔗 Integration Status</h4>
          <div className="status-grid">
            <div className="status-item completed">
              <span className="status-icon">✅</span>
              <span className="status-text">Framework Development</span>
            </div>
            <div className="status-item completed">
              <span className="status-icon">✅</span>
              <span className="status-text">Financial Modeling</span>
            </div>
            <div className="status-item in-progress">
              <span className="status-icon">🔄</span>
              <span className="status-text">Stakeholder Engagement</span>
            </div>
            <div className="status-item warning">
              <span className="status-icon">⚠️</span>
              <span className="status-text">Risk Assessment</span>
            </div>
            <div className="status-item pending">
              <span className="status-icon">⏳</span>
              <span className="status-text">Compliance Validation</span>
            </div>
          </div>
        </div>

        <div className="portfolio-integration">
          <h4>📊 Portfolio Integration</h4>
          <div className="portfolio-metrics">
            <div className="portfolio-card">
              <div className="portfolio-icon">💼</div>
              <div className="portfolio-content">
                <h5>Active Projects</h5>
                <div className="portfolio-value">12</div>
                <div className="portfolio-description">Currently in portfolio</div>
              </div>
            </div>
            <div className="portfolio-card">
              <div className="portfolio-icon">💰</div>
              <div className="portfolio-content">
                <h5>Total Investment</h5>
                <div className="portfolio-value">$24.5M</div>
                <div className="portfolio-description">Portfolio value</div>
              </div>
            </div>
            <div className="portfolio-card">
              <div className="portfolio-icon">📈</div>
              <div className="portfolio-content">
                <h5>Expected ROI</h5>
                <div className="portfolio-value">22.3%</div>
                <div className="portfolio-description">Weighted average</div>
              </div>
            </div>
          </div>
        </div>

        <div className="governance-framework">
          <h4>🏛️ Governance Framework</h4>
          <div className="governance-content">
            <div className="governance-item">
              <h5>📋 Project Management Standards</h5>
              <p>Adherence to PMI PMBOK Guide 7th Edition and PgMP standards</p>
              <div className="governance-status">
                <span className="status-badge good">Compliant</span>
              </div>
            </div>
            <div className="governance-item">
              <h5>🔍 Quality Assurance</h5>
              <p>Regular quality reviews and compliance audits</p>
              <div className="governance-status">
                <span className="status-badge good">Active</span>
              </div>
            </div>
            <div className="governance-item">
              <h5>📊 Performance Monitoring</h5>
              <p>KPI tracking and benefits realization monitoring</p>
              <div className="governance-status">
                <span className="status-badge warning">Needs Attention</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <div className="business-case-framework">
      <div className="framework-header">
        <h2>📋 Business Case Development Framework</h2>
        <div className="framework-status">
          <span className="current-step">Step {frameworkData.currentStep} of 6</span>
          <div className="progress-bar">
            <div
              className="progress-fill"
              style={{ width: `${(frameworkData.completedSteps.length / 6) * 100}%` }}
            ></div>
          </div>
        </div>
      </div>

      <div className="framework-navigation">
        {views.map((view) => (
          <button
            key={view.id}
            className={`nav-btn ${activeView === view.id ? 'active' : ''}`}
            onClick={() => setActiveView(view.id)}
          >
            <span className="nav-icon">{view.icon}</span>
            <span className="nav-label">{view.label}</span>
          </button>
        ))}
      </div>

      <div className="framework-content">
        {activeView === 'overview' && renderOverview()}
        {activeView === 'process' && renderProcessView()}
        {activeView === 'alternatives' && renderAlternativesView()}
        {activeView === 'benefits' && renderBenefitsView()}
        {activeView === 'risks' && renderRisksView()}
        {activeView === 'financial' && renderFinancialView()}
        {activeView === 'integration' && renderIntegrationView()}
        {activeView === 'master' && renderMasterBusinessCaseView()}
      </div>
    </div>
  )
}

export default BusinessCaseFramework
