@echo off
echo 🚀 Starting Master Business Case Governance Platform
echo.

echo 📦 Installing dependencies...
echo.

echo Installing frontend dependencies...
call npm install

echo.
echo Installing backend dependencies...
cd server
call npm install
cd ..

echo.
echo 🗄️ Starting MongoDB (make sure MongoDB is installed and running)
echo If you're using MongoDB Atlas, make sure to update server/.env with your connection string
echo.

echo 🔧 Starting backend server...
start cmd /k "cd server && npm run dev"

echo.
echo ⏳ Waiting for backend to start...
timeout /t 5 /nobreak > nul

echo.
echo 🎨 Starting frontend application...
start cmd /k "npm run dev"

echo.
echo ✅ Both servers are starting!
echo.
echo 📍 Access points:
echo   Frontend: http://localhost:3000
echo   Backend API: http://localhost:5000
echo   API Health: http://localhost:5000/api/health
echo.
echo 🎯 Features available:
echo   ✅ Create, Read, Update, Delete Master Business Cases
echo   ✅ Search and filter business cases
echo   ✅ Status management and workflow
echo   ✅ Export data in multiple formats
echo   ✅ Real-time MongoDB integration
echo.
echo Press any key to exit...
pause > nul
