import React, { useState, useEffect, useCallback } from 'react'

const AdvancedFinancialModeling = ({ data, onChange }) => {
  const [modelData, setModelData] = useState({
    // 1. Input Parameters
    parameters: {
      revenueGrowthRate: 15,
      costInflationRate: 5,
      discountRate: 10,
      taxRate: 25,
      workingCapitalRate: 5,
      projectDuration: 5,
      currency: 'USD'
    },

    // 2. Capital Expenditure
    capex: {
      initialInvestment: 2000000,
      yearlyCapex: [500000, 300000, 200000, 100000, 50000],
      depreciationRate: 20,
      salvageValue: 100000
    },

    // 3. Operational Expenses
    opex: {
      fixedCosts: 800000,
      variableCostRate: 35, // % of revenue
      marketingExpenses: 200000,
      adminExpenses: 150000,
      totalOpex: 0 // Auto-calculated
    },

    // 4. Cost and Sales Definition
    costSales: {
      products: [
        {
          id: 1,
          name: 'Product A',
          unitCost: 50,
          salesPrice: 100,
          volume: [10000, 12000, 15000, 18000, 22000],
          totalCost: 0, // Auto-calculated
          totalSales: 0, // Auto-calculated
          grossMargin: 0 // Auto-calculated
        },
        {
          id: 2,
          name: 'Product B',
          unitCost: 75,
          salesPrice: 150,
          volume: [5000, 6000, 8000, 10000, 12000],
          totalCost: 0,
          totalSales: 0,
          grossMargin: 0
        }
      ],
      totalRevenue: [0, 0, 0, 0, 0], // Auto-calculated
      totalCosts: [0, 0, 0, 0, 0], // Auto-calculated
      grossProfit: [0, 0, 0, 0, 0] // Auto-calculated
    },

    // 5. Sensitivity Analysis Scenarios
    scenarios: {
      bestCase: {
        name: 'Best Case',
        revenueMultiplier: 1.3,
        costMultiplier: 0.9,
        probability: 20,
        active: false
      },
      baseCase: {
        name: 'Base Case',
        revenueMultiplier: 1.0,
        costMultiplier: 1.0,
        probability: 60,
        active: true
      },
      worstCase: {
        name: 'Worst Case',
        revenueMultiplier: 0.7,
        costMultiplier: 1.1,
        probability: 20,
        active: false
      }
    },

    // 6. Financial Indexes (Auto-calculated)
    financialIndexes: {
      npv: 0,
      irr: 0,
      paybackPeriod: 0,
      profitabilityIndex: 0,
      breakEvenPoint: 0
    },

    // 7. Cash Flow (Auto-calculated)
    cashFlow: {
      years: [2024, 2025, 2026, 2027, 2028],
      revenue: [0, 0, 0, 0, 0],
      costs: [0, 0, 0, 0, 0],
      ebitda: [0, 0, 0, 0, 0],
      depreciation: [0, 0, 0, 0, 0],
      ebit: [0, 0, 0, 0, 0],
      tax: [0, 0, 0, 0, 0],
      netIncome: [0, 0, 0, 0, 0],
      freeCashFlow: [0, 0, 0, 0, 0],
      cumulativeCashFlow: [0, 0, 0, 0, 0]
    },

    // 8. Audit Trail & Versioning
    auditTrail: [],
    version: 1,
    lastModified: new Date().toISOString(),
    modifiedBy: 'Current User',

    ...data
  })

  const [activeView, setActiveView] = useState('parameters')
  const [activeScenario, setActiveScenario] = useState('baseCase')
  const [validationErrors, setValidationErrors] = useState({})
  const [isCalculating, setIsCalculating] = useState(false)

  // Auto-calculation functions
  const calculateCostSales = useCallback(() => {
    const updatedProducts = modelData.costSales.products.map(product => {
      const totalCost = product.volume.map(vol => vol * product.unitCost)
      const totalSales = product.volume.map(vol => vol * product.salesPrice)
      const grossMargin = totalSales.map((sales, idx) => sales - totalCost[idx])

      return {
        ...product,
        totalCost,
        totalSales,
        grossMargin
      }
    })

    // Calculate totals across all products
    const totalRevenue = Array(5).fill(0)
    const totalCosts = Array(5).fill(0)
    const grossProfit = Array(5).fill(0)

    updatedProducts.forEach(product => {
      product.totalSales.forEach((sales, idx) => {
        totalRevenue[idx] += sales
      })
      product.totalCost.forEach((cost, idx) => {
        totalCosts[idx] += cost
      })
      product.grossMargin.forEach((margin, idx) => {
        grossProfit[idx] += margin
      })
    })

    return {
      products: updatedProducts,
      totalRevenue,
      totalCosts,
      grossProfit
    }
  }, [modelData.costSales.products])

  const calculateCashFlow = useCallback(() => {
    const scenario = modelData.scenarios[activeScenario]
    const { totalRevenue, totalCosts } = calculateCostSales()

    // Apply scenario multipliers
    const adjustedRevenue = totalRevenue.map(rev => rev * scenario.revenueMultiplier)
    const adjustedCosts = totalCosts.map(cost => cost * scenario.costMultiplier)

    // Calculate operational expenses
    const opexPerYear = adjustedRevenue.map(rev => {
      const variableCosts = rev * (modelData.opex.variableCostRate / 100)
      return modelData.opex.fixedCosts + variableCosts + modelData.opex.marketingExpenses + modelData.opex.adminExpenses
    })

    // Calculate EBITDA
    const ebitda = adjustedRevenue.map((rev, idx) => rev - adjustedCosts[idx] - opexPerYear[idx])

    // Calculate depreciation
    const depreciation = modelData.capex.yearlyCapex.map(capex => capex * (modelData.parameters.depreciationRate / 100))

    // Calculate EBIT
    const ebit = ebitda.map((ebitdaVal, idx) => ebitdaVal - (depreciation[idx] || 0))

    // Calculate tax
    const tax = ebit.map(ebitVal => Math.max(0, ebitVal * (modelData.parameters.taxRate / 100)))

    // Calculate net income
    const netIncome = ebit.map((ebitVal, idx) => ebitVal - tax[idx])

    // Calculate free cash flow
    const freeCashFlow = netIncome.map((ni, idx) => {
      const capexAmount = modelData.capex.yearlyCapex[idx] || 0
      const workingCapitalChange = idx === 0 ? 0 : (adjustedRevenue[idx] - adjustedRevenue[idx - 1]) * (modelData.parameters.workingCapitalRate / 100)
      return ni + (depreciation[idx] || 0) - capexAmount - workingCapitalChange
    })

    // Add initial investment to first year
    freeCashFlow[0] -= modelData.capex.initialInvestment

    // Calculate cumulative cash flow
    const cumulativeCashFlow = []
    let cumulative = 0
    freeCashFlow.forEach(fcf => {
      cumulative += fcf
      cumulativeCashFlow.push(cumulative)
    })

    return {
      years: [2024, 2025, 2026, 2027, 2028],
      revenue: adjustedRevenue,
      costs: adjustedCosts.map((cost, idx) => cost + opexPerYear[idx]),
      ebitda,
      depreciation,
      ebit,
      tax,
      netIncome,
      freeCashFlow,
      cumulativeCashFlow
    }
  }, [modelData, activeScenario, calculateCostSales])

  const calculateFinancialIndexes = useCallback(() => {
    const cashFlow = calculateCashFlow()
    const discountRate = modelData.parameters.discountRate / 100

    // Calculate NPV
    let npv = -modelData.capex.initialInvestment
    cashFlow.freeCashFlow.forEach((fcf, idx) => {
      if (idx > 0) { // Skip initial investment year
        npv += fcf / Math.pow(1 + discountRate, idx)
      }
    })

    // Calculate IRR (simplified approximation)
    let irr = 0
    for (let rate = 0.01; rate <= 1; rate += 0.001) {
      let npvTest = -modelData.capex.initialInvestment
      cashFlow.freeCashFlow.forEach((fcf, idx) => {
        if (idx > 0) {
          npvTest += fcf / Math.pow(1 + rate, idx)
        }
      })
      if (Math.abs(npvTest) < 1000) {
        irr = rate * 100
        break
      }
    }

    // Calculate Payback Period
    let paybackPeriod = 0
    let cumulativeFlow = -modelData.capex.initialInvestment
    for (let i = 0; i < cashFlow.freeCashFlow.length; i++) {
      cumulativeFlow += cashFlow.freeCashFlow[i]
      if (cumulativeFlow >= 0) {
        paybackPeriod = i + 1
        break
      }
    }

    // Calculate Profitability Index
    const profitabilityIndex = (npv + modelData.capex.initialInvestment) / modelData.capex.initialInvestment

    // Calculate Break-even Point (simplified)
    const avgRevenue = cashFlow.revenue.reduce((sum, rev) => sum + rev, 0) / cashFlow.revenue.length
    const avgCosts = cashFlow.costs.reduce((sum, cost) => sum + cost, 0) / cashFlow.costs.length
    const breakEvenPoint = avgCosts / (avgRevenue > 0 ? avgRevenue : 1) * 100

    return {
      npv: Math.round(npv),
      irr: Math.round(irr * 10) / 10,
      paybackPeriod: Math.round(paybackPeriod * 10) / 10,
      profitabilityIndex: Math.round(profitabilityIndex * 100) / 100,
      breakEvenPoint: Math.round(breakEvenPoint * 10) / 10
    }
  }, [modelData, calculateCashFlow])

  // Real-time updates when parameters change
  useEffect(() => {
    setIsCalculating(true)

    const timer = setTimeout(() => {
      const updatedCostSales = calculateCostSales()
      const updatedCashFlow = calculateCashFlow()
      const updatedIndexes = calculateFinancialIndexes()

      setModelData(prev => ({
        ...prev,
        costSales: updatedCostSales,
        cashFlow: updatedCashFlow,
        financialIndexes: updatedIndexes
      }))

      setIsCalculating(false)
    }, 300) // Debounce calculations

    return () => clearTimeout(timer)
  }, [modelData.parameters, modelData.capex, modelData.opex, activeScenario, calculateCostSales, calculateCashFlow, calculateFinancialIndexes])

  // CRUD Operations
  const handleParameterChange = (category, field, value) => {
    const updatedData = {
      ...modelData,
      [category]: {
        ...modelData[category],
        [field]: value
      },
      lastModified: new Date().toISOString(),
      auditTrail: [
        ...modelData.auditTrail,
        {
          timestamp: new Date().toISOString(),
          action: 'UPDATE',
          field: `${category}.${field}`,
          oldValue: modelData[category][field],
          newValue: value,
          user: 'Current User'
        }
      ]
    }

    setModelData(updatedData)
    if (onChange) onChange(updatedData)
  }

  const handleProductUpdate = (productId, field, value) => {
    const updatedProducts = modelData.costSales.products.map(product => {
      if (product.id === productId) {
        return { ...product, [field]: value }
      }
      return product
    })

    const updatedData = {
      ...modelData,
      costSales: {
        ...modelData.costSales,
        products: updatedProducts
      },
      lastModified: new Date().toISOString(),
      auditTrail: [
        ...modelData.auditTrail,
        {
          timestamp: new Date().toISOString(),
          action: 'UPDATE',
          field: `product.${productId}.${field}`,
          oldValue: modelData.costSales.products.find(p => p.id === productId)?.[field],
          newValue: value,
          user: 'Current User'
        }
      ]
    }

    setModelData(updatedData)
    if (onChange) onChange(updatedData)
  }

  const addProduct = () => {
    const newProduct = {
      id: Math.max(...modelData.costSales.products.map(p => p.id)) + 1,
      name: `Product ${modelData.costSales.products.length + 1}`,
      unitCost: 0,
      salesPrice: 0,
      volume: [0, 0, 0, 0, 0],
      totalCost: 0,
      totalSales: 0,
      grossMargin: 0
    }

    const updatedData = {
      ...modelData,
      costSales: {
        ...modelData.costSales,
        products: [...modelData.costSales.products, newProduct]
      },
      lastModified: new Date().toISOString(),
      auditTrail: [
        ...modelData.auditTrail,
        {
          timestamp: new Date().toISOString(),
          action: 'CREATE',
          field: 'product',
          newValue: newProduct,
          user: 'Current User'
        }
      ]
    }

    setModelData(updatedData)
    if (onChange) onChange(updatedData)
  }

  const deleteProduct = (productId) => {
    if (window.confirm('Are you sure you want to delete this product?')) {
      const updatedProducts = modelData.costSales.products.filter(p => p.id !== productId)

      const updatedData = {
        ...modelData,
        costSales: {
          ...modelData.costSales,
          products: updatedProducts
        },
        lastModified: new Date().toISOString(),
        auditTrail: [
          ...modelData.auditTrail,
          {
            timestamp: new Date().toISOString(),
            action: 'DELETE',
            field: 'product',
            oldValue: modelData.costSales.products.find(p => p.id === productId),
            user: 'Current User'
          }
        ]
      }

      setModelData(updatedData)
      if (onChange) onChange(updatedData)
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: modelData.parameters.currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const formatPercentage = (value) => {
    return `${value}%`
  }

  const views = [
    { id: 'parameters', label: 'Input Parameters', icon: '⚙️' },
    { id: 'costsales', label: 'Cost & Sales Definition', icon: '💰' },
    { id: 'sensitivity', label: 'Sensitivity Analysis', icon: '📊' },
    { id: 'indexes', label: 'Financial Indexes', icon: '📈' },
    { id: 'cashflow', label: 'Cash Flow Analysis', icon: '💸' },
    { id: 'audit', label: 'Audit Trail', icon: '📋' }
  ]

  // Render Functions
  const renderParametersView = () => (
    <div className="parameters-view">
      <h3>⚙️ Input Parameters Setup</h3>

      <div className="parameters-grid">
        <div className="parameter-section">
          <h4>📊 Financial Assumptions</h4>
          <div className="parameter-group">
            <label>Revenue Growth Rate (%)</label>
            <input
              type="number"
              value={modelData.parameters.revenueGrowthRate}
              onChange={(e) => handleParameterChange('parameters', 'revenueGrowthRate', parseFloat(e.target.value))}
              className="parameter-input"
            />
          </div>
          <div className="parameter-group">
            <label>Cost Inflation Rate (%)</label>
            <input
              type="number"
              value={modelData.parameters.costInflationRate}
              onChange={(e) => handleParameterChange('parameters', 'costInflationRate', parseFloat(e.target.value))}
              className="parameter-input"
            />
          </div>
          <div className="parameter-group">
            <label>Discount Rate (%)</label>
            <input
              type="number"
              value={modelData.parameters.discountRate}
              onChange={(e) => handleParameterChange('parameters', 'discountRate', parseFloat(e.target.value))}
              className="parameter-input"
            />
          </div>
          <div className="parameter-group">
            <label>Tax Rate (%)</label>
            <input
              type="number"
              value={modelData.parameters.taxRate}
              onChange={(e) => handleParameterChange('parameters', 'taxRate', parseFloat(e.target.value))}
              className="parameter-input"
            />
          </div>
        </div>

        <div className="parameter-section">
          <h4>💰 Capital Expenditure</h4>
          <div className="parameter-group">
            <label>Initial Investment</label>
            <input
              type="number"
              value={modelData.capex.initialInvestment}
              onChange={(e) => handleParameterChange('capex', 'initialInvestment', parseFloat(e.target.value))}
              className="parameter-input"
            />
          </div>
          <div className="parameter-group">
            <label>Depreciation Rate (%)</label>
            <input
              type="number"
              value={modelData.capex.depreciationRate}
              onChange={(e) => handleParameterChange('capex', 'depreciationRate', parseFloat(e.target.value))}
              className="parameter-input"
            />
          </div>
          <div className="parameter-group">
            <label>Salvage Value</label>
            <input
              type="number"
              value={modelData.capex.salvageValue}
              onChange={(e) => handleParameterChange('capex', 'salvageValue', parseFloat(e.target.value))}
              className="parameter-input"
            />
          </div>
        </div>

        <div className="parameter-section">
          <h4>🏢 Operational Expenses</h4>
          <div className="parameter-group">
            <label>Fixed Costs (Annual)</label>
            <input
              type="number"
              value={modelData.opex.fixedCosts}
              onChange={(e) => handleParameterChange('opex', 'fixedCosts', parseFloat(e.target.value))}
              className="parameter-input"
            />
          </div>
          <div className="parameter-group">
            <label>Variable Cost Rate (% of Revenue)</label>
            <input
              type="number"
              value={modelData.opex.variableCostRate}
              onChange={(e) => handleParameterChange('opex', 'variableCostRate', parseFloat(e.target.value))}
              className="parameter-input"
            />
          </div>
          <div className="parameter-group">
            <label>Marketing Expenses</label>
            <input
              type="number"
              value={modelData.opex.marketingExpenses}
              onChange={(e) => handleParameterChange('opex', 'marketingExpenses', parseFloat(e.target.value))}
              className="parameter-input"
            />
          </div>
        </div>
      </div>
    </div>
  )

  const renderCostSalesView = () => (
    <div className="cost-sales-view">
      <div className="view-header">
        <h3>💰 Cost & Sales Definition</h3>
        <button className="btn btn-primary" onClick={addProduct}>
          ➕ Add Product
        </button>
      </div>

      <div className="products-table">
        <table className="financial-table">
          <thead>
            <tr>
              <th>Product Name</th>
              <th>Unit Cost</th>
              <th>Sales Price</th>
              <th>Year 1 Volume</th>
              <th>Year 2 Volume</th>
              <th>Year 3 Volume</th>
              <th>Year 4 Volume</th>
              <th>Year 5 Volume</th>
              <th>Total Sales</th>
              <th>Total Cost</th>
              <th>Gross Margin</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {modelData.costSales.products.map((product) => (
              <tr key={product.id}>
                <td>
                  <input
                    type="text"
                    value={product.name}
                    onChange={(e) => handleProductUpdate(product.id, 'name', e.target.value)}
                    className="table-input"
                  />
                </td>
                <td>
                  <input
                    type="number"
                    value={product.unitCost}
                    onChange={(e) => handleProductUpdate(product.id, 'unitCost', parseFloat(e.target.value))}
                    className="table-input"
                  />
                </td>
                <td>
                  <input
                    type="number"
                    value={product.salesPrice}
                    onChange={(e) => handleProductUpdate(product.id, 'salesPrice', parseFloat(e.target.value))}
                    className="table-input"
                  />
                </td>
                {product.volume.map((vol, idx) => (
                  <td key={idx}>
                    <input
                      type="number"
                      value={vol}
                      onChange={(e) => {
                        const newVolume = [...product.volume]
                        newVolume[idx] = parseFloat(e.target.value)
                        handleProductUpdate(product.id, 'volume', newVolume)
                      }}
                      className="table-input"
                    />
                  </td>
                ))}
                <td className="calculated-cell">
                  {formatCurrency(product.totalSales?.reduce((sum, sales) => sum + sales, 0) || 0)}
                </td>
                <td className="calculated-cell">
                  {formatCurrency(product.totalCost?.reduce((sum, cost) => sum + cost, 0) || 0)}
                </td>
                <td className="calculated-cell">
                  {formatCurrency(product.grossMargin?.reduce((sum, margin) => sum + margin, 0) || 0)}
                </td>
                <td>
                  <button
                    className="btn btn-sm btn-danger"
                    onClick={() => deleteProduct(product.id)}
                  >
                    🗑️
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
          <tfoot>
            <tr className="totals-row">
              <td colSpan="7"><strong>TOTALS</strong></td>
              <td className="total-cell">
                <strong>{formatCurrency(modelData.costSales.totalRevenue?.reduce((sum, rev) => sum + rev, 0) || 0)}</strong>
              </td>
              <td className="total-cell">
                <strong>{formatCurrency(modelData.costSales.totalCosts?.reduce((sum, cost) => sum + cost, 0) || 0)}</strong>
              </td>
              <td className="total-cell">
                <strong>{formatCurrency(modelData.costSales.grossProfit?.reduce((sum, profit) => sum + profit, 0) || 0)}</strong>
              </td>
              <td></td>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>
  )

  return (
    <div className="advanced-financial-modeling">
      <div className="modeling-header">
        <h2>🎯 Advanced Financial Modeling System</h2>
        <div className="calculation-status">
          {isCalculating ? (
            <span className="calculating">🔄 Calculating...</span>
          ) : (
            <span className="updated">✅ Updated</span>
          )}
        </div>
      </div>

      <div className="modeling-navigation">
        {views.map((view) => (
          <button
            key={view.id}
            className={`nav-btn ${activeView === view.id ? 'active' : ''}`}
            onClick={() => setActiveView(view.id)}
          >
            <span className="nav-icon">{view.icon}</span>
            <span className="nav-label">{view.label}</span>
          </button>
        ))}
      </div>

      <div className="modeling-content">
        {activeView === 'parameters' && renderParametersView()}
        {activeView === 'costsales' && renderCostSalesView()}
        {activeView === 'sensitivity' && renderSensitivityView()}
        {activeView === 'indexes' && renderIndexesView()}
        {activeView === 'cashflow' && renderCashFlowView()}
        {activeView === 'audit' && renderAuditView()}
      </div>
    </div>
  )
}

export default AdvancedFinancialModeling
