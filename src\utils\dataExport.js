// Data Export Utilities for Business Case Analysis

export class DataExporter {
  constructor() {
    this.formatCurrency = (value) => {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(value);
    };

    this.formatPercentage = (value) => {
      return `${value.toFixed(1)}%`;
    };
  }

  // Export to CSV
  exportToCSV(data, filename = 'business_case_analysis') {
    const csvContent = this.generateCSVContent(data);
    this.downloadFile(csvContent, `${filename}.csv`, 'text/csv');
  }

  // Export to JSON
  exportToJSON(data, filename = 'business_case_analysis') {
    const jsonContent = JSON.stringify(data, null, 2);
    this.downloadFile(jsonContent, `${filename}.json`, 'application/json');
  }

  // Export to Excel-compatible format
  exportToExcel(data, filename = 'business_case_analysis') {
    const excelContent = this.generateExcelContent(data);
    this.downloadFile(excelContent, `${filename}.xlsx`, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
  }

  // Generate comprehensive PDF report
  exportToPDF(data, filename = 'business_case_report') {
    const htmlContent = this.generatePDFContent(data);
    
    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    printWindow.document.write(htmlContent);
    printWindow.document.close();
    
    // Trigger print dialog
    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 250);
  }

  // Generate CSV content
  generateCSVContent(data) {
    let csv = '';
    
    // Financial Metrics Summary
    csv += 'FINANCIAL METRICS SUMMARY\n';
    csv += 'Metric,Value\n';
    csv += `Net Present Value,${this.formatCurrency(data.results.npv)}\n`;
    csv += `Internal Rate of Return,${this.formatPercentage(data.results.irr)}\n`;
    csv += `Payback Period,${data.results.payback?.toFixed(1) || 'N/A'} years\n`;
    csv += `Yield Index,${data.results.yieldIndex?.toFixed(2) || 'N/A'}\n`;
    csv += `Gross Margin,${this.formatPercentage(data.results.grossMargin)}\n`;
    csv += '\n';

    // Yearly Cash Flow Data
    csv += 'YEARLY CASH FLOW ANALYSIS\n';
    csv += 'Year,Revenue,Costs,Cash Flow,Cumulative Cash Flow\n';
    
    if (data.results.yearlyData) {
      data.results.yearlyData.forEach(year => {
        csv += `${year.year},${this.formatCurrency(year.revenue)},${this.formatCurrency(year.costs)},${this.formatCurrency(year.cashFlow)},${this.formatCurrency(year.cumulative)}\n`;
      });
    }
    csv += '\n';

    // Parameters
    csv += 'PROJECT PARAMETERS\n';
    csv += 'Parameter,Value\n';
    csv += `Discount Rate,${data.parameters.discountRate}%\n`;
    csv += `Tax Rate,${data.parameters.taxRate}%\n`;
    csv += `Inflation Rate,${data.parameters.inflationRate}%\n`;
    csv += `Project Duration,${data.parameters.projectDuration} years\n`;
    csv += `Base Currency,${data.parameters.baseCurrency}\n`;
    csv += '\n';

    // Sensitivity Analysis
    if (data.sensitivity && data.sensitivityResults) {
      csv += 'SENSITIVITY ANALYSIS\n';
      csv += 'Scenario,NPV,IRR,Payback Period\n';
      csv += `Best Case,${this.formatCurrency(data.sensitivityResults.best.npv)},${this.formatPercentage(data.sensitivityResults.best.irr)},${data.sensitivityResults.best.payback?.toFixed(1) || 'N/A'} years\n`;
      csv += `Base Case,${this.formatCurrency(data.sensitivityResults.base.npv)},${this.formatPercentage(data.sensitivityResults.base.irr)},${data.sensitivityResults.base.payback?.toFixed(1) || 'N/A'} years\n`;
      csv += `Worst Case,${this.formatCurrency(data.sensitivityResults.worst.npv)},${this.formatPercentage(data.sensitivityResults.worst.irr)},${data.sensitivityResults.worst.payback?.toFixed(1) || 'N/A'} years\n`;
    }

    return csv;
  }

  // Generate Excel-compatible content (CSV with tabs)
  generateExcelContent(data) {
    // For now, return CSV content - in a real implementation, you'd use a library like SheetJS
    return this.generateCSVContent(data);
  }

  // Generate PDF content (HTML for printing)
  generatePDFContent(data) {
    return `
<!DOCTYPE html>
<html>
<head>
    <title>Business Case Analysis Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            color: #333;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #667eea;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            page-break-inside: avoid;
        }
        .section h2 {
            color: #667eea;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 20px 0;
        }
        .metric-box {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }
        .metric-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .positive { color: #28a745; }
        .negative { color: #dc3545; }
        .neutral { color: #6c757d; }
        @media print {
            .no-print { display: none; }
            body { margin: 0; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 Business Case Analysis Report</h1>
        <p>Generated on ${new Date().toLocaleDateString()}</p>
        <p>Project: ${data.projectName || 'New Product Launch'}</p>
    </div>

    <div class="section">
        <h2>Executive Summary</h2>
        <div class="metrics-grid">
            <div class="metric-box">
                <div class="metric-value ${data.results.npv > 0 ? 'positive' : 'negative'}">
                    ${this.formatCurrency(data.results.npv)}
                </div>
                <div class="metric-label">Net Present Value</div>
            </div>
            <div class="metric-box">
                <div class="metric-value ${data.results.irr > 10 ? 'positive' : 'neutral'}">
                    ${this.formatPercentage(data.results.irr)}
                </div>
                <div class="metric-label">Internal Rate of Return</div>
            </div>
            <div class="metric-box">
                <div class="metric-value ${data.results.payback < 3 ? 'positive' : 'neutral'}">
                    ${data.results.payback?.toFixed(1) || 'N/A'} years
                </div>
                <div class="metric-label">Payback Period</div>
            </div>
            <div class="metric-box">
                <div class="metric-value ${data.results.yieldIndex > 1.5 ? 'positive' : 'neutral'}">
                    ${data.results.yieldIndex?.toFixed(2) || 'N/A'}
                </div>
                <div class="metric-label">Yield Index</div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>Financial Analysis</h2>
        <table>
            <thead>
                <tr>
                    <th>Year</th>
                    <th>Revenue</th>
                    <th>Costs</th>
                    <th>Cash Flow</th>
                    <th>Cumulative</th>
                </tr>
            </thead>
            <tbody>
                ${data.results.yearlyData?.map(year => `
                    <tr>
                        <td>${year.year}</td>
                        <td>${this.formatCurrency(year.revenue)}</td>
                        <td>${this.formatCurrency(year.costs)}</td>
                        <td class="${year.cashFlow >= 0 ? 'positive' : 'negative'}">
                            ${this.formatCurrency(year.cashFlow)}
                        </td>
                        <td class="${year.cumulative >= 0 ? 'positive' : 'negative'}">
                            ${this.formatCurrency(year.cumulative)}
                        </td>
                    </tr>
                `).join('') || ''}
            </tbody>
        </table>
    </div>

    ${data.sensitivityResults ? `
    <div class="section">
        <h2>Sensitivity Analysis</h2>
        <table>
            <thead>
                <tr>
                    <th>Scenario</th>
                    <th>NPV</th>
                    <th>IRR</th>
                    <th>Payback Period</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>🟢 Best Case</td>
                    <td class="positive">${this.formatCurrency(data.sensitivityResults.best.npv)}</td>
                    <td class="positive">${this.formatPercentage(data.sensitivityResults.best.irr)}</td>
                    <td>${data.sensitivityResults.best.payback?.toFixed(1) || 'N/A'} years</td>
                </tr>
                <tr>
                    <td>🟡 Base Case</td>
                    <td class="neutral">${this.formatCurrency(data.sensitivityResults.base.npv)}</td>
                    <td class="neutral">${this.formatPercentage(data.sensitivityResults.base.irr)}</td>
                    <td>${data.sensitivityResults.base.payback?.toFixed(1) || 'N/A'} years</td>
                </tr>
                <tr>
                    <td>🔴 Worst Case</td>
                    <td class="negative">${this.formatCurrency(data.sensitivityResults.worst.npv)}</td>
                    <td class="negative">${this.formatPercentage(data.sensitivityResults.worst.irr)}</td>
                    <td>${data.sensitivityResults.worst.payback?.toFixed(1) || 'N/A'} years</td>
                </tr>
            </tbody>
        </table>
    </div>
    ` : ''}

    <div class="section">
        <h2>Project Parameters</h2>
        <table>
            <tbody>
                <tr><td><strong>Discount Rate</strong></td><td>${data.parameters.discountRate}%</td></tr>
                <tr><td><strong>Tax Rate</strong></td><td>${data.parameters.taxRate}%</td></tr>
                <tr><td><strong>Inflation Rate</strong></td><td>${data.parameters.inflationRate}%</td></tr>
                <tr><td><strong>Project Duration</strong></td><td>${data.parameters.projectDuration} years</td></tr>
                <tr><td><strong>Base Currency</strong></td><td>${data.parameters.baseCurrency}</td></tr>
            </tbody>
        </table>
    </div>

    <div class="section">
        <h2>Risk Assessment</h2>
        <p><strong>Investment Recommendation:</strong> 
            ${data.results.npv > 0 && data.results.irr > 10 ? 
                '<span class="positive">RECOMMENDED</span> - Strong financial metrics indicate a viable investment.' :
                '<span class="negative">NOT RECOMMENDED</span> - Financial metrics do not meet investment criteria.'
            }
        </p>
        <p><strong>Key Risks:</strong></p>
        <ul>
            ${data.results.payback > 5 ? '<li>Long payback period increases investment risk</li>' : ''}
            ${data.results.irr < 15 ? '<li>IRR below target threshold</li>' : ''}
            ${data.results.yieldIndex < 1.2 ? '<li>Low yield index indicates marginal profitability</li>' : ''}
        </ul>
    </div>
</body>
</html>`;
  }

  // Download file helper
  downloadFile(content, filename, mimeType) {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.style.display = 'none';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
  }

  // Generate summary report for quick sharing
  generateSummaryReport(data) {
    return {
      projectName: data.projectName || 'Business Case Analysis',
      generatedDate: new Date().toISOString(),
      recommendation: data.results.npv > 0 && data.results.irr > 10 ? 'RECOMMENDED' : 'NOT RECOMMENDED',
      keyMetrics: {
        npv: data.results.npv,
        irr: data.results.irr,
        payback: data.results.payback,
        yieldIndex: data.results.yieldIndex
      },
      riskFactors: this.identifyRiskFactors(data.results),
      totalInvestment: data.results.yearlyData?.[0]?.costs || 0,
      projectedRevenue: data.results.totalRevenue
    };
  }

  // Identify risk factors
  identifyRiskFactors(results) {
    const risks = [];
    
    if (results.payback > 5) {
      risks.push('Long payback period (>5 years)');
    }
    
    if (results.irr < 15) {
      risks.push('IRR below target threshold (15%)');
    }
    
    if (results.yieldIndex < 1.2) {
      risks.push('Low profitability index (<1.2)');
    }
    
    if (results.npv < 500000) {
      risks.push('Low absolute NPV value');
    }
    
    return risks;
  }
}

export default DataExporter;
