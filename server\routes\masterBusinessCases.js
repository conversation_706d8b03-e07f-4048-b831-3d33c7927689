const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const MasterBusinessCase = require('../models/MasterBusinessCase');

// Validation middleware
const validateMasterBusinessCase = [
  body('programInfo.programName')
    .trim()
    .isLength({ min: 3, max: 200 })
    .withMessage('Program name must be between 3 and 200 characters'),
  body('programInfo.programManager')
    .trim()
    .notEmpty()
    .withMessage('Program manager is required'),
  body('programInfo.sponsor')
    .trim()
    .notEmpty()
    .withMessage('Executive sponsor is required'),
  body('programInfo.totalBudget')
    .isNumeric()
    .isFloat({ min: 0 })
    .withMessage('Total budget must be a positive number'),
  body('programInfo.duration')
    .isInt({ min: 1 })
    .withMessage('Duration must be at least 1 month'),
  body('createdBy')
    .trim()
    .notEmpty()
    .withMessage('Created by is required')
];

// @route   GET /api/master-business-cases
// @desc    Get all master business cases with pagination and filtering
// @access  Public (should be protected in production)
router.get('/', async (req, res) => {
  try {


    
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build filter object
    const filter = {};

    if (req.query.status) {
      filter.status = req.query.status;
    }

    if (req.query.businessDriver) {
      filter['programInfo.businessDriver'] = req.query.businessDriver;
    }

    if (req.query.investmentCategory) {
      filter['programInfo.investmentCategory'] = req.query.investmentCategory;
    }

    if (req.query.createdBy) {
      filter.createdBy = req.query.createdBy;
    }

    if (req.query.search) {
      filter.$text = { $search: req.query.search };
    }

    // Sort options
    const sortOptions = {};
    if (req.query.sortBy) {
      const sortField = req.query.sortBy;
      const sortOrder = req.query.sortOrder === 'desc' ? -1 : 1;
      sortOptions[sortField] = sortOrder;
    } else {
      sortOptions.createdAt = -1; // Default sort by creation date
    }

    const masterBusinessCases = await MasterBusinessCase.find(filter)
      .sort(sortOptions)
      .skip(skip)
      .limit(limit)
      .lean();

    const total = await MasterBusinessCase.countDocuments(filter);

    res.json({
      success: true,
      data: masterBusinessCases,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching master business cases:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching master business cases',
      error: error.message
    });
  }
});

// @route   GET /api/master-business-cases/:id
// @desc    Get single master business case by ID
// @access  Public (should be protected in production)
router.get('/:id', async (req, res) => {
  try {
    const masterBusinessCase = await MasterBusinessCase.findById(req.params.id);

    if (!masterBusinessCase) {
      return res.status(404).json({
        success: false,
        message: 'Master business case not found'
      });
    }

    res.json({
      success: true,
      data: masterBusinessCase
    });
  } catch (error) {
    console.error('Error fetching master business case:', error);

    if (error.name === 'CastError') {
      return res.status(400).json({
        success: false,
        message: 'Invalid master business case ID'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Server error while fetching master business case',
      error: error.message
    });
  }
});

// @route   POST /api/master-business-cases
// @desc    Create new master business case
// @access  Public (should be protected in production)
router.post('/', validateMasterBusinessCase, async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    // Create new master business case
    const masterBusinessCase = new MasterBusinessCase(req.body);

    // Generate default use case IDs if not provided
    if (masterBusinessCase.useCases && masterBusinessCase.useCases.length > 0) {
      masterBusinessCase.useCases.forEach((useCase, index) => {
        if (!useCase.id) {
          useCase.id = `UC${String(index + 1).padStart(3, '0')}`;
        }
      });
    }

    // Generate default milestone IDs if not provided
    if (masterBusinessCase.milestones && masterBusinessCase.milestones.length > 0) {
      masterBusinessCase.milestones.forEach((milestone, index) => {
        if (!milestone.id) {
          milestone.id = `MS${String(index + 1).padStart(3, '0')}`;
        }
      });
    }

    await masterBusinessCase.save();

    res.status(201).json({
      success: true,
      message: 'Master business case created successfully',
      data: masterBusinessCase
    });
  } catch (error) {
    console.error('Error creating master business case:', error);

    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => ({
        field: err.path,
        message: err.message
      }));

      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: validationErrors
      });
    }

    res.status(500).json({
      success: false,
      message: 'Server error while creating master business case',
      error: error.message
    });
  }
});

// @route   PUT /api/master-business-cases/:id
// @desc    Update master business case
// @access  Public (should be protected in production)
router.put('/:id', validateMasterBusinessCase, async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const masterBusinessCase = await MasterBusinessCase.findById(req.params.id);

    if (!masterBusinessCase) {
      return res.status(404).json({
        success: false,
        message: 'Master business case not found'
      });
    }

    // Update fields
    Object.assign(masterBusinessCase, req.body);
    masterBusinessCase.lastModifiedBy = req.body.lastModifiedBy || req.body.createdBy;

    await masterBusinessCase.save();

    res.json({
      success: true,
      message: 'Master business case updated successfully',
      data: masterBusinessCase
    });
  } catch (error) {
    console.error('Error updating master business case:', error);

    if (error.name === 'CastError') {
      return res.status(400).json({
        success: false,
        message: 'Invalid master business case ID'
      });
    }

    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => ({
        field: err.path,
        message: err.message
      }));

      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: validationErrors
      });
    }

    res.status(500).json({
      success: false,
      message: 'Server error while updating master business case',
      error: error.message
    });
  }
});

// @route   DELETE /api/master-business-cases/:id
// @desc    Delete master business case
// @access  Public (should be protected in production)
router.delete('/:id', async (req, res) => {
  try {
    const masterBusinessCase = await MasterBusinessCase.findById(req.params.id);

    if (!masterBusinessCase) {
      return res.status(404).json({
        success: false,
        message: 'Master business case not found'
      });
    }

    await MasterBusinessCase.findByIdAndDelete(req.params.id);

    res.json({
      success: true,
      message: 'Master business case deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting master business case:', error);

    if (error.name === 'CastError') {
      return res.status(400).json({
        success: false,
        message: 'Invalid master business case ID'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Server error while deleting master business case',
      error: error.message
    });
  }
});

// @route   PATCH /api/master-business-cases/:id/status
// @desc    Update master business case status
// @access  Public (should be protected in production)
router.patch('/:id/status', async (req, res) => {
  try {
    const { status, updatedBy } = req.body;

    if (!status) {
      return res.status(400).json({
        success: false,
        message: 'Status is required'
      });
    }

    const validStatuses = ['Draft', 'Under Review', 'Approved', 'Active', 'On Hold', 'Completed', 'Cancelled'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status value'
      });
    }

    const masterBusinessCase = await MasterBusinessCase.findByIdAndUpdate(
      req.params.id,
      {
        status,
        lastModifiedBy: updatedBy
      },
      { new: true }
    );

    if (!masterBusinessCase) {
      return res.status(404).json({
        success: false,
        message: 'Master business case not found'
      });
    }

    res.json({
      success: true,
      message: 'Status updated successfully',
      data: masterBusinessCase
    });
  } catch (error) {
    console.error('Error updating status:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating status',
      error: error.message
    });
  }
});

// @route   GET /api/master-business-cases/stats/summary
// @desc    Get summary statistics
// @access  Public (should be protected in production)
router.get('/stats/summary', async (req, res) => {
  try {
    const stats = await MasterBusinessCase.aggregate([
      {
        $group: {
          _id: null,
          totalCases: { $sum: 1 },
          totalBudget: { $sum: '$programInfo.totalBudget' },
          avgBudget: { $avg: '$programInfo.totalBudget' },
          avgDuration: { $avg: '$programInfo.duration' }
        }
      }
    ]);

    const statusStats = await MasterBusinessCase.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    const businessDriverStats = await MasterBusinessCase.aggregate([
      {
        $group: {
          _id: '$programInfo.businessDriver',
          count: { $sum: 1 }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        summary: stats[0] || {
          totalCases: 0,
          totalBudget: 0,
          avgBudget: 0,
          avgDuration: 0
        },
        statusDistribution: statusStats,
        businessDriverDistribution: businessDriverStats
      }
    });
  } catch (error) {
    console.error('Error fetching statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching statistics',
      error: error.message
    });
  }
});

module.exports = router;
