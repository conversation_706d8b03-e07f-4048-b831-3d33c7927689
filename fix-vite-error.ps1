# PowerShell script to fix Vite error
Write-Host "🔧 Fixing Vite Error - Master BC Framework Setup" -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan
Write-Host ""

# Check Node.js installation
Write-Host "📋 Step 1: Checking Node.js installation..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    $npmVersion = npm --version
    Write-Host "✅ Node.js version: $nodeVersion" -ForegroundColor Green
    Write-Host "✅ npm version: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Node.js from https://nodejs.org/" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Clean previous installation
Write-Host "📋 Step 2: Cleaning previous installation..." -ForegroundColor Yellow
if (Test-Path "node_modules") {
    Write-Host "Removing old node_modules..." -ForegroundColor Yellow
    Remove-Item -Recurse -Force "node_modules"
}

if (Test-Path "package-lock.json") {
    Write-Host "Removing package-lock.json..." -ForegroundColor Yellow
    Remove-Item "package-lock.json"
}

Write-Host ""

# Install dependencies
Write-Host "📋 Step 3: Installing dependencies..." -ForegroundColor Yellow
Write-Host "This may take a few minutes..." -ForegroundColor Yellow

try {
    npm install
    Write-Host "✅ Dependencies installed successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 Trying alternative installation methods..." -ForegroundColor Yellow
    
    try {
        Write-Host "Method 1: Clear npm cache and retry..." -ForegroundColor Yellow
        npm cache clean --force
        npm install
        Write-Host "✅ Installation successful with cache clean!" -ForegroundColor Green
    } catch {
        try {
            Write-Host "Method 2: Install with legacy peer deps..." -ForegroundColor Yellow
            npm install --legacy-peer-deps
            Write-Host "✅ Installation successful with legacy peer deps!" -ForegroundColor Green
        } catch {
            try {
                Write-Host "Method 3: Install with force..." -ForegroundColor Yellow
                npm install --force
                Write-Host "✅ Installation successful with force!" -ForegroundColor Green
            } catch {
                Write-Host "❌ All installation methods failed" -ForegroundColor Red
                Write-Host ""
                Write-Host "💡 Manual steps to try:" -ForegroundColor Yellow
                Write-Host "1. Delete node_modules folder manually" -ForegroundColor White
                Write-Host "2. Run: npm cache clean --force" -ForegroundColor White
                Write-Host "3. Run: npm install --verbose" -ForegroundColor White
                Write-Host "4. Check your internet connection" -ForegroundColor White
                Write-Host "5. Try using yarn instead: npm install -g yarn, then yarn install" -ForegroundColor White
                Read-Host "Press Enter to exit"
                exit 1
            }
        }
    }
}

Write-Host ""

# Verify Vite installation
Write-Host "📋 Step 4: Verifying Vite installation..." -ForegroundColor Yellow
if (Test-Path "node_modules\.bin\vite.cmd") {
    Write-Host "✅ Vite is installed correctly" -ForegroundColor Green
} else {
    Write-Host "❌ Vite not found, trying to install manually..." -ForegroundColor Red
    npm install vite@latest --save-dev
}

Write-Host ""

# Test development server
Write-Host "📋 Step 5: Starting development server..." -ForegroundColor Yellow
Write-Host "This may take a moment..." -ForegroundColor Yellow
Write-Host ""

Start-Process -NoNewWindow npm -ArgumentList "run", "dev"

Start-Sleep -Seconds 5

Write-Host ""
Write-Host "🎯 Setup Complete!" -ForegroundColor Green
Write-Host ""
Write-Host "📊 Your Master BC Framework should now be running at:" -ForegroundColor Cyan
Write-Host "   http://localhost:3000" -ForegroundColor White
Write-Host ""
Write-Host "🚀 If the browser doesn't open automatically, manually navigate to:" -ForegroundColor Yellow
Write-Host "   http://localhost:3000" -ForegroundColor White
Write-Host ""
Write-Host "💡 To stop the server, press Ctrl+C in the terminal" -ForegroundColor Yellow
Write-Host ""
Write-Host "🎯 Available Features:" -ForegroundColor Cyan
Write-Host "   ✅ Business Case Framework (6-step process)" -ForegroundColor Green
Write-Host "   ✅ Advanced Financial Modeling" -ForegroundColor Green
Write-Host "   ✅ Excel Analysis & Augment AI" -ForegroundColor Green
Write-Host "   ✅ PMI/PGMP Compliance Tracking" -ForegroundColor Green
Write-Host "   ✅ Multi-Stakeholder Management" -ForegroundColor Green
Write-Host "   ✅ Change Management & Roadmap" -ForegroundColor Green
Write-Host ""

Read-Host "Press Enter to continue"
