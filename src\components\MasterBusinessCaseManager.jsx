import React, { useState, useEffect } from 'react'
import masterBusinessCaseService from '../services/api'

const MasterBusinessCaseManager = () => {
  const [businessCases, setBusinessCases] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [selectedCase, setSelectedCase] = useState(null)
  const [isEditing, setIsEditing] = useState(false)
  const [isCreating, setIsCreating] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('')
  const [filterBusinessDriver, setFilterBusinessDriver] = useState('')
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  })

  // Form state for creating/editing
  const [formData, setFormData] = useState({
    programInfo: {
      programName: '',
      programManager: '',
      sponsor: '',
      strategicAlignment: 'Medium',
      businessDriver: 'Market Expansion',
      investmentCategory: 'Strategic',
      totalBudget: 0,
      duration: 12,
      startDate: '',
      endDate: '',
      currency: 'USD'
    },
    governance: {
      steeringCommittee: [],
      decisionGates: [],
      riskTolerance: 'Medium',
      complianceFramework: ['PMI']
    },
    useCases: [],
    milestones: [],
    status: 'Draft',
    createdBy: 'Current User',
    tags: []
  })

  // Load business cases on component mount
  useEffect(() => {
    loadBusinessCases()
  }, [pagination.page, filterStatus, filterBusinessDriver])

  // Search effect
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (searchTerm) {
        searchBusinessCases()
      } else {
        loadBusinessCases()
      }
    }, 500)

    return () => clearTimeout(delayedSearch)
  }, [searchTerm])

  const loadBusinessCases = async () => {
    try {
      setLoading(true)
      const params = {
        page: pagination.page,
        limit: pagination.limit,
        ...(filterStatus && { status: filterStatus }),
        ...(filterBusinessDriver && { businessDriver: filterBusinessDriver })
      }

      const response = await masterBusinessCaseService.getAll(params)
      
      if (response.success) {
        setBusinessCases(response.data)
        setPagination(response.pagination)
      } else {
        setError('Failed to load business cases')
      }
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const searchBusinessCases = async () => {
    try {
      setLoading(true)
      const response = await masterBusinessCaseService.search(searchTerm, {
        status: filterStatus,
        businessDriver: filterBusinessDriver
      })
      
      if (response.success) {
        setBusinessCases(response.data)
        setPagination(response.pagination)
      }
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const handleCreate = async () => {
    try {
      const validation = masterBusinessCaseService.validateData(formData)
      if (!validation.isValid) {
        setError(validation.errors.join(', '))
        return
      }

      const response = await masterBusinessCaseService.create(formData)
      
      if (response.success) {
        setIsCreating(false)
        resetForm()
        loadBusinessCases()
        setError(null)
      }
    } catch (err) {
      setError(err.message)
    }
  }

  const handleUpdate = async () => {
    try {
      if (!selectedCase) return

      const validation = masterBusinessCaseService.validateData(formData)
      if (!validation.isValid) {
        setError(validation.errors.join(', '))
        return
      }

      const response = await masterBusinessCaseService.update(selectedCase._id, {
        ...formData,
        lastModifiedBy: 'Current User'
      })
      
      if (response.success) {
        setIsEditing(false)
        setSelectedCase(null)
        resetForm()
        loadBusinessCases()
        setError(null)
      }
    } catch (err) {
      setError(err.message)
    }
  }

  const handleDelete = async (id) => {
    if (!window.confirm('Are you sure you want to delete this business case?')) {
      return
    }

    try {
      const response = await masterBusinessCaseService.delete(id)
      
      if (response.success) {
        loadBusinessCases()
        setError(null)
      }
    } catch (err) {
      setError(err.message)
    }
  }

  const handleStatusUpdate = async (id, newStatus) => {
    try {
      const response = await masterBusinessCaseService.updateStatus(id, newStatus, 'Current User')
      
      if (response.success) {
        loadBusinessCases()
        setError(null)
      }
    } catch (err) {
      setError(err.message)
    }
  }

  const handleDuplicate = async (id, originalName) => {
    try {
      const newName = prompt('Enter name for the duplicated business case:', `${originalName} (Copy)`)
      if (!newName) return

      const response = await masterBusinessCaseService.duplicate(id, newName)
      
      if (response.success) {
        loadBusinessCases()
        setError(null)
      }
    } catch (err) {
      setError(err.message)
    }
  }

  const handleEdit = (businessCase) => {
    setSelectedCase(businessCase)
    setFormData(businessCase)
    setIsEditing(true)
  }

  const resetForm = () => {
    setFormData({
      programInfo: {
        programName: '',
        programManager: '',
        sponsor: '',
        strategicAlignment: 'Medium',
        businessDriver: 'Market Expansion',
        investmentCategory: 'Strategic',
        totalBudget: 0,
        duration: 12,
        startDate: '',
        endDate: '',
        currency: 'USD'
      },
      governance: {
        steeringCommittee: [],
        decisionGates: [],
        riskTolerance: 'Medium',
        complianceFramework: ['PMI']
      },
      useCases: [],
      milestones: [],
      status: 'Draft',
      createdBy: 'Current User',
      tags: []
    })
  }

  const handleFormChange = (section, field, value) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }))
  }

  const getStatusColor = (status) => {
    const colors = {
      'Draft': '#6c757d',
      'Under Review': '#ffc107',
      'Approved': '#28a745',
      'Active': '#17a2b8',
      'On Hold': '#fd7e14',
      'Completed': '#28a745',
      'Cancelled': '#dc3545'
    }
    return colors[status] || '#6c757d'
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  if (loading && businessCases.length === 0) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading Master Business Cases...</p>
      </div>
    )
  }

  return (
    <div className="master-business-case-manager">
      <div className="manager-header">
        <h1>🎯 Master Business Case Management</h1>
        <div className="header-actions">
          <button 
            className="btn btn-primary"
            onClick={() => setIsCreating(true)}
          >
            + Create New Business Case
          </button>
        </div>
      </div>

      {error && (
        <div className="error-message">
          <span>❌ {error}</span>
          <button onClick={() => setError(null)}>×</button>
        </div>
      )}

      {/* Search and Filters */}
      <div className="search-filters">
        <div className="search-box">
          <input
            type="text"
            placeholder="Search business cases..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="form-input"
          />
        </div>
        
        <div className="filters">
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="form-input"
          >
            <option value="">All Statuses</option>
            <option value="Draft">Draft</option>
            <option value="Under Review">Under Review</option>
            <option value="Approved">Approved</option>
            <option value="Active">Active</option>
            <option value="On Hold">On Hold</option>
            <option value="Completed">Completed</option>
            <option value="Cancelled">Cancelled</option>
          </select>

          <select
            value={filterBusinessDriver}
            onChange={(e) => setFilterBusinessDriver(e.target.value)}
            className="form-input"
          >
            <option value="">All Business Drivers</option>
            <option value="Market Expansion">Market Expansion</option>
            <option value="Cost Reduction">Cost Reduction</option>
            <option value="Regulatory Compliance">Regulatory Compliance</option>
            <option value="Digital Transformation">Digital Transformation</option>
            <option value="Customer Experience">Customer Experience</option>
            <option value="Innovation">Innovation</option>
            <option value="Risk Mitigation">Risk Mitigation</option>
          </select>
        </div>
      </div>

      {/* Business Cases List */}
      <div className="business-cases-grid">
        {businessCases.map((businessCase) => (
          <div key={businessCase._id} className="business-case-card">
            <div className="card-header">
              <h3>{businessCase.programInfo.programName}</h3>
              <div className="card-actions">
                <span 
                  className="status-badge"
                  style={{ backgroundColor: getStatusColor(businessCase.status) }}
                >
                  {businessCase.status}
                </span>
              </div>
            </div>

            <div className="card-content">
              <div className="info-row">
                <span className="label">Program Manager:</span>
                <span>{businessCase.programInfo.programManager}</span>
              </div>
              <div className="info-row">
                <span className="label">Sponsor:</span>
                <span>{businessCase.programInfo.sponsor}</span>
              </div>
              <div className="info-row">
                <span className="label">Business Driver:</span>
                <span>{businessCase.programInfo.businessDriver}</span>
              </div>
              <div className="info-row">
                <span className="label">Total Budget:</span>
                <span>{formatCurrency(businessCase.programInfo.totalBudget)}</span>
              </div>
              <div className="info-row">
                <span className="label">Duration:</span>
                <span>{businessCase.programInfo.duration} months</span>
              </div>
              <div className="info-row">
                <span className="label">Use Cases:</span>
                <span>{businessCase.useCases?.length || 0}</span>
              </div>
              <div className="info-row">
                <span className="label">Created:</span>
                <span>{new Date(businessCase.createdAt).toLocaleDateString()}</span>
              </div>
            </div>

            <div className="card-actions-footer">
              <button 
                className="btn btn-sm btn-secondary"
                onClick={() => handleEdit(businessCase)}
              >
                ✏️ Edit
              </button>
              <button 
                className="btn btn-sm btn-info"
                onClick={() => handleDuplicate(businessCase._id, businessCase.programInfo.programName)}
              >
                📋 Duplicate
              </button>
              <select
                value={businessCase.status}
                onChange={(e) => handleStatusUpdate(businessCase._id, e.target.value)}
                className="status-select"
              >
                <option value="Draft">Draft</option>
                <option value="Under Review">Under Review</option>
                <option value="Approved">Approved</option>
                <option value="Active">Active</option>
                <option value="On Hold">On Hold</option>
                <option value="Completed">Completed</option>
                <option value="Cancelled">Cancelled</option>
              </select>
              <button 
                className="btn btn-sm btn-danger"
                onClick={() => handleDelete(businessCase._id)}
              >
                🗑️ Delete
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      {pagination.pages > 1 && (
        <div className="pagination">
          <button 
            className="btn btn-secondary"
            disabled={pagination.page === 1}
            onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
          >
            Previous
          </button>
          
          <span className="pagination-info">
            Page {pagination.page} of {pagination.pages} ({pagination.total} total)
          </span>
          
          <button 
            className="btn btn-secondary"
            disabled={pagination.page === pagination.pages}
            onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
          >
            Next
          </button>
        </div>
      )}

      {/* Create/Edit Modal */}
      {(isCreating || isEditing) && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h2>{isCreating ? 'Create New Business Case' : 'Edit Business Case'}</h2>
              <button 
                className="modal-close"
                onClick={() => {
                  setIsCreating(false)
                  setIsEditing(false)
                  setSelectedCase(null)
                  resetForm()
                }}
              >
                ×
              </button>
            </div>

            <div className="modal-body">
              <div className="form-section">
                <h3>Program Information</h3>
                <div className="form-row">
                  <div className="form-group">
                    <label>Program Name *</label>
                    <input
                      type="text"
                      value={formData.programInfo.programName}
                      onChange={(e) => handleFormChange('programInfo', 'programName', e.target.value)}
                      className="form-input"
                      required
                    />
                  </div>
                  <div className="form-group">
                    <label>Program Manager *</label>
                    <input
                      type="text"
                      value={formData.programInfo.programManager}
                      onChange={(e) => handleFormChange('programInfo', 'programManager', e.target.value)}
                      className="form-input"
                      required
                    />
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label>Executive Sponsor *</label>
                    <input
                      type="text"
                      value={formData.programInfo.sponsor}
                      onChange={(e) => handleFormChange('programInfo', 'sponsor', e.target.value)}
                      className="form-input"
                      required
                    />
                  </div>
                  <div className="form-group">
                    <label>Strategic Alignment</label>
                    <select
                      value={formData.programInfo.strategicAlignment}
                      onChange={(e) => handleFormChange('programInfo', 'strategicAlignment', e.target.value)}
                      className="form-input"
                    >
                      <option value="High">High</option>
                      <option value="Medium">Medium</option>
                      <option value="Low">Low</option>
                    </select>
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label>Business Driver</label>
                    <select
                      value={formData.programInfo.businessDriver}
                      onChange={(e) => handleFormChange('programInfo', 'businessDriver', e.target.value)}
                      className="form-input"
                    >
                      <option value="Market Expansion">Market Expansion</option>
                      <option value="Cost Reduction">Cost Reduction</option>
                      <option value="Regulatory Compliance">Regulatory Compliance</option>
                      <option value="Digital Transformation">Digital Transformation</option>
                      <option value="Customer Experience">Customer Experience</option>
                      <option value="Innovation">Innovation</option>
                      <option value="Risk Mitigation">Risk Mitigation</option>
                    </select>
                  </div>
                  <div className="form-group">
                    <label>Investment Category</label>
                    <select
                      value={formData.programInfo.investmentCategory}
                      onChange={(e) => handleFormChange('programInfo', 'investmentCategory', e.target.value)}
                      className="form-input"
                    >
                      <option value="Strategic">Strategic</option>
                      <option value="Operational">Operational</option>
                      <option value="Mandatory">Mandatory</option>
                      <option value="Infrastructure">Infrastructure</option>
                    </select>
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label>Total Budget ($) *</label>
                    <input
                      type="number"
                      value={formData.programInfo.totalBudget}
                      onChange={(e) => handleFormChange('programInfo', 'totalBudget', parseFloat(e.target.value) || 0)}
                      className="form-input"
                      min="0"
                      required
                    />
                  </div>
                  <div className="form-group">
                    <label>Duration (Months) *</label>
                    <input
                      type="number"
                      value={formData.programInfo.duration}
                      onChange={(e) => handleFormChange('programInfo', 'duration', parseInt(e.target.value) || 1)}
                      className="form-input"
                      min="1"
                      required
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => {
                  setIsCreating(false)
                  setIsEditing(false)
                  setSelectedCase(null)
                  resetForm()
                }}
              >
                Cancel
              </button>
              <button 
                className="btn btn-primary"
                onClick={isCreating ? handleCreate : handleUpdate}
              >
                {isCreating ? 'Create' : 'Update'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default MasterBusinessCaseManager
