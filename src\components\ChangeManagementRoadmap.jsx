import React, { useState } from 'react'

const ChangeManagementRoadmap = ({ data, onChange }) => {
  const [activeSection, setActiveSection] = useState('current')

  const roadmapSections = [
    { id: 'current', label: 'Current State', icon: '📍' },
    { id: 'proposed1', label: 'Proposed 1 - Medium Change', icon: '🔄' },
    { id: 'proposed2', label: 'Proposed 2 - Big Change', icon: '🚀' }
  ]

  const renderCurrentState = () => (
    <div className="roadmap-section">
      <h3>📍 Current State</h3>
      <div className="governance-table">
        <table className="data-table">
          <thead>
            <tr>
              <th>Master BC Framework</th>
              <th>PPM</th>
              <th>Change Management</th>
              <th>Milestone for CoA</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><strong>BC1</strong></td>
              <td><strong>EPIC</strong></td>
              <td>-</td>
              <td>-</td>
            </tr>
            <tr>
              <td style={{ paddingLeft: '2rem' }}>Offer1</td>
              <td>NA</td>
              <td>-</td>
              <td>-</td>
            </tr>
            <tr>
              <td style={{ paddingLeft: '2rem' }}>Offer2</td>
              <td>NA</td>
              <td>-</td>
              <td>-</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div className="current-state-analysis">
        <h4>🔍 Current State Analysis:</h4>
        <ul>
          <li>Direct mapping from BC1 to EPIC without program-level governance</li>
          <li>Multiple offers under single business case create approval complexity</li>
          <li>No structured change management process</li>
          <li>Limited milestone-based approval gates</li>
        </ul>
      </div>
    </div>
  )

  const renderProposed1 = () => (
    <div className="roadmap-section">
      <h3>🔄 Proposed 1 - Medium Change Management Required</h3>
      <div className="governance-table">
        <table className="data-table">
          <thead>
            <tr>
              <th>Master BC Framework</th>
              <th>PPM</th>
              <th>Change Management</th>
              <th>Milestone for CoA</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><strong>BC1</strong></td>
              <td><strong>Offer/Program</strong></td>
              <td>Medium</td>
              <td><span className="status-badge status-approved">Approve</span></td>
            </tr>
            <tr>
              <td style={{ paddingLeft: '2rem' }}>Offer1</td>
              <td>EPIC/Project</td>
              <td>-</td>
              <td>No CoA for individual EPIC/Project</td>
            </tr>
            <tr>
              <td style={{ paddingLeft: '2rem' }}>Offer2</td>
              <td>EPIC/Project</td>
              <td>-</td>
              <td>No CoA for individual EPIC/Project</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div className="proposed-benefits">
        <h4>✅ Benefits:</h4>
        <ul>
          <li>Structured approval process at program level</li>
          <li>Clear separation between program and project governance</li>
          <li>Reduced approval overhead for individual projects</li>
          <li>Better alignment between Master BC Framework and PPM</li>
        </ul>
      </div>

      <div className="implementation-timeline">
        <h4>📅 Implementation Timeline:</h4>
        <div className="timeline-item">
          <span className="timeline-phase">Phase 1 (Months 1-2):</span>
          <span>Update Master BC Framework governance structure</span>
        </div>
        <div className="timeline-item">
          <span className="timeline-phase">Phase 2 (Months 3-4):</span>
          <span>Implement program-level approval workflows</span>
        </div>
        <div className="timeline-item">
          <span className="timeline-phase">Phase 3 (Months 5-6):</span>
          <span>Training and change management rollout</span>
        </div>
      </div>
    </div>
  )

  const renderProposed2 = () => (
    <div className="roadmap-section">
      <h3>🚀 Proposed 2 - Big Change Management</h3>

      <div className="sequential-approach">
        <h4>📋 Sequential Implementation Approach</h4>
        
        <div className="sequence-step">
          <div className="step-header">
            <span className="step-number">Seq1</span>
            <h5>BC Master - Discover Phase</h5>
          </div>
          <div className="governance-table">
            <table className="data-table">
              <thead>
                <tr>
                  <th>Master BC Framework</th>
                  <th>PPM</th>
                  <th>Milestone for CoA</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><strong>BC Master (Discover)</strong></td>
                  <td><strong>Program</strong></td>
                  <td><span className="status-badge status-approved">Approve</span></td>
                </tr>
                <tr>
                  <td style={{ paddingLeft: '2rem' }}>BC1</td>
                  <td>-</td>
                  <td>-</td>
                </tr>
                <tr>
                  <td style={{ paddingLeft: '2rem' }}>BC2</td>
                  <td>-</td>
                  <td>-</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div className="sequence-step">
          <div className="step-header">
            <span className="step-number">Seq2</span>
            <h5>BC1 - Check Pulse MVP1</h5>
          </div>
          <div className="governance-table">
            <table className="data-table">
              <thead>
                <tr>
                  <th>Master BC Framework</th>
                  <th>PPM</th>
                  <th>Milestone for CoA</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><strong>BC1 (check pulse MVP1)</strong></td>
                  <td><strong>EPIC/Project</strong></td>
                  <td><span className="status-badge status-approved">Approve</span></td>
                </tr>
                <tr>
                  <td style={{ paddingLeft: '2rem' }}>Offer1</td>
                  <td>NA</td>
                  <td>-</td>
                </tr>
                <tr>
                  <td style={{ paddingLeft: '2rem' }}>Offer2</td>
                  <td>NA</td>
                  <td>-</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div className="sequence-step">
          <div className="step-header">
            <span className="step-number">Seq3</span>
            <h5>BC2 - Grow Phase</h5>
          </div>
          <div className="governance-table">
            <table className="data-table">
              <thead>
                <tr>
                  <th>Master BC Framework</th>
                  <th>PPM</th>
                  <th>Milestone for CoA</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><strong>BC2 (Grow)</strong></td>
                  <td><strong>EPIC/Project</strong></td>
                  <td><span className="status-badge status-approved">Approve</span></td>
                </tr>
                <tr>
                  <td style={{ paddingLeft: '2rem' }}>Offer1</td>
                  <td>NA</td>
                  <td>-</td>
                </tr>
                <tr>
                  <td style={{ paddingLeft: '2rem' }}>Offer2</td>
                  <td>NA</td>
                  <td>-</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <div className="key-principle">
        <h4>🎯 Key Principle:</h4>
        <div className="principle-box">
          <strong>No more multiple offers as BC is connected with only one EPIC/Program/Offer</strong>
          <p>This ensures clear governance boundaries and eliminates confusion in the approval process.</p>
        </div>
      </div>

      <div className="transformation-benefits">
        <h4>🚀 Transformation Benefits:</h4>
        <ul>
          <li>Clear sequential approval process with defined gates</li>
          <li>One-to-one mapping between Business Cases and EPICs</li>
          <li>Reduced complexity in governance structure</li>
          <li>Enhanced traceability from strategy to execution</li>
          <li>Improved stakeholder accountability</li>
        </ul>
      </div>
    </div>
  )

  const renderImplementationRecommendations = () => (
    <div className="card">
      <div className="card-header">
        <h2 className="card-title">🎯 Implementation Recommendations</h2>
      </div>

      <div className="recommendations">
        <div className="recommendation-item">
          <h4>🔄 Phase 1: Medium Change (Proposed 1)</h4>
          <ul>
            <li>Implement program-level approvals in Master BC Framework</li>
            <li>Establish clear governance between programs and projects</li>
            <li>Reduce approval overhead for individual EPICs</li>
            <li>Timeline: 3-6 months</li>
          </ul>
        </div>

        <div className="recommendation-item">
          <h4>🚀 Phase 2: Big Change (Proposed 2)</h4>
          <ul>
            <li>Implement Master Business Case concept</li>
            <li>Sequential approval process (Seq1 → Seq2 → Seq3)</li>
            <li>One-to-one BC to EPIC/Program mapping</li>
            <li>Timeline: 6-12 months</li>
          </ul>
        </div>

        <div className="recommendation-item">
          <h4>📊 Success Metrics</h4>
          <ul>
            <li>Reduced approval cycle time by 40%</li>
            <li>Improved governance compliance to 95%</li>
            <li>Enhanced stakeholder satisfaction scores</li>
            <li>Better alignment between strategic and operational planning</li>
          </ul>
        </div>
      </div>
    </div>
  )

  return (
    <div className="change-management-roadmap">
      <div className="card">
        <div className="card-header">
          <h2 className="card-title">🗺️ Governance Evolution Roadmap</h2>
          <div style={{ fontSize: '0.9rem', color: '#666' }}>
            Transformation from Current State to Proposed Governance Models
          </div>
        </div>

        <div className="roadmap-navigation">
          {roadmapSections.map((section) => (
            <button
              key={section.id}
              className={`roadmap-btn ${activeSection === section.id ? 'active' : ''}`}
              onClick={() => setActiveSection(section.id)}
            >
              <span>{section.icon}</span>
              <span>{section.label}</span>
            </button>
          ))}
        </div>

        <div className="roadmap-content">
          {activeSection === 'current' && renderCurrentState()}
          {activeSection === 'proposed1' && renderProposed1()}
          {activeSection === 'proposed2' && renderProposed2()}
        </div>
      </div>

      {renderImplementationRecommendations()}
    </div>
  )
}

export default ChangeManagementRoadmap
