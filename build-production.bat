@echo off
echo 🏗️ Building Master BC Framework - Governance Platform for Production
echo ==================================================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js first.
    pause
    exit /b 1
)

REM Check if npm is installed
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm is not installed. Please install npm first.
    pause
    exit /b 1
)

echo 📦 Installing dependencies...
call npm install

if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo 🔧 Building frontend application...
call npm run build

if %errorlevel% neq 0 (
    echo ❌ Failed to build frontend application
    pause
    exit /b 1
)

echo.
echo 📦 Installing backend dependencies...
cd server
call npm install

if %errorlevel% neq 0 (
    echo ❌ Failed to install backend dependencies
    pause
    exit /b 1
)

cd ..

echo.
echo ✅ Production build completed successfully!
echo.
echo 📁 Build artifacts:
echo    Frontend: .\dist\
echo    Backend: .\server\
echo.
echo 🚀 To deploy:
echo    1. Copy .\dist\ to your web server
echo    2. Copy .\server\ to your application server
echo    3. Set up MongoDB connection
echo    4. Configure environment variables
echo    5. Start the backend server: cd server ^&^& npm start
echo.
echo 🎯 Features included in this build:
echo    ✅ Master Business Case Management
echo    ✅ Enhanced PMI Guidelines ^& PGMP Framework
echo    ✅ Multi-Stakeholder Feedback System with CRUD
echo    ✅ Change Management ^& Roadmap
echo    ✅ Financial Modeling ^& Analysis
echo    ✅ Real-time MongoDB Integration
echo    ✅ Professional UI/UX Design
echo    ✅ Mobile-Responsive Layout
echo.
echo Press any key to exit...
pause > nul
