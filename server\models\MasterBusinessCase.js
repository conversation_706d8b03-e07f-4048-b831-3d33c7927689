const mongoose = require('mongoose');

const UseCaseSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  priority: {
    type: String,
    enum: ['High', 'Medium', 'Low'],
    default: 'Medium'
  },
  businessValue: {
    type: String,
    enum: ['Very High', 'High', 'Medium', 'Low'],
    default: 'Medium'
  },
  complexity: {
    type: String,
    enum: ['High', 'Medium', 'Low'],
    default: 'Medium'
  },
  budget: {
    type: Number,
    required: true,
    min: 0
  },
  timeline: {
    type: Number,
    required: true,
    min: 1
  },
  dependencies: [{
    type: String
  }],
  status: {
    type: String,
    enum: ['Concept', 'Planning', 'Analysis', 'Development', 'Testing', 'Deployment', 'Completed', 'On Hold', 'Cancelled'],
    default: 'Concept'
  },
  roi: {
    type: Number,
    default: 0
  },
  npv: {
    type: Number,
    default: 0
  },
  assignedTo: {
    type: String,
    trim: true
  },
  startDate: {
    type: Date
  },
  endDate: {
    type: Date
  }
}, {
  timestamps: true
});

const DecisionGateSchema = new mongoose.Schema({
  gate: {
    type: String,
    required: true
  },
  name: {
    type: String,
    required: true
  },
  status: {
    type: String,
    enum: ['Pending', 'In Review', 'Approved', 'Rejected', 'On Hold'],
    default: 'Pending'
  },
  date: {
    type: Date,
    required: true
  },
  approver: {
    type: String,
    trim: true
  },
  comments: {
    type: String,
    trim: true
  }
}, {
  timestamps: true
});

const MilestoneSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  targetDate: {
    type: Date,
    required: true
  },
  actualDate: {
    type: Date
  },
  status: {
    type: String,
    enum: ['Not Started', 'In Progress', 'Completed', 'Delayed', 'At Risk'],
    default: 'Not Started'
  },
  budgetAllocated: {
    type: Number,
    required: true,
    min: 0
  },
  actualCost: {
    type: Number,
    default: 0,
    min: 0
  },
  progress: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  approvals: [{
    stakeholder: {
      type: String,
      required: true
    },
    role: {
      type: String,
      required: true
    },
    status: {
      type: String,
      enum: ['Required', 'Pending', 'Approved', 'Rejected'],
      default: 'Required'
    },
    date: {
      type: Date
    },
    comments: {
      type: String
    }
  }]
}, {
  timestamps: true
});

const MasterBusinessCaseSchema = new mongoose.Schema({
  programInfo: {
    programName: {
      type: String,
      required: true,
      trim: true,
      maxlength: 200
    },
    programManager: {
      type: String,
      required: true,
      trim: true
    },
    sponsor: {
      type: String,
      required: true,
      trim: true
    },
    strategicAlignment: {
      type: String,
      enum: ['High', 'Medium', 'Low'],
      required: true
    },
    businessDriver: {
      type: String,
      enum: ['Market Expansion', 'Cost Reduction', 'Regulatory Compliance', 'Digital Transformation', 'Customer Experience', 'Innovation', 'Risk Mitigation'],
      required: true
    },
    investmentCategory: {
      type: String,
      enum: ['Strategic', 'Operational', 'Mandatory', 'Infrastructure'],
      required: true
    },
    totalBudget: {
      type: Number,
      required: true,
      min: 0
    },
    duration: {
      type: Number,
      required: true,
      min: 1
    },
    startDate: {
      type: Date,
      required: true
    },
    endDate: {
      type: Date,
      required: true
    },
    currency: {
      type: String,
      default: 'USD',
      enum: ['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD']
    }
  },
  
  governance: {
    steeringCommittee: [{
      name: {
        type: String,
        required: true
      },
      role: {
        type: String,
        required: true
      },
      email: {
        type: String
      }
    }],
    decisionGates: [DecisionGateSchema],
    riskTolerance: {
      type: String,
      enum: ['Low', 'Medium', 'High'],
      default: 'Medium'
    },
    complianceFramework: [{
      type: String,
      enum: ['PMI', 'PGMP', 'ISO 21500', 'PRINCE2', 'Agile', 'SAFe']
    }]
  },
  
  useCases: [UseCaseSchema],
  
  milestones: [MilestoneSchema],
  
  financialMetrics: {
    totalInvestment: {
      type: Number,
      default: 0
    },
    expectedROI: {
      type: Number,
      default: 0
    },
    npv: {
      type: Number,
      default: 0
    },
    irr: {
      type: Number,
      default: 0
    },
    paybackPeriod: {
      type: Number,
      default: 0
    },
    yieldIndex: {
      type: Number,
      default: 0
    }
  },
  
  status: {
    type: String,
    enum: ['Draft', 'Under Review', 'Approved', 'Active', 'On Hold', 'Completed', 'Cancelled'],
    default: 'Draft'
  },
  
  createdBy: {
    type: String,
    required: true,
    trim: true
  },
  
  lastModifiedBy: {
    type: String,
    trim: true
  },
  
  tags: [{
    type: String,
    trim: true
  }],
  
  attachments: [{
    filename: String,
    originalName: String,
    path: String,
    size: Number,
    uploadDate: {
      type: Date,
      default: Date.now
    }
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for calculating total use cases budget
MasterBusinessCaseSchema.virtual('totalUseCasesBudget').get(function() {
  return this.useCases.reduce((total, useCase) => total + useCase.budget, 0);
});

// Virtual for calculating average ROI
MasterBusinessCaseSchema.virtual('averageROI').get(function() {
  if (this.useCases.length === 0) return 0;
  const totalROI = this.useCases.reduce((total, useCase) => total + useCase.roi, 0);
  return totalROI / this.useCases.length;
});

// Virtual for calculating total milestones budget
MasterBusinessCaseSchema.virtual('totalMilestonesBudget').get(function() {
  return this.milestones.reduce((total, milestone) => total + milestone.budgetAllocated, 0);
});

// Virtual for calculating total actual costs
MasterBusinessCaseSchema.virtual('totalActualCosts').get(function() {
  return this.milestones.reduce((total, milestone) => total + milestone.actualCost, 0);
});

// Virtual for calculating budget variance
MasterBusinessCaseSchema.virtual('budgetVariance').get(function() {
  return this.totalMilestonesBudget - this.totalActualCosts;
});

// Indexes for better query performance
MasterBusinessCaseSchema.index({ 'programInfo.programName': 'text' });
MasterBusinessCaseSchema.index({ status: 1 });
MasterBusinessCaseSchema.index({ createdBy: 1 });
MasterBusinessCaseSchema.index({ 'programInfo.businessDriver': 1 });
MasterBusinessCaseSchema.index({ 'programInfo.investmentCategory': 1 });
MasterBusinessCaseSchema.index({ createdAt: -1 });

// Pre-save middleware to update financial metrics
MasterBusinessCaseSchema.pre('save', function(next) {
  // Update total investment
  this.financialMetrics.totalInvestment = this.totalUseCasesBudget;
  
  // Update expected ROI
  this.financialMetrics.expectedROI = this.averageROI;
  
  next();
});

module.exports = mongoose.model('MasterBusinessCase', MasterBusinessCaseSchema);
