// Financial Calculation Engine for Business Case Analysis

export class FinancialCalculator {
  constructor(parameters) {
    this.discountRate = parameters.discountRate / 100;
    this.taxRate = parameters.taxRate / 100;
    this.inflationRate = parameters.inflationRate / 100;
    this.projectDuration = parameters.projectDuration;
    this.baseCurrency = parameters.baseCurrency;
    this.exchangeRates = parameters.exchangeRates;
    this.inflationToggles = parameters.inflationToggles;
  }

  // Calculate Net Present Value (NPV)
  calculateNPV(cashFlows, initialInvestment = 0) {
    let npv = -initialInvestment;
    
    cashFlows.forEach((cashFlow, year) => {
      const discountFactor = Math.pow(1 + this.discountRate, year + 1);
      npv += cashFlow / discountFactor;
    });
    
    return npv;
  }

  // Calculate Internal Rate of Return (IRR) using Newton-<PERSON><PERSON>on method
  calculateIRR(cashFlows, initialInvestment = 0, guess = 0.1) {
    const flows = [-initialInvestment, ...cashFlows];
    let rate = guess;
    const maxIterations = 100;
    const tolerance = 0.0001;

    for (let i = 0; i < maxIterations; i++) {
      let npv = 0;
      let dnpv = 0;

      flows.forEach((flow, period) => {
        npv += flow / Math.pow(1 + rate, period);
        if (period > 0) {
          dnpv -= period * flow / Math.pow(1 + rate, period + 1);
        }
      });

      const newRate = rate - npv / dnpv;
      
      if (Math.abs(newRate - rate) < tolerance) {
        return newRate * 100; // Return as percentage
      }
      
      rate = newRate;
    }
    
    return rate * 100; // Return as percentage
  }

  // Calculate Payback Period
  calculatePaybackPeriod(cashFlows, initialInvestment = 0) {
    let cumulativeCashFlow = -initialInvestment;
    
    for (let year = 0; year < cashFlows.length; year++) {
      cumulativeCashFlow += cashFlows[year];
      
      if (cumulativeCashFlow >= 0) {
        // Linear interpolation for more precise payback period
        const previousCumulative = cumulativeCashFlow - cashFlows[year];
        const fraction = Math.abs(previousCumulative) / cashFlows[year];
        return year + fraction;
      }
    }
    
    return null; // Payback period exceeds project duration
  }

  // Calculate Yield Index (Profitability Index)
  calculateYieldIndex(cashFlows, initialInvestment = 0) {
    const presentValueOfCashFlows = cashFlows.reduce((pv, cashFlow, year) => {
      const discountFactor = Math.pow(1 + this.discountRate, year + 1);
      return pv + (cashFlow / discountFactor);
    }, 0);
    
    return presentValueOfCashFlows / initialInvestment;
  }

  // Apply inflation to costs/revenues
  applyInflation(baseValue, year, category) {
    if (!this.inflationToggles[category]) {
      return baseValue;
    }
    
    return baseValue * Math.pow(1 + this.inflationRate, year);
  }

  // Calculate annual cash flows from project data
  generateCashFlows(projectData) {
    const cashFlows = [];
    const { costs, sales, parameters } = projectData;

    for (let year = 1; year <= this.projectDuration; year++) {
      let annualRevenue = 0;
      let annualCosts = 0;

      // Calculate revenue
      if (sales.offers) {
        sales.offers.forEach(offer => {
          const baseRevenue = offer.price * offer.volume;
          const inflatedRevenue = this.applyInflation(baseRevenue, year - 1, 'sales');
          
          // Apply growth rate based on regions
          const growthFactor = sales.regions.reduce((avg, region) => {
            return avg + (region.growth / 100) * (region.share / 100);
          }, 0);
          
          const growthMultiplier = Math.pow(1 + growthFactor, year - 1);
          annualRevenue += inflatedRevenue * growthMultiplier;
        });
      }

      // Calculate costs
      // CAPEX (one-time investments)
      if (costs.capex) {
        costs.capex.forEach(item => {
          if (item.year === year) {
            annualCosts += this.applyInflation(item.amount, year - 1, 'production');
          }
        });
      }

      // OPEX (recurring costs)
      if (costs.opex) {
        costs.opex.forEach(item => {
          if (item.recurring) {
            annualCosts += this.applyInflation(item.amount, year - 1, 'production');
          }
        });
      }

      // Tools depreciation
      if (costs.tools) {
        costs.tools.forEach(tool => {
          const annualDepreciation = tool.amount / tool.lifespan;
          if (year <= tool.lifespan) {
            annualCosts += annualDepreciation;
          }
        });
      }

      // Machinery costs
      if (costs.machinery) {
        costs.machinery.forEach(machine => {
          if (machine.year === year) {
            annualCosts += this.applyInflation(machine.amount, year - 1, 'production');
          }
        });
      }

      // Calculate net cash flow (after tax)
      const grossProfit = annualRevenue - annualCosts;
      const tax = grossProfit > 0 ? grossProfit * this.taxRate : 0;
      const netCashFlow = grossProfit - tax;

      cashFlows.push(netCashFlow);
    }

    return cashFlows;
  }

  // Calculate all financial metrics
  calculateAllMetrics(projectData) {
    const cashFlows = this.generateCashFlows(projectData);
    const initialInvestment = this.calculateInitialInvestment(projectData.costs);

    const npv = this.calculateNPV(cashFlows, initialInvestment);
    const irr = this.calculateIRR(cashFlows, initialInvestment);
    const payback = this.calculatePaybackPeriod(cashFlows, initialInvestment);
    const yieldIndex = this.calculateYieldIndex(cashFlows, initialInvestment);

    // Calculate additional metrics
    const totalRevenue = cashFlows.reduce((sum, cf) => sum + Math.max(0, cf), 0);
    const totalCosts = cashFlows.reduce((sum, cf) => sum + Math.max(0, -cf), 0);
    const grossMargin = totalRevenue > 0 ? ((totalRevenue - totalCosts) / totalRevenue) * 100 : 0;

    // Break-even analysis
    const breakEvenSales = this.calculateBreakEvenSales(projectData);

    return {
      npv,
      irr,
      payback,
      yieldIndex,
      grossMargin,
      totalRevenue,
      totalCosts,
      breakEvenSales,
      cashFlows,
      yearlyData: this.generateYearlyData(projectData, cashFlows)
    };
  }

  // Calculate initial investment
  calculateInitialInvestment(costs) {
    let initialInvestment = 0;

    if (costs.capex) {
      initialInvestment += costs.capex
        .filter(item => item.year === 1)
        .reduce((sum, item) => sum + item.amount, 0);
    }

    if (costs.machinery) {
      initialInvestment += costs.machinery
        .filter(item => item.year === 1)
        .reduce((sum, item) => sum + item.amount, 0);
    }

    return initialInvestment;
  }

  // Calculate break-even sales
  calculateBreakEvenSales(projectData) {
    const fixedCosts = this.calculateFixedCosts(projectData.costs);
    const variableCostRatio = 0.6; // Assume 60% variable costs
    const averagePrice = this.calculateAveragePrice(projectData.sales);

    return fixedCosts / (averagePrice * (1 - variableCostRatio));
  }

  // Helper methods
  calculateFixedCosts(costs) {
    let fixedCosts = 0;
    
    if (costs.opex) {
      fixedCosts += costs.opex.reduce((sum, item) => sum + item.amount, 0);
    }
    
    return fixedCosts;
  }

  calculateAveragePrice(sales) {
    if (!sales.offers || sales.offers.length === 0) return 0;
    
    const totalValue = sales.offers.reduce((sum, offer) => sum + (offer.price * offer.volume), 0);
    const totalVolume = sales.offers.reduce((sum, offer) => sum + offer.volume, 0);
    
    return totalVolume > 0 ? totalValue / totalVolume : 0;
  }

  // Generate detailed yearly data
  generateYearlyData(projectData, cashFlows) {
    const yearlyData = [];
    
    for (let year = 1; year <= this.projectDuration; year++) {
      const revenue = this.calculateYearlyRevenue(projectData.sales, year);
      const costs = this.calculateYearlyCosts(projectData.costs, year);
      const cashFlow = cashFlows[year - 1];
      const cumulative = yearlyData.reduce((sum, data) => sum + data.cashFlow, 0) + cashFlow;

      yearlyData.push({
        year,
        revenue,
        costs,
        cashFlow,
        cumulative
      });
    }

    return yearlyData;
  }

  calculateYearlyRevenue(sales, year) {
    if (!sales.offers) return 0;
    
    return sales.offers.reduce((total, offer) => {
      const baseRevenue = offer.price * offer.volume;
      const inflatedRevenue = this.applyInflation(baseRevenue, year - 1, 'sales');
      
      const growthFactor = sales.regions?.reduce((avg, region) => {
        return avg + (region.growth / 100) * (region.share / 100);
      }, 0) || 0;
      
      const growthMultiplier = Math.pow(1 + growthFactor, year - 1);
      return total + (inflatedRevenue * growthMultiplier);
    }, 0);
  }

  calculateYearlyCosts(costs, year) {
    let totalCosts = 0;

    // CAPEX
    if (costs.capex) {
      totalCosts += costs.capex
        .filter(item => item.year === year)
        .reduce((sum, item) => sum + this.applyInflation(item.amount, year - 1, 'production'), 0);
    }

    // OPEX
    if (costs.opex) {
      totalCosts += costs.opex
        .filter(item => item.recurring)
        .reduce((sum, item) => sum + this.applyInflation(item.amount, year - 1, 'production'), 0);
    }

    // Machinery
    if (costs.machinery) {
      totalCosts += costs.machinery
        .filter(item => item.year === year)
        .reduce((sum, item) => sum + this.applyInflation(item.amount, year - 1, 'production'), 0);
    }

    return totalCosts;
  }
}

// Sensitivity Analysis utilities
export class SensitivityAnalyzer {
  constructor(baseCalculator) {
    this.baseCalculator = baseCalculator;
  }

  // Run sensitivity analysis for multiple variables
  runSensitivityAnalysis(projectData, variables) {
    const results = {
      best: {},
      base: {},
      worst: {}
    };

    // Base case
    results.base = this.baseCalculator.calculateAllMetrics(projectData);

    // Best case scenario
    const bestCaseData = this.applyVariableChanges(projectData, variables, 'max');
    results.best = this.baseCalculator.calculateAllMetrics(bestCaseData);

    // Worst case scenario
    const worstCaseData = this.applyVariableChanges(projectData, variables, 'min');
    results.worst = this.baseCalculator.calculateAllMetrics(worstCaseData);

    return results;
  }

  applyVariableChanges(projectData, variables, scenario) {
    const modifiedData = JSON.parse(JSON.stringify(projectData)); // Deep clone

    Object.entries(variables).forEach(([varName, varData]) => {
      const value = scenario === 'max' ? varData.max : varData.min;
      this.applyVariableChange(modifiedData, varName, value);
    });

    return modifiedData;
  }

  applyVariableChange(projectData, varName, value) {
    switch (varName) {
      case 'salesPrice':
        if (projectData.sales.offers) {
          projectData.sales.offers.forEach(offer => {
            offer.price = value;
          });
        }
        break;
      case 'salesVolume':
        if (projectData.sales.offers) {
          projectData.sales.offers.forEach(offer => {
            offer.volume = value;
          });
        }
        break;
      case 'productionCost':
        if (projectData.costs.opex) {
          projectData.costs.opex.forEach(cost => {
            if (cost.name.toLowerCase().includes('production')) {
              cost.amount = value;
            }
          });
        }
        break;
      case 'marketingCost':
        if (projectData.costs.opex) {
          projectData.costs.opex.forEach(cost => {
            if (cost.name.toLowerCase().includes('marketing')) {
              cost.amount = value;
            }
          });
        }
        break;
      case 'discountRate':
        projectData.parameters.discountRate = value;
        this.baseCalculator.discountRate = value / 100;
        break;
    }
  }
}

export default FinancialCalculator;
