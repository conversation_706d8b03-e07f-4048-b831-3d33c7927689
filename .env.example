# Master BC Framework - Governance Platform
# Environment Configuration

# Application Settings
REACT_APP_APP_NAME=Master BC Framework - Governance Platform
REACT_APP_VERSION=1.0.0
REACT_APP_ENVIRONMENT=development

# API Configuration
REACT_APP_API_BASE_URL=http://localhost:5000/api
REACT_APP_API_TIMEOUT=10000

# Database Configuration (for backend)
MONGODB_URI=mongodb://localhost:27017/master-bc-framework
MONGODB_DB_NAME=master-bc-framework

# Server Configuration (for backend)
PORT=5000
NODE_ENV=development

# Features Flags
REACT_APP_ENABLE_ANALYTICS=true
REACT_APP_ENABLE_EXPORT=true
REACT_APP_ENABLE_NOTIFICATIONS=true

# Security
JWT_SECRET=your-jwt-secret-key-here
SESSION_SECRET=your-session-secret-here

# External Services (optional)
REACT_APP_GOOGLE_ANALYTICS_ID=
REACT_APP_SENTRY_DSN=

# Development Settings
REACT_APP_DEBUG_MODE=true
REACT_APP_LOG_LEVEL=debug
