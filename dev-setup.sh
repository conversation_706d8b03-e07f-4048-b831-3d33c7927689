#!/bin/bash

echo "🔧 Master BC Framework - Development Setup"
echo "=========================================="
echo ""

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo "📋 Checking prerequisites..."

if ! command_exists node; then
    echo "❌ Node.js is not installed"
    echo "   Please install Node.js from: https://nodejs.org/"
    exit 1
else
    echo "✅ Node.js: $(node --version)"
fi

if ! command_exists npm; then
    echo "❌ npm is not installed"
    echo "   Please install npm (usually comes with Node.js)"
    exit 1
else
    echo "✅ npm: $(npm --version)"
fi

echo ""
echo "📦 Installing dependencies..."

# Install frontend dependencies
echo "Installing frontend dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install frontend dependencies"
    exit 1
fi

# Install backend dependencies
echo "Installing backend dependencies..."
cd server
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install backend dependencies"
    exit 1
fi

cd ..

echo ""
echo "📄 Setting up environment..."

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "Creating .env file from template..."
    cp .env.example .env
    echo "✅ .env file created. Please review and update as needed."
else
    echo "✅ .env file already exists"
fi

# Create server .env file if it doesn't exist
if [ ! -f server/.env ]; then
    echo "Creating server/.env file..."
    cat > server/.env << EOL
# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/master-bc-framework
MONGODB_DB_NAME=master-bc-framework

# Security
JWT_SECRET=dev-jwt-secret-key-change-in-production
SESSION_SECRET=dev-session-secret-change-in-production

# Features
ENABLE_CORS=true
ENABLE_LOGGING=true
EOL
    echo "✅ server/.env file created"
else
    echo "✅ server/.env file already exists"
fi

echo ""
echo "🗄️ Database setup information..."
echo "   MongoDB is required for full functionality"
echo "   Options:"
echo "   1. Local MongoDB: Install from https://www.mongodb.com/try/download/community"
echo "   2. MongoDB Atlas: Create free cluster at https://www.mongodb.com/atlas"
echo "   3. Docker: docker run -d -p 27017:27017 --name mongodb mongo:latest"

echo ""
echo "✅ Development setup completed!"
echo ""
echo "🚀 To start development:"
echo "   Option 1 (Automated): ./start-full-stack.sh"
echo "   Option 2 (Manual):"
echo "     Terminal 1: cd server && npm run dev"
echo "     Terminal 2: npm run dev"
echo ""
echo "🌐 Access points:"
echo "   Frontend: http://localhost:3000"
echo "   Backend: http://localhost:5000"
echo "   Demo: Open demo.html in browser"
echo ""
echo "📚 Documentation:"
echo "   Quick Start: QUICK_START.md"
echo "   Setup Guide: setup.md"
echo ""
echo "🎯 Key Features Ready:"
echo "   ✅ Master Business Case Management"
echo "   ✅ Enhanced PMI Guidelines & PGMP Framework"
echo "   ✅ Multi-Stakeholder Feedback System with CRUD"
echo "   ✅ Change Management & Roadmap"
echo "   ✅ Financial Modeling & Analysis"
echo ""
