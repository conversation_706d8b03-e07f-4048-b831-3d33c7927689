import React, { useState, useCallback, useRef } from 'react'
import * as XLSX from 'xlsx'

const AugmentExcelAnalysis = ({ data, onChange }) => {
  const [excelData, setExcelData] = useState({
    rawData: [],
    processedData: [],
    columns: [],
    fileName: '',
    lastImported: null,
    validationErrors: [],
    analysisResults: {
      irr: 0,
      npv: 0,
      paybackPeriod: 0,
      roi: 0,
      breakEvenPoint: 0
    },
    scenarios: {
      optimistic: { multiplier: 1.2, results: {} },
      realistic: { multiplier: 1.0, results: {} },
      pessimistic: { multiplier: 0.8, results: {} }
    },
    currentScenario: 'realistic',
    discountRate: 10,
    ...data
  })

  const [activeView, setActiveView] = useState('import')
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [selectedRows, setSelectedRows] = useState([])
  const fileInputRef = useRef(null)

  // Required columns for financial analysis
  const requiredColumns = ['Year', 'Revenue', 'Cost', 'CashFlow']
  const optionalColumns = ['Investment', 'Tax', 'Depreciation', 'WorkingCapital']

  // Excel Import Functionality
  const handleFileUpload = useCallback((event) => {
    const file = event.target.files[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target.result)
        const workbook = XLSX.read(data, { type: 'array' })
        const sheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[sheetName]
        const jsonData = XLSX.utils.sheet_to_json(worksheet)

        // Validate data structure
        const validation = validateExcelData(jsonData)

        if (validation.isValid) {
          const processedData = processExcelData(jsonData)
          setExcelData(prev => ({
            ...prev,
            rawData: jsonData,
            processedData: processedData,
            columns: Object.keys(jsonData[0] || {}),
            fileName: file.name,
            lastImported: new Date().toISOString(),
            validationErrors: []
          }))

          // Automatically perform analysis
          performFinancialAnalysis(processedData)
          setActiveView('analysis')
        } else {
          setExcelData(prev => ({
            ...prev,
            validationErrors: validation.errors
          }))
        }
      } catch (error) {
        console.error('Error reading Excel file:', error)
        setExcelData(prev => ({
          ...prev,
          validationErrors: ['Error reading Excel file. Please check the file format.']
        }))
      }
    }
    reader.readAsArrayBuffer(file)
  }, [])

  // Data Validation
  const validateExcelData = (data) => {
    const errors = []

    if (!data || data.length === 0) {
      errors.push('Excel file is empty or contains no data')
      return { isValid: false, errors }
    }

    const columns = Object.keys(data[0])
    const missingColumns = requiredColumns.filter(col => !columns.includes(col))

    if (missingColumns.length > 0) {
      errors.push(`Missing required columns: ${missingColumns.join(', ')}`)
    }

    // Validate data types
    data.forEach((row, index) => {
      if (typeof row.Year !== 'number' || row.Year < 1900 || row.Year > 2100) {
        errors.push(`Invalid year in row ${index + 1}: ${row.Year}`)
      }
      if (typeof row.Revenue !== 'number' || row.Revenue < 0) {
        errors.push(`Invalid revenue in row ${index + 1}: ${row.Revenue}`)
      }
      if (typeof row.Cost !== 'number' || row.Cost < 0) {
        errors.push(`Invalid cost in row ${index + 1}: ${row.Cost}`)
      }
    })

    return { isValid: errors.length === 0, errors }
  }

  // Process Excel Data
  const processExcelData = (rawData) => {
    return rawData.map((row, index) => ({
      id: index + 1,
      year: row.Year,
      revenue: row.Revenue || 0,
      cost: row.Cost || 0,
      cashFlow: row.CashFlow || (row.Revenue - row.Cost),
      investment: row.Investment || 0,
      tax: row.Tax || 0,
      depreciation: row.Depreciation || 0,
      workingCapital: row.WorkingCapital || 0,
      netCashFlow: 0, // Will be calculated
      cumulativeCashFlow: 0 // Will be calculated
    }))
  }

  // Financial Analysis Calculations
  const calculateIRR = (cashFlows) => {
    // Simplified IRR calculation using Newton-Raphson method
    let rate = 0.1 // Initial guess
    const maxIterations = 100
    const tolerance = 0.0001

    for (let i = 0; i < maxIterations; i++) {
      let npv = 0
      let dnpv = 0

      cashFlows.forEach((cf, t) => {
        npv += cf / Math.pow(1 + rate, t)
        dnpv -= t * cf / Math.pow(1 + rate, t + 1)
      })

      if (Math.abs(npv) < tolerance) break
      rate = rate - npv / dnpv
    }

    return rate * 100 // Return as percentage
  }

  const calculateNPV = (cashFlows, discountRate) => {
    return cashFlows.reduce((npv, cf, t) => {
      return npv + cf / Math.pow(1 + discountRate / 100, t)
    }, 0)
  }

  const calculatePaybackPeriod = (cashFlows) => {
    let cumulativeCF = 0
    for (let i = 0; i < cashFlows.length; i++) {
      cumulativeCF += cashFlows[i]
      if (cumulativeCF >= 0) {
        return i + (Math.abs(cumulativeCF - cashFlows[i]) / cashFlows[i])
      }
    }
    return cashFlows.length // If never positive
  }

  // Perform Financial Analysis
  const performFinancialAnalysis = useCallback((data) => {
    setIsAnalyzing(true)

    setTimeout(() => {
      // Calculate net cash flows
      const processedData = data.map((row, index) => {
        const netCashFlow = row.cashFlow - row.investment - row.tax
        const cumulativeCashFlow = index === 0 ? netCashFlow :
          data.slice(0, index + 1).reduce((sum, r) => sum + (r.cashFlow - r.investment - r.tax), 0)

        return {
          ...row,
          netCashFlow,
          cumulativeCashFlow
        }
      })

      const cashFlows = processedData.map(row => row.netCashFlow)

      // Calculate financial metrics
      const irr = calculateIRR(cashFlows)
      const npv = calculateNPV(cashFlows, excelData.discountRate)
      const paybackPeriod = calculatePaybackPeriod(cashFlows)
      const totalInvestment = processedData.reduce((sum, row) => sum + row.investment, 0)
      const totalRevenue = processedData.reduce((sum, row) => sum + row.revenue, 0)
      const roi = totalInvestment > 0 ? ((totalRevenue - totalInvestment) / totalInvestment) * 100 : 0

      // Calculate break-even point
      const breakEvenPoint = processedData.findIndex(row => row.cumulativeCashFlow >= 0) + 1

      const analysisResults = {
        irr: Math.round(irr * 100) / 100,
        npv: Math.round(npv),
        paybackPeriod: Math.round(paybackPeriod * 100) / 100,
        roi: Math.round(roi * 100) / 100,
        breakEvenPoint
      }

      // Perform scenario analysis
      const scenarios = {}
      ['optimistic', 'realistic', 'pessimistic'].forEach(scenario => {
        const multiplier = excelData.scenarios[scenario].multiplier
        const scenarioCashFlows = cashFlows.map(cf => cf * multiplier)

        scenarios[scenario] = {
          multiplier,
          results: {
            irr: Math.round(calculateIRR(scenarioCashFlows) * 100) / 100,
            npv: Math.round(calculateNPV(scenarioCashFlows, excelData.discountRate)),
            paybackPeriod: Math.round(calculatePaybackPeriod(scenarioCashFlows) * 100) / 100,
            roi: Math.round(roi * multiplier * 100) / 100
          }
        }
      })

      setExcelData(prev => ({
        ...prev,
        processedData,
        analysisResults,
        scenarios
      }))

      setIsAnalyzing(false)
    }, 1000) // Simulate processing time
  }, [excelData.discountRate, excelData.scenarios])

  // CRUD Operations
  const handleAddRow = () => {
    const newRow = {
      id: excelData.processedData.length + 1,
      year: new Date().getFullYear(),
      revenue: 0,
      cost: 0,
      cashFlow: 0,
      investment: 0,
      tax: 0,
      depreciation: 0,
      workingCapital: 0,
      netCashFlow: 0,
      cumulativeCashFlow: 0
    }

    const updatedData = [...excelData.processedData, newRow]
    setExcelData(prev => ({ ...prev, processedData: updatedData }))
    performFinancialAnalysis(updatedData)
  }

  const handleUpdateRow = (id, field, value) => {
    const updatedData = excelData.processedData.map(row => {
      if (row.id === id) {
        const updatedRow = { ...row, [field]: parseFloat(value) || 0 }
        if (field === 'revenue' || field === 'cost') {
          updatedRow.cashFlow = updatedRow.revenue - updatedRow.cost
        }
        return updatedRow
      }
      return row
    })

    setExcelData(prev => ({ ...prev, processedData: updatedData }))
    performFinancialAnalysis(updatedData)
  }

  const handleDeleteRows = () => {
    if (selectedRows.length === 0) return

    if (window.confirm(`Delete ${selectedRows.length} selected row(s)?`)) {
      const updatedData = excelData.processedData.filter(row => !selectedRows.includes(row.id))
      setExcelData(prev => ({ ...prev, processedData: updatedData }))
      setSelectedRows([])
      performFinancialAnalysis(updatedData)
    }
  }

  // Excel Export Functionality
  const handleExportToExcel = () => {
    const exportData = excelData.processedData.map(row => ({
      Year: row.year,
      Revenue: row.revenue,
      Cost: row.cost,
      'Cash Flow': row.cashFlow,
      Investment: row.investment,
      Tax: row.tax,
      Depreciation: row.depreciation,
      'Working Capital': row.workingCapital,
      'Net Cash Flow': row.netCashFlow,
      'Cumulative Cash Flow': row.cumulativeCashFlow
    }))

    // Add analysis results
    exportData.push({})
    exportData.push({ Year: 'ANALYSIS RESULTS' })
    exportData.push({ Year: 'IRR (%)', Revenue: excelData.analysisResults.irr })
    exportData.push({ Year: 'NPV', Revenue: excelData.analysisResults.npv })
    exportData.push({ Year: 'Payback Period (Years)', Revenue: excelData.analysisResults.paybackPeriod })
    exportData.push({ Year: 'ROI (%)', Revenue: excelData.analysisResults.roi })
    exportData.push({ Year: 'Break-Even Point (Year)', Revenue: excelData.analysisResults.breakEvenPoint })

    const worksheet = XLSX.utils.json_to_sheet(exportData)
    const workbook = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Financial Analysis')

    const fileName = `financial_analysis_${new Date().toISOString().split('T')[0]}.xlsx`
    XLSX.writeFile(workbook, fileName)
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const views = [
    { id: 'import', label: 'Excel Import', icon: '📊' },
    { id: 'data', label: 'Data Management', icon: '📋' },
    { id: 'analysis', label: 'Financial Analysis', icon: '📈' },
    { id: 'scenarios', label: 'Scenario Analysis', icon: '🎯' },
    { id: 'augment', label: 'Augment AI Assistant', icon: '🤖' }
  ]

  // Render Functions
  const renderImportView = () => (
    <div className="import-view">
      <div className="import-section">
        <h3>📊 Excel Data Import</h3>
        <p>Upload your Excel file (.xlsx or .xls) containing financial data for analysis.</p>

        <div className="upload-area">
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileUpload}
            accept=".xlsx,.xls"
            style={{ display: 'none' }}
          />
          <div
            className="upload-dropzone"
            onClick={() => fileInputRef.current?.click()}
          >
            <div className="upload-icon">📁</div>
            <div className="upload-text">
              <strong>Click to upload Excel file</strong>
              <p>Supports .xlsx and .xls formats</p>
            </div>
          </div>
        </div>

        <div className="requirements-section">
          <h4>📋 Required Columns</h4>
          <div className="requirements-grid">
            {requiredColumns.map(col => (
              <div key={col} className="requirement-item required">
                <span className="requirement-icon">✅</span>
                <span className="requirement-name">{col}</span>
                <span className="requirement-type">Required</span>
              </div>
            ))}
          </div>

          <h4>📋 Optional Columns</h4>
          <div className="requirements-grid">
            {optionalColumns.map(col => (
              <div key={col} className="requirement-item optional">
                <span className="requirement-icon">⭕</span>
                <span className="requirement-name">{col}</span>
                <span className="requirement-type">Optional</span>
              </div>
            ))}
          </div>
        </div>

        {excelData.validationErrors.length > 0 && (
          <div className="validation-errors">
            <h4>❌ Validation Errors</h4>
            {excelData.validationErrors.map((error, index) => (
              <div key={index} className="error-item">
                {error}
              </div>
            ))}
          </div>
        )}

        {excelData.fileName && (
          <div className="import-success">
            <h4>✅ Successfully Imported</h4>
            <div className="import-details">
              <div className="detail-item">
                <strong>File:</strong> {excelData.fileName}
              </div>
              <div className="detail-item">
                <strong>Imported:</strong> {new Date(excelData.lastImported).toLocaleString()}
              </div>
              <div className="detail-item">
                <strong>Rows:</strong> {excelData.processedData.length}
              </div>
              <div className="detail-item">
                <strong>Columns:</strong> {excelData.columns.join(', ')}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )

  const renderDataView = () => (
    <div className="data-view">
      <div className="data-header">
        <h3>📋 Data Management & CRUD Operations</h3>
        <div className="data-actions">
          <button className="btn btn-primary" onClick={handleAddRow}>
            ➕ Add Row
          </button>
          {selectedRows.length > 0 && (
            <button className="btn btn-danger" onClick={handleDeleteRows}>
              🗑️ Delete Selected ({selectedRows.length})
            </button>
          )}
        </div>
      </div>

      {excelData.processedData.length > 0 ? (
        <div className="data-table-container">
          <table className="data-table">
            <thead>
              <tr>
                <th>
                  <input
                    type="checkbox"
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedRows(excelData.processedData.map(row => row.id))
                      } else {
                        setSelectedRows([])
                      }
                    }}
                    checked={selectedRows.length === excelData.processedData.length}
                  />
                </th>
                <th>Year</th>
                <th>Revenue</th>
                <th>Cost</th>
                <th>Cash Flow</th>
                <th>Investment</th>
                <th>Tax</th>
                <th>Net Cash Flow</th>
                <th>Cumulative CF</th>
              </tr>
            </thead>
            <tbody>
              {excelData.processedData.map((row) => (
                <tr key={row.id} className={selectedRows.includes(row.id) ? 'selected' : ''}>
                  <td>
                    <input
                      type="checkbox"
                      checked={selectedRows.includes(row.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedRows([...selectedRows, row.id])
                        } else {
                          setSelectedRows(selectedRows.filter(id => id !== row.id))
                        }
                      }}
                    />
                  </td>
                  <td>
                    <input
                      type="number"
                      value={row.year}
                      onChange={(e) => handleUpdateRow(row.id, 'year', e.target.value)}
                      className="table-input"
                    />
                  </td>
                  <td>
                    <input
                      type="number"
                      value={row.revenue}
                      onChange={(e) => handleUpdateRow(row.id, 'revenue', e.target.value)}
                      className="table-input currency"
                    />
                  </td>
                  <td>
                    <input
                      type="number"
                      value={row.cost}
                      onChange={(e) => handleUpdateRow(row.id, 'cost', e.target.value)}
                      className="table-input currency"
                    />
                  </td>
                  <td className="calculated-cell">
                    {formatCurrency(row.cashFlow)}
                  </td>
                  <td>
                    <input
                      type="number"
                      value={row.investment}
                      onChange={(e) => handleUpdateRow(row.id, 'investment', e.target.value)}
                      className="table-input currency"
                    />
                  </td>
                  <td>
                    <input
                      type="number"
                      value={row.tax}
                      onChange={(e) => handleUpdateRow(row.id, 'tax', e.target.value)}
                      className="table-input currency"
                    />
                  </td>
                  <td className="calculated-cell">
                    {formatCurrency(row.netCashFlow)}
                  </td>
                  <td className={`calculated-cell ${row.cumulativeCashFlow >= 0 ? 'positive' : 'negative'}`}>
                    {formatCurrency(row.cumulativeCashFlow)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="no-data">
          <div className="no-data-icon">📊</div>
          <h4>No Data Available</h4>
          <p>Import an Excel file or add rows manually to get started.</p>
        </div>
      )}
    </div>
  )

  const renderAnalysisView = () => (
    <div className="analysis-view">
      <div className="analysis-header">
        <h3>📈 Financial Analysis Results</h3>
        {isAnalyzing && (
          <div className="analyzing-indicator">
            <span className="spinner">🔄</span>
            Analyzing...
          </div>
        )}
      </div>

      <div className="analysis-controls">
        <div className="control-group">
          <label>Discount Rate (%)</label>
          <input
            type="number"
            value={excelData.discountRate}
            onChange={(e) => {
              const newRate = parseFloat(e.target.value) || 10
              setExcelData(prev => ({ ...prev, discountRate: newRate }))
              if (excelData.processedData.length > 0) {
                performFinancialAnalysis(excelData.processedData)
              }
            }}
            className="control-input"
          />
        </div>
      </div>

      <div className="metrics-grid">
        <div className="metric-card irr">
          <div className="metric-icon">📊</div>
          <div className="metric-content">
            <div className="metric-value">{excelData.analysisResults.irr}%</div>
            <div className="metric-label">Internal Rate of Return (IRR)</div>
            <div className="metric-description">
              The discount rate that makes NPV equal to zero
            </div>
          </div>
        </div>

        <div className="metric-card npv">
          <div className="metric-icon">💰</div>
          <div className="metric-content">
            <div className="metric-value">{formatCurrency(excelData.analysisResults.npv)}</div>
            <div className="metric-label">Net Present Value (NPV)</div>
            <div className="metric-description">
              Present value of future cash flows minus initial investment
            </div>
          </div>
        </div>

        <div className="metric-card payback">
          <div className="metric-icon">⏱️</div>
          <div className="metric-content">
            <div className="metric-value">{excelData.analysisResults.paybackPeriod} years</div>
            <div className="metric-label">Payback Period</div>
            <div className="metric-description">
              Time required to recover the initial investment
            </div>
          </div>
        </div>

        <div className="metric-card roi">
          <div className="metric-icon">📈</div>
          <div className="metric-content">
            <div className="metric-value">{excelData.analysisResults.roi}%</div>
            <div className="metric-label">Return on Investment (ROI)</div>
            <div className="metric-description">
              Percentage return on the initial investment
            </div>
          </div>
        </div>

        <div className="metric-card breakeven">
          <div className="metric-icon">🎯</div>
          <div className="metric-content">
            <div className="metric-value">Year {excelData.analysisResults.breakEvenPoint}</div>
            <div className="metric-label">Break-Even Point</div>
            <div className="metric-description">
              When cumulative cash flow becomes positive
            </div>
          </div>
        </div>
      </div>

      {excelData.processedData.length > 0 && (
        <div className="cash-flow-chart">
          <h4>💸 Cash Flow Analysis</h4>
          <div className="chart-container">
            <div className="chart-bars">
              {excelData.processedData.map((row, index) => (
                <div key={index} className="chart-bar-group">
                  <div className="chart-year">{row.year}</div>
                  <div className="chart-bars-container">
                    <div
                      className="chart-bar revenue"
                      style={{ height: `${Math.max(row.revenue / 1000000 * 100, 5)}px` }}
                      title={`Revenue: ${formatCurrency(row.revenue)}`}
                    ></div>
                    <div
                      className="chart-bar cost"
                      style={{ height: `${Math.max(row.cost / 1000000 * 100, 5)}px` }}
                      title={`Cost: ${formatCurrency(row.cost)}`}
                    ></div>
                    <div
                      className={`chart-bar cashflow ${row.netCashFlow >= 0 ? 'positive' : 'negative'}`}
                      style={{ height: `${Math.max(Math.abs(row.netCashFlow) / 1000000 * 100, 5)}px` }}
                      title={`Net Cash Flow: ${formatCurrency(row.netCashFlow)}`}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
            <div className="chart-legend">
              <div className="legend-item">
                <div className="legend-color revenue"></div>
                <span>Revenue</span>
              </div>
              <div className="legend-item">
                <div className="legend-color cost"></div>
                <span>Cost</span>
              </div>
              <div className="legend-item">
                <div className="legend-color cashflow"></div>
                <span>Net Cash Flow</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )

  const renderScenariosView = () => (
    <div className="scenarios-view">
      <h3>🎯 Scenario Analysis & Sensitivity Testing</h3>

      <div className="scenarios-grid">
        {Object.entries(excelData.scenarios).map(([scenarioKey, scenario]) => (
          <div
            key={scenarioKey}
            className={`scenario-card ${excelData.currentScenario === scenarioKey ? 'active' : ''}`}
            onClick={() => setExcelData(prev => ({ ...prev, currentScenario: scenarioKey }))}
          >
            <div className="scenario-header">
              <h4>{scenario.name || scenarioKey.charAt(0).toUpperCase() + scenarioKey.slice(1)}</h4>
              <div className="scenario-multiplier">
                {Math.round(scenario.multiplier * 100)}% of base case
              </div>
            </div>

            <div className="scenario-results">
              <div className="scenario-metric">
                <span className="metric-label">IRR:</span>
                <span className="metric-value">{scenario.results.irr || 0}%</span>
              </div>
              <div className="scenario-metric">
                <span className="metric-label">NPV:</span>
                <span className="metric-value">{formatCurrency(scenario.results.npv || 0)}</span>
              </div>
              <div className="scenario-metric">
                <span className="metric-label">Payback:</span>
                <span className="metric-value">{scenario.results.paybackPeriod || 0} years</span>
              </div>
              <div className="scenario-metric">
                <span className="metric-label">ROI:</span>
                <span className="metric-value">{scenario.results.roi || 0}%</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="sensitivity-controls">
        <h4>🎛️ Sensitivity Controls</h4>
        <div className="controls-grid">
          <div className="control-group">
            <label>Optimistic Scenario Multiplier</label>
            <input
              type="range"
              min="1.0"
              max="2.0"
              step="0.1"
              value={excelData.scenarios.optimistic.multiplier}
              onChange={(e) => {
                const newMultiplier = parseFloat(e.target.value)
                setExcelData(prev => ({
                  ...prev,
                  scenarios: {
                    ...prev.scenarios,
                    optimistic: { ...prev.scenarios.optimistic, multiplier: newMultiplier }
                  }
                }))
                if (excelData.processedData.length > 0) {
                  performFinancialAnalysis(excelData.processedData)
                }
              }}
              className="range-input"
            />
            <span className="range-value">{Math.round(excelData.scenarios.optimistic.multiplier * 100)}%</span>
          </div>

          <div className="control-group">
            <label>Pessimistic Scenario Multiplier</label>
            <input
              type="range"
              min="0.3"
              max="1.0"
              step="0.1"
              value={excelData.scenarios.pessimistic.multiplier}
              onChange={(e) => {
                const newMultiplier = parseFloat(e.target.value)
                setExcelData(prev => ({
                  ...prev,
                  scenarios: {
                    ...prev.scenarios,
                    pessimistic: { ...prev.scenarios.pessimistic, multiplier: newMultiplier }
                  }
                }))
                if (excelData.processedData.length > 0) {
                  performFinancialAnalysis(excelData.processedData)
                }
              }}
              className="range-input"
            />
            <span className="range-value">{Math.round(excelData.scenarios.pessimistic.multiplier * 100)}%</span>
          </div>
        </div>
      </div>
    </div>
  )

  const renderAugmentView = () => (
    <div className="augment-view">
      <div className="augment-hero">
        <div className="augment-logo">
          <div className="logo-icon">🤖</div>
          <div className="logo-text">
            <h2>Augment AI</h2>
            <p>Your Intelligent Coding Assistant</p>
          </div>
        </div>
      </div>

      <div className="augment-content">
        <div className="augment-section">
          <h3>🚀 Revolutionize Your Development with Augment AI</h3>
          <p className="augment-description">
            Experience the future of software development with Augment AI - the world's most advanced
            AI-powered coding assistant that understands your codebase, accelerates development,
            and delivers enterprise-grade solutions.
          </p>
        </div>

        <div className="features-grid">
          <div className="feature-card">
            <div className="feature-icon">🧠</div>
            <h4>Intelligent Code Generation</h4>
            <p>Generate complex financial models, Excel integrations, and business logic with natural language prompts.</p>
            <ul>
              <li>✅ Advanced financial calculations (IRR, NPV, Payback)</li>
              <li>✅ Excel import/export functionality</li>
              <li>✅ Real-time data validation and processing</li>
              <li>✅ CRUD operations with audit trails</li>
            </ul>
          </div>

          <div className="feature-card">
            <div className="feature-icon">⚡</div>
            <h4>Lightning-Fast Development</h4>
            <p>Reduce development time by 10x with AI-powered code completion and generation.</p>
            <ul>
              <li>✅ Instant component creation</li>
              <li>✅ Automated testing and validation</li>
              <li>✅ Professional UI/UX generation</li>
              <li>✅ Cross-platform compatibility</li>
            </ul>
          </div>

          <div className="feature-card">
            <div className="feature-icon">🎯</div>
            <h4>Enterprise-Ready Solutions</h4>
            <p>Build production-ready applications with enterprise-grade security and scalability.</p>
            <ul>
              <li>✅ Compliance-ready audit trails</li>
              <li>✅ Advanced data security</li>
              <li>✅ Scalable architecture patterns</li>
              <li>✅ Professional documentation</li>
            </ul>
          </div>

          <div className="feature-card">
            <div className="feature-icon">🔧</div>
            <h4>Seamless Integration</h4>
            <p>Integrate with your existing tools and workflows without disruption.</p>
            <ul>
              <li>✅ Excel/CSV data processing</li>
              <li>✅ Database connectivity</li>
              <li>✅ API integrations</li>
              <li>✅ Cloud deployment ready</li>
            </ul>
          </div>
        </div>

        <div className="augment-demo-section">
          <h3>🎯 See Augment AI in Action</h3>
          <p>This entire Excel Financial Analysis system was built using Augment AI, demonstrating:</p>

          <div className="demo-features">
            <div className="demo-feature">
              <span className="demo-icon">📊</span>
              <div className="demo-content">
                <strong>Excel Integration</strong>
                <p>Complete Excel import/export with data validation and error handling</p>
              </div>
            </div>

            <div className="demo-feature">
              <span className="demo-icon">🧮</span>
              <div className="demo-content">
                <strong>Financial Calculations</strong>
                <p>Advanced IRR, NPV, and payback period calculations with real-time updates</p>
              </div>
            </div>

            <div className="demo-feature">
              <span className="demo-icon">🔄</span>
              <div className="demo-content">
                <strong>CRUD Operations</strong>
                <p>Full create, read, update, delete functionality with data persistence</p>
              </div>
            </div>

            <div className="demo-feature">
              <span className="demo-icon">📈</span>
              <div className="demo-content">
                <strong>Scenario Analysis</strong>
                <p>Dynamic sensitivity analysis with multiple scenario modeling</p>
              </div>
            </div>
          </div>
        </div>

        <div className="augment-cta">
          <h3>🚀 Ready to Transform Your Development?</h3>
          <p>Join thousands of developers who are already building faster, smarter, and better with Augment AI.</p>

          <div className="cta-buttons">
            <button className="btn btn-primary btn-large">
              🤖 Try Augment AI Free
            </button>
            <button className="btn btn-secondary btn-large">
              📚 View Documentation
            </button>
          </div>

          <div className="augment-stats">
            <div className="stat-item">
              <div className="stat-value">10x</div>
              <div className="stat-label">Faster Development</div>
            </div>
            <div className="stat-item">
              <div className="stat-value">99%</div>
              <div className="stat-label">Code Accuracy</div>
            </div>
            <div className="stat-item">
              <div className="stat-value">50K+</div>
              <div className="stat-label">Developers</div>
            </div>
            <div className="stat-item">
              <div className="stat-value">24/7</div>
              <div className="stat-label">AI Support</div>
            </div>
          </div>
        </div>

        <div className="augment-testimonial">
          <div className="testimonial-content">
            <blockquote>
              "Augment AI transformed our development process. What used to take weeks now takes hours.
              The financial modeling system it generated is production-ready and exceeds our expectations."
            </blockquote>
            <div className="testimonial-author">
              <strong>Sarah Johnson</strong>
              <span>Senior Developer, FinTech Solutions</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <div className="augment-excel-analysis">
      <div className="analysis-header">
        <h2>🤖 Augment AI-Powered Excel Financial Analysis</h2>
        <div className="header-actions">
          {excelData.processedData.length > 0 && (
            <button className="btn btn-success" onClick={handleExportToExcel}>
              📤 Export to Excel
            </button>
          )}
        </div>
      </div>

      <div className="analysis-navigation">
        {views.map((view) => (
          <button
            key={view.id}
            className={`nav-btn ${activeView === view.id ? 'active' : ''}`}
            onClick={() => setActiveView(view.id)}
          >
            <span className="nav-icon">{view.icon}</span>
            <span className="nav-label">{view.label}</span>
          </button>
        ))}
      </div>

      <div className="analysis-content">
        {activeView === 'import' && renderImportView()}
        {activeView === 'data' && renderDataView()}
        {activeView === 'analysis' && renderAnalysisView()}
        {activeView === 'scenarios' && renderScenariosView()}
        {activeView === 'augment' && renderAugmentView()}
      </div>
    </div>
  )
}

export default AugmentExcelAnalysis
