import React, { useState } from 'react'

const EnhancedStakeholderFeedback = ({ data, onChange }) => {
  const [feedbackData, setFeedbackData] = useState({
    stakeholders: [
      {
        id: 1,
        name: 'GYANEHS_PANDEY & GYANEHS_PANDEY',
        role: 'Project Sponsor',
        department: 'Executive',
        email: '<EMAIL>',
        phone: '******-0101',
        influence: 'High',
        interest: 'High',
        feedback: 'Strong support for the initiative. Budget allocation approved.',
        rating: 5,
        status: 'Approved',
        lastContact: '2024-03-15',
        nextAction: 'Monthly review meeting',
        priority: 'Critical',
        engagementLevel: 'Champion',
        comments: [
          {
            id: 1,
            date: '2024-03-15',
            author: 'Project Manager',
            comment: 'Initial stakeholder meeting completed. Full support confirmed.',
            type: 'Meeting Note'
          },
          {
            id: 2,
            date: '2024-03-10',
            author: 'Business Analyst',
            comment: 'Budget requirements discussed and approved.',
            type: 'Decision'
          }
        ]
      },
      {
        id: 2,
        name: '<PERSON>',
        role: 'Business Unit Manager',
        department: 'Operations',
        email: '<EMAIL>',
        phone: '******-0102',
        influence: 'High',
        interest: 'High',
        feedback: 'Concerns about implementation timeline. Need more resources.',
        rating: 3,
        status: 'Conditional',
        lastContact: '2024-03-12',
        nextAction: 'Resource planning session',
        priority: 'High',
        engagementLevel: 'Supportive',
        comments: [
          {
            id: 1,
            date: '2024-03-12',
            author: 'Project Manager',
            comment: 'Discussed resource constraints and timeline concerns.',
            type: 'Issue'
          },
          {
            id: 2,
            date: '2024-03-08',
            author: 'Resource Manager',
            comment: 'Additional resources being evaluated for Q2.',
            type: 'Action Item'
          }
        ]
      },
      {
        id: 3,
        name: 'Mike Chen',
        role: 'IT Director',
        department: 'Technology',
        email: '<EMAIL>',
        phone: '******-0103',
        influence: 'Medium',
        interest: 'High',
        feedback: 'Technical feasibility confirmed. Integration challenges identified.',
        rating: 4,
        status: 'Approved',
        lastContact: '2024-03-10',
        nextAction: 'Technical architecture review',
        priority: 'High',
        engagementLevel: 'Supportive',
        comments: [
          {
            id: 1,
            date: '2024-03-10',
            author: 'Technical Lead',
            comment: 'Technical assessment completed. Some integration challenges noted.',
            type: 'Assessment'
          }
        ]
      },
      {
        id: 4,
        name: 'Lisa Rodriguez',
        role: 'Finance Manager',
        department: 'Finance',
        email: '<EMAIL>',
        phone: '******-0104',
        influence: 'High',
        interest: 'Medium',
        feedback: 'ROI projections look promising. Need detailed cost breakdown.',
        rating: 4,
        status: 'Pending Review',
        lastContact: '2024-03-14',
        nextAction: 'Financial model review',
        priority: 'High',
        engagementLevel: 'Neutral',
        comments: [
          {
            id: 1,
            date: '2024-03-14',
            author: 'Financial Analyst',
            comment: 'Initial ROI analysis shows positive returns. Detailed breakdown requested.',
            type: 'Analysis'
          }
        ]
      },
      {
        id: 5,
        name: 'David Park',
        role: 'HR Director',
        department: 'Human Resources',
        email: '<EMAIL>',
        phone: '******-0105',
        influence: 'Medium',
        interest: 'Medium',
        feedback: 'Change management plan needs more detail. Training requirements unclear.',
        rating: 3,
        status: 'Needs Clarification',
        lastContact: '2024-03-11',
        nextAction: 'Change management workshop',
        priority: 'Medium',
        engagementLevel: 'Resistant',
        comments: [
          {
            id: 1,
            date: '2024-03-11',
            author: 'Change Manager',
            comment: 'Concerns raised about employee impact and training needs.',
            type: 'Concern'
          }
        ]
      }
    ],
    feedbackSummary: {
      totalStakeholders: 5,
      approvedCount: 2,
      conditionalCount: 1,
      pendingCount: 1,
      needsClarificationCount: 1,
      averageRating: 3.8,
      lastUpdated: '2024-03-15'
    },
    ...data
  })

  const [activeView, setActiveView] = useState('table')
  const [selectedStakeholder, setSelectedStakeholder] = useState(null)
  const [isEditing, setIsEditing] = useState(false)
  const [isAdding, setIsAdding] = useState(false)
  const [editForm, setEditForm] = useState({})
  const [newComment, setNewComment] = useState({ comment: '', type: 'General' })
  const [filterStatus, setFilterStatus] = useState('All')
  const [sortBy, setSortBy] = useState('name')

  const views = [
    { id: 'table', label: 'Stakeholder Table', icon: '📊' },
    { id: 'details', label: 'Detailed View', icon: '📋' },
    { id: 'analytics', label: 'Analytics', icon: '📈' },
    { id: 'comments', label: 'Comments & Actions', icon: '💬' }
  ]

  const statusOptions = ['Approved', 'Conditional', 'Pending Review', 'Needs Clarification', 'Rejected']
  const influenceOptions = ['High', 'Medium', 'Low']
  const interestOptions = ['High', 'Medium', 'Low']
  const priorityOptions = ['Critical', 'High', 'Medium', 'Low']
  const engagementOptions = ['Champion', 'Supportive', 'Neutral', 'Resistant', 'Opponent']
  const commentTypes = ['General', 'Meeting Note', 'Decision', 'Issue', 'Action Item', 'Assessment', 'Concern', 'Follow-up']

  const getStatusColor = (status) => {
    const colors = {
      'Approved': '#28a745',
      'Conditional': '#ffc107',
      'Pending Review': '#17a2b8',
      'Needs Clarification': '#fd7e14',
      'Rejected': '#dc3545'
    }
    return colors[status] || '#6c757d'
  }

  const getEngagementColor = (level) => {
    const colors = {
      'Champion': '#28a745',
      'Supportive': '#17a2b8',
      'Neutral': '#ffc107',
      'Resistant': '#fd7e14',
      'Opponent': '#dc3545'
    }
    return colors[level] || '#6c757d'
  }

  const getPriorityColor = (priority) => {
    const colors = {
      'Critical': '#dc3545',
      'High': '#fd7e14',
      'Medium': '#ffc107',
      'Low': '#28a745'
    }
    return colors[priority] || '#6c757d'
  }

  const handleAddStakeholder = () => {
    setEditForm({
      name: '',
      role: '',
      department: '',
      email: '',
      phone: '',
      influence: 'Medium',
      interest: 'Medium',
      feedback: '',
      rating: 3,
      status: 'Pending Review',
      lastContact: new Date().toISOString().split('T')[0],
      nextAction: '',
      priority: 'Medium',
      engagementLevel: 'Neutral',
      comments: []
    })
    setIsAdding(true)
    setIsEditing(true)
  }

  const handleEditStakeholder = (stakeholder) => {
    setEditForm({ ...stakeholder })
    setSelectedStakeholder(stakeholder)
    setIsEditing(true)
    setIsAdding(false)
  }

  const handleSaveStakeholder = () => {
    const updatedStakeholders = [...feedbackData.stakeholders]

    if (isAdding) {
      const newId = Math.max(...feedbackData.stakeholders.map(s => s.id)) + 1
      const newStakeholder = { ...editForm, id: newId, comments: [] }
      updatedStakeholders.push(newStakeholder)
    } else {
      const index = updatedStakeholders.findIndex(s => s.id === editForm.id)
      if (index !== -1) {
        updatedStakeholders[index] = { ...editForm }
      }
    }

    const updatedData = {
      ...feedbackData,
      stakeholders: updatedStakeholders,
      feedbackSummary: {
        ...feedbackData.feedbackSummary,
        totalStakeholders: updatedStakeholders.length,
        lastUpdated: new Date().toISOString().split('T')[0]
      }
    }

    setFeedbackData(updatedData)
    if (onChange) onChange(updatedData)

    setIsEditing(false)
    setIsAdding(false)
    setEditForm({})
    setSelectedStakeholder(null)
  }

  const handleDeleteStakeholder = (id) => {
    if (window.confirm('Are you sure you want to delete this stakeholder?')) {
      const updatedStakeholders = feedbackData.stakeholders.filter(s => s.id !== id)
      const updatedData = {
        ...feedbackData,
        stakeholders: updatedStakeholders,
        feedbackSummary: {
          ...feedbackData.feedbackSummary,
          totalStakeholders: updatedStakeholders.length,
          lastUpdated: new Date().toISOString().split('T')[0]
        }
      }

      setFeedbackData(updatedData)
      if (onChange) onChange(updatedData)

      if (selectedStakeholder && selectedStakeholder.id === id) {
        setSelectedStakeholder(null)
      }
    }
  }

  const handleAddComment = (stakeholderId) => {
    if (!newComment.comment.trim()) return

    const updatedStakeholders = feedbackData.stakeholders.map(stakeholder => {
      if (stakeholder.id === stakeholderId) {
        const newCommentObj = {
          id: (stakeholder.comments?.length || 0) + 1,
          date: new Date().toISOString().split('T')[0],
          author: 'Current User', // This would come from user context
          comment: newComment.comment,
          type: newComment.type
        }
        return {
          ...stakeholder,
          comments: [...(stakeholder.comments || []), newCommentObj]
        }
      }
      return stakeholder
    })

    const updatedData = { ...feedbackData, stakeholders: updatedStakeholders }
    setFeedbackData(updatedData)
    if (onChange) onChange(updatedData)

    setNewComment({ comment: '', type: 'General' })
  }

  const handleDeleteComment = (stakeholderId, commentId) => {
    if (window.confirm('Are you sure you want to delete this comment?')) {
      const updatedStakeholders = feedbackData.stakeholders.map(stakeholder => {
        if (stakeholder.id === stakeholderId) {
          return {
            ...stakeholder,
            comments: stakeholder.comments?.filter(c => c.id !== commentId) || []
          }
        }
        return stakeholder
      })

      const updatedData = { ...feedbackData, stakeholders: updatedStakeholders }
      setFeedbackData(updatedData)
      if (onChange) onChange(updatedData)
    }
  }

  const getFilteredStakeholders = () => {
    let filtered = feedbackData.stakeholders

    if (filterStatus !== 'All') {
      filtered = filtered.filter(s => s.status === filterStatus)
    }

    // Sort stakeholders
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name)
        case 'rating':
          return b.rating - a.rating
        case 'lastContact':
          return new Date(b.lastContact) - new Date(a.lastContact)
        case 'priority':
          const priorityOrder = { 'Critical': 4, 'High': 3, 'Medium': 2, 'Low': 1 }
          return priorityOrder[b.priority] - priorityOrder[a.priority]
        default:
          return 0
      }
    })

    return filtered
  }

  return (
    <div className="enhanced-stakeholder-feedback">
      <div className="feedback-header">
        <h2>👥 Multi-Stakeholder Feedback Management</h2>
        <div className="header-actions">
          <button className="btn btn-primary" onClick={handleAddStakeholder}>
            ➕ Add Stakeholder
          </button>
        </div>
      </div>

      <div className="feedback-summary-cards">
        <div className="summary-card">
          <div className="summary-icon">👥</div>
          <div className="summary-content">
            <div className="summary-value">{feedbackData.feedbackSummary.totalStakeholders}</div>
            <div className="summary-label">Total Stakeholders</div>
          </div>
        </div>
        <div className="summary-card">
          <div className="summary-icon">✅</div>
          <div className="summary-content">
            <div className="summary-value">{feedbackData.feedbackSummary.approvedCount}</div>
            <div className="summary-label">Approved</div>
          </div>
        </div>
        <div className="summary-card">
          <div className="summary-icon">⭐</div>
          <div className="summary-content">
            <div className="summary-value">{feedbackData.feedbackSummary.averageRating.toFixed(1)}</div>
            <div className="summary-label">Avg Rating</div>
          </div>
        </div>
        <div className="summary-card">
          <div className="summary-icon">📅</div>
          <div className="summary-content">
            <div className="summary-value">{feedbackData.feedbackSummary.lastUpdated}</div>
            <div className="summary-label">Last Updated</div>
          </div>
        </div>
      </div>

      <div className="feedback-navigation">
        {views.map((view) => (
          <button
            key={view.id}
            className={`nav-btn ${activeView === view.id ? 'active' : ''}`}
            onClick={() => setActiveView(view.id)}
          >
            <span className="nav-icon">{view.icon}</span>
            <span className="nav-label">{view.label}</span>
          </button>
        ))}
      </div>

      <div className="feedback-content">
        {activeView === 'table' && (
          <div className="table-view">
            <div className="table-controls">
              <div className="filter-controls">
                <label>Filter by Status:</label>
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="form-select"
                >
                  <option value="All">All Statuses</option>
                  {statusOptions.map(status => (
                    <option key={status} value={status}>{status}</option>
                  ))}
                </select>
              </div>
              <div className="sort-controls">
                <label>Sort by:</label>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="form-select"
                >
                  <option value="name">Name</option>
                  <option value="rating">Rating</option>
                  <option value="lastContact">Last Contact</option>
                  <option value="priority">Priority</option>
                </select>
              </div>
            </div>

            <div className="stakeholder-table-container">
              <table className="stakeholder-table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Role</th>
                    <th>Department</th>
                    <th>Status</th>
                    <th>Rating</th>
                    <th>Priority</th>
                    <th>Engagement</th>
                    <th>Last Contact</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {getFilteredStakeholders().map((stakeholder) => (
                    <tr key={stakeholder.id}>
                      <td>
                        <div className="stakeholder-name">
                          <strong>{stakeholder.name}</strong>
                          <div className="stakeholder-email">{stakeholder.email}</div>
                        </div>
                      </td>
                      <td>{stakeholder.role}</td>
                      <td>{stakeholder.department}</td>
                      <td>
                        <span
                          className="status-badge"
                          style={{ backgroundColor: getStatusColor(stakeholder.status) }}
                        >
                          {stakeholder.status}
                        </span>
                      </td>
                      <td>
                        <div className="rating-display">
                          <span className="rating-value">{stakeholder.rating}</span>
                          <div className="rating-stars">
                            {'★'.repeat(stakeholder.rating)}{'☆'.repeat(5 - stakeholder.rating)}
                          </div>
                        </div>
                      </td>
                      <td>
                        <span
                          className="priority-badge"
                          style={{ backgroundColor: getPriorityColor(stakeholder.priority) }}
                        >
                          {stakeholder.priority}
                        </span>
                      </td>
                      <td>
                        <span
                          className="engagement-badge"
                          style={{ backgroundColor: getEngagementColor(stakeholder.engagementLevel) }}
                        >
                          {stakeholder.engagementLevel}
                        </span>
                      </td>
                      <td>{stakeholder.lastContact}</td>
                      <td>
                        <div className="action-buttons">
                          <button
                            className="btn btn-sm btn-info"
                            onClick={() => {
                              setSelectedStakeholder(stakeholder)
                              setActiveView('details')
                            }}
                          >
                            👁️
                          </button>
                          <button
                            className="btn btn-sm btn-warning"
                            onClick={() => handleEditStakeholder(stakeholder)}
                          >
                            ✏️
                          </button>
                          <button
                            className="btn btn-sm btn-danger"
                            onClick={() => handleDeleteStakeholder(stakeholder.id)}
                          >
                            🗑️
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {activeView === 'details' && selectedStakeholder && (
          <div className="details-view">
            <div className="stakeholder-details-card">
              <div className="details-header">
                <h3>{selectedStakeholder.name}</h3>
                <div className="header-badges">
                  <span
                    className="status-badge"
                    style={{ backgroundColor: getStatusColor(selectedStakeholder.status) }}
                  >
                    {selectedStakeholder.status}
                  </span>
                  <span
                    className="priority-badge"
                    style={{ backgroundColor: getPriorityColor(selectedStakeholder.priority) }}
                  >
                    {selectedStakeholder.priority}
                  </span>
                </div>
              </div>

              <div className="details-content">
                <div className="details-section">
                  <h4>📋 Basic Information</h4>
                  <div className="info-grid">
                    <div className="info-item">
                      <label>Role:</label>
                      <span>{selectedStakeholder.role}</span>
                    </div>
                    <div className="info-item">
                      <label>Department:</label>
                      <span>{selectedStakeholder.department}</span>
                    </div>
                    <div className="info-item">
                      <label>Email:</label>
                      <span>{selectedStakeholder.email}</span>
                    </div>
                    <div className="info-item">
                      <label>Phone:</label>
                      <span>{selectedStakeholder.phone}</span>
                    </div>
                    <div className="info-item">
                      <label>Influence:</label>
                      <span>{selectedStakeholder.influence}</span>
                    </div>
                    <div className="info-item">
                      <label>Interest:</label>
                      <span>{selectedStakeholder.interest}</span>
                    </div>
                  </div>
                </div>

                <div className="details-section">
                  <h4>⭐ Feedback & Rating</h4>
                  <div className="feedback-content">
                    <div className="rating-display-large">
                      <span className="rating-value-large">{selectedStakeholder.rating}</span>
                      <div className="rating-stars-large">
                        {'★'.repeat(selectedStakeholder.rating)}{'☆'.repeat(5 - selectedStakeholder.rating)}
                      </div>
                    </div>
                    <div className="feedback-text">
                      <p>{selectedStakeholder.feedback}</p>
                    </div>
                  </div>
                </div>

                <div className="details-section">
                  <h4>🎯 Engagement Information</h4>
                  <div className="info-grid">
                    <div className="info-item">
                      <label>Engagement Level:</label>
                      <span
                        className="engagement-badge"
                        style={{ backgroundColor: getEngagementColor(selectedStakeholder.engagementLevel) }}
                      >
                        {selectedStakeholder.engagementLevel}
                      </span>
                    </div>
                    <div className="info-item">
                      <label>Last Contact:</label>
                      <span>{selectedStakeholder.lastContact}</span>
                    </div>
                    <div className="info-item">
                      <label>Next Action:</label>
                      <span>{selectedStakeholder.nextAction}</span>
                    </div>
                  </div>
                </div>

                <div className="details-section">
                  <h4>💬 Comments & Notes</h4>
                  <div className="comments-section">
                    <div className="add-comment">
                      <div className="comment-form">
                        <div className="form-row">
                          <select
                            value={newComment.type}
                            onChange={(e) => setNewComment({...newComment, type: e.target.value})}
                            className="form-select"
                          >
                            {commentTypes.map(type => (
                              <option key={type} value={type}>{type}</option>
                            ))}
                          </select>
                        </div>
                        <div className="form-row">
                          <textarea
                            value={newComment.comment}
                            onChange={(e) => setNewComment({...newComment, comment: e.target.value})}
                            placeholder="Add a comment or note..."
                            className="form-textarea"
                            rows="3"
                          />
                        </div>
                        <button
                          className="btn btn-primary"
                          onClick={() => handleAddComment(selectedStakeholder.id)}
                        >
                          💬 Add Comment
                        </button>
                      </div>
                    </div>

                    <div className="comments-list">
                      {selectedStakeholder.comments?.map((comment) => (
                        <div key={comment.id} className="comment-item">
                          <div className="comment-header">
                            <div className="comment-meta">
                              <span className="comment-author">{comment.author}</span>
                              <span className="comment-date">{comment.date}</span>
                              <span
                                className="comment-type"
                                style={{ backgroundColor: getStatusColor(comment.type) }}
                              >
                                {comment.type}
                              </span>
                            </div>
                            <button
                              className="btn btn-sm btn-danger"
                              onClick={() => handleDeleteComment(selectedStakeholder.id, comment.id)}
                            >
                              🗑️
                            </button>
                          </div>
                          <div className="comment-content">
                            {comment.comment}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeView === 'analytics' && (
          <div className="analytics-view">
            <div className="analytics-grid">
              <div className="analytics-card">
                <h4>📊 Status Distribution</h4>
                <div className="status-chart">
                  {statusOptions.map(status => {
                    const count = feedbackData.stakeholders.filter(s => s.status === status).length
                    const percentage = (count / feedbackData.stakeholders.length * 100).toFixed(1)
                    return (
                      <div key={status} className="status-bar">
                        <div className="status-label">
                          <span>{status}</span>
                          <span>{count} ({percentage}%)</span>
                        </div>
                        <div className="progress-bar">
                          <div
                            className="progress-fill"
                            style={{
                              width: `${percentage}%`,
                              backgroundColor: getStatusColor(status)
                            }}
                          />
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>

              <div className="analytics-card">
                <h4>🎯 Engagement Levels</h4>
                <div className="engagement-chart">
                  {engagementOptions.map(level => {
                    const count = feedbackData.stakeholders.filter(s => s.engagementLevel === level).length
                    const percentage = (count / feedbackData.stakeholders.length * 100).toFixed(1)
                    return (
                      <div key={level} className="engagement-bar">
                        <div className="engagement-label">
                          <span>{level}</span>
                          <span>{count} ({percentage}%)</span>
                        </div>
                        <div className="progress-bar">
                          <div
                            className="progress-fill"
                            style={{
                              width: `${percentage}%`,
                              backgroundColor: getEngagementColor(level)
                            }}
                          />
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>

              <div className="analytics-card">
                <h4>⭐ Rating Distribution</h4>
                <div className="rating-chart">
                  {[5, 4, 3, 2, 1].map(rating => {
                    const count = feedbackData.stakeholders.filter(s => s.rating === rating).length
                    const percentage = (count / feedbackData.stakeholders.length * 100).toFixed(1)
                    return (
                      <div key={rating} className="rating-bar">
                        <div className="rating-label">
                          <span>{rating} ★</span>
                          <span>{count} ({percentage}%)</span>
                        </div>
                        <div className="progress-bar">
                          <div
                            className="progress-fill"
                            style={{
                              width: `${percentage}%`,
                              backgroundColor: rating >= 4 ? '#28a745' : rating >= 3 ? '#ffc107' : '#dc3545'
                            }}
                          />
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>

              <div className="analytics-card">
                <h4>🔥 Priority Matrix</h4>
                <div className="priority-chart">
                  {priorityOptions.map(priority => {
                    const count = feedbackData.stakeholders.filter(s => s.priority === priority).length
                    const percentage = (count / feedbackData.stakeholders.length * 100).toFixed(1)
                    return (
                      <div key={priority} className="priority-bar">
                        <div className="priority-label">
                          <span>{priority}</span>
                          <span>{count} ({percentage}%)</span>
                        </div>
                        <div className="progress-bar">
                          <div
                            className="progress-fill"
                            style={{
                              width: `${percentage}%`,
                              backgroundColor: getPriorityColor(priority)
                            }}
                          />
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>
            </div>
          </div>
        )}

        {activeView === 'comments' && (
          <div className="comments-view">
            <h3>💬 All Comments & Actions</h3>
            <div className="all-comments">
              {feedbackData.stakeholders.map(stakeholder => (
                <div key={stakeholder.id} className="stakeholder-comments">
                  <div className="stakeholder-header">
                    <h4>{stakeholder.name} - {stakeholder.role}</h4>
                    <span className="comment-count">
                      {stakeholder.comments?.length || 0} comments
                    </span>
                  </div>
                  <div className="comments-list">
                    {stakeholder.comments?.map(comment => (
                      <div key={comment.id} className="comment-item">
                        <div className="comment-header">
                          <div className="comment-meta">
                            <span className="comment-author">{comment.author}</span>
                            <span className="comment-date">{comment.date}</span>
                            <span
                              className="comment-type"
                              style={{ backgroundColor: getStatusColor(comment.type) }}
                            >
                              {comment.type}
                            </span>
                          </div>
                        </div>
                        <div className="comment-content">
                          {comment.comment}
                        </div>
                      </div>
                    )) || <p className="no-comments">No comments yet</p>}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Edit/Add Modal */}
      {isEditing && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>{isAdding ? '➕ Add New Stakeholder' : '✏️ Edit Stakeholder'}</h3>
              <button
                className="btn btn-close"
                onClick={() => {
                  setIsEditing(false)
                  setIsAdding(false)
                  setEditForm({})
                }}
              >
                ✕
              </button>
            </div>
            <div className="modal-body">
              <div className="form-grid">
                <div className="form-group">
                  <label>Name *</label>
                  <input
                    type="text"
                    value={editForm.name || ''}
                    onChange={(e) => setEditForm({...editForm, name: e.target.value})}
                    className="form-input"
                    required
                  />
                </div>
                <div className="form-group">
                  <label>Role *</label>
                  <input
                    type="text"
                    value={editForm.role || ''}
                    onChange={(e) => setEditForm({...editForm, role: e.target.value})}
                    className="form-input"
                    required
                  />
                </div>
                <div className="form-group">
                  <label>Department</label>
                  <input
                    type="text"
                    value={editForm.department || ''}
                    onChange={(e) => setEditForm({...editForm, department: e.target.value})}
                    className="form-input"
                  />
                </div>
                <div className="form-group">
                  <label>Email</label>
                  <input
                    type="email"
                    value={editForm.email || ''}
                    onChange={(e) => setEditForm({...editForm, email: e.target.value})}
                    className="form-input"
                  />
                </div>
                <div className="form-group">
                  <label>Phone</label>
                  <input
                    type="tel"
                    value={editForm.phone || ''}
                    onChange={(e) => setEditForm({...editForm, phone: e.target.value})}
                    className="form-input"
                  />
                </div>
                <div className="form-group">
                  <label>Influence</label>
                  <select
                    value={editForm.influence || 'Medium'}
                    onChange={(e) => setEditForm({...editForm, influence: e.target.value})}
                    className="form-select"
                  >
                    {influenceOptions.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                </div>
                <div className="form-group">
                  <label>Interest</label>
                  <select
                    value={editForm.interest || 'Medium'}
                    onChange={(e) => setEditForm({...editForm, interest: e.target.value})}
                    className="form-select"
                  >
                    {interestOptions.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                </div>
                <div className="form-group">
                  <label>Status</label>
                  <select
                    value={editForm.status || 'Pending Review'}
                    onChange={(e) => setEditForm({...editForm, status: e.target.value})}
                    className="form-select"
                  >
                    {statusOptions.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                </div>
                <div className="form-group">
                  <label>Priority</label>
                  <select
                    value={editForm.priority || 'Medium'}
                    onChange={(e) => setEditForm({...editForm, priority: e.target.value})}
                    className="form-select"
                  >
                    {priorityOptions.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                </div>
                <div className="form-group">
                  <label>Engagement Level</label>
                  <select
                    value={editForm.engagementLevel || 'Neutral'}
                    onChange={(e) => setEditForm({...editForm, engagementLevel: e.target.value})}
                    className="form-select"
                  >
                    {engagementOptions.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                </div>
                <div className="form-group">
                  <label>Rating (1-5)</label>
                  <input
                    type="number"
                    min="1"
                    max="5"
                    value={editForm.rating || 3}
                    onChange={(e) => setEditForm({...editForm, rating: parseInt(e.target.value)})}
                    className="form-input"
                  />
                </div>
                <div className="form-group">
                  <label>Last Contact</label>
                  <input
                    type="date"
                    value={editForm.lastContact || ''}
                    onChange={(e) => setEditForm({...editForm, lastContact: e.target.value})}
                    className="form-input"
                  />
                </div>
                <div className="form-group full-width">
                  <label>Next Action</label>
                  <input
                    type="text"
                    value={editForm.nextAction || ''}
                    onChange={(e) => setEditForm({...editForm, nextAction: e.target.value})}
                    className="form-input"
                    placeholder="Next planned action or meeting"
                  />
                </div>
                <div className="form-group full-width">
                  <label>Feedback</label>
                  <textarea
                    value={editForm.feedback || ''}
                    onChange={(e) => setEditForm({...editForm, feedback: e.target.value})}
                    className="form-textarea"
                    rows="4"
                    placeholder="Stakeholder feedback and comments"
                  />
                </div>
              </div>
            </div>
            <div className="modal-footer">
              <button
                className="btn btn-secondary"
                onClick={() => {
                  setIsEditing(false)
                  setIsAdding(false)
                  setEditForm({})
                }}
              >
                Cancel
              </button>
              <button
                className="btn btn-primary"
                onClick={handleSaveStakeholder}
                disabled={!editForm.name || !editForm.role}
              >
                {isAdding ? 'Add Stakeholder' : 'Save Changes'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default EnhancedStakeholderFeedback
