const express = require('express')
const mongoose = require('mongoose')
const cors = require('cors')
const helmet = require('helmet')
const rateLimit = require('express-rate-limit')
require('dotenv').config()

const app = express()
const PORT = process.env.PORT || 5000

// MongoDB Connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/mybc'
let isMongoConnected = false

// Try to connect to MongoDB, but don't crash if it fails
mongoose.connect(MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
  serverSelectionTimeoutMS: 5000, // Timeout after 5s instead of 30s
}).then(() => {
  isMongoConnected = true
  console.log('✅ Connected to MongoDB:', MONGODB_URI)
}).catch(err => {
  console.warn('⚠️ MongoDB connection failed:', err.message)
  console.log('📝 Server will continue with in-memory storage')
  isMongoConnected = false
})

const db = mongoose.connection
db.on('error', (err) => {
  console.warn('⚠️ MongoDB connection error:', err.message)
  isMongoConnected = false
})
db.once('open', () => {
  isMongoConnected = true
  console.log('✅ MongoDB connection established')
})

// Middleware
app.use(helmet()) // Security headers

// Enhanced CORS configuration
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true)

    const allowedOrigins = [
      'http://localhost:3000',
      'http://127.0.0.1:3000',
      process.env.FRONTEND_URL
    ].filter(Boolean)

    if (allowedOrigins.includes(origin)) {
      callback(null, true)
    } else {
      console.log('CORS blocked origin:', origin)
      callback(new Error('Not allowed by CORS'))
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  optionsSuccessStatus: 200 // Some legacy browsers choke on 204
}

app.use(cors(corsOptions))
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true }))

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
})
app.use('/api/', limiter)

// Cost Schema
const costSchema = new mongoose.Schema({
  name: { type: String, required: true, trim: true },
  category: { type: String, required: true, enum: ['Personnel', 'Equipment', 'Software', 'Infrastructure', 'Marketing', 'Operations', 'Other'] },
  amount: { type: Number, required: true, min: 0 },
  type: { type: String, required: true, enum: ['fixed', 'variable', 'one-time'] },
  description: { type: String, trim: true },
  year: { type: Number, required: true, min: 2020, max: 2030 },
  frequency: { type: String, required: true, enum: ['annual', 'monthly', 'quarterly', 'one-time'] },
  projectId: { type: String, default: 'default' },
  createdBy: { type: String, default: 'system' },
  tags: [String],
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true
})

// Sales Schema
const salesSchema = new mongoose.Schema({
  name: { type: String, required: true, trim: true },
  category: { type: String, required: true, enum: ['Product Sales', 'Service Revenue', 'Licensing', 'Subscriptions', 'Consulting', 'Other'] },
  revenue: { type: Number, required: true, min: 0 },
  units: { type: Number, required: true, min: 0 },
  unitPrice: { type: Number, required: true, min: 0 },
  description: { type: String, trim: true },
  year: { type: Number, required: true, min: 2020, max: 2030 },
  quarter: { type: String, required: true, enum: ['Q1', 'Q2', 'Q3', 'Q4'] },
  region: { type: String, default: 'Global' },
  projectId: { type: String, default: 'default' },
  createdBy: { type: String, default: 'system' },
  tags: [String],
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true
})

// Financial Index Schema
const financialIndexSchema = new mongoose.Schema({
  projectId: { type: String, required: true, default: 'default' },
  totalCosts: { type: Number, default: 0 },
  totalRevenue: { type: Number, default: 0 },
  netProfit: { type: Number, default: 0 },
  roi: { type: Number, default: 0 },
  npv: { type: Number, default: 0 },
  irr: { type: Number, default: 0 },
  paybackPeriod: { type: Number, default: 0 },
  profitabilityIndex: { type: Number, default: 0 },
  breakEvenPoint: { type: Number, default: 0 },
  calculatedAt: { type: Date, default: Date.now }
}, {
  timestamps: true
})

// Business Case Schema
const businessCaseSchema = new mongoose.Schema({
  programInfo: {
    programName: { type: String, required: true, trim: true },
    programManager: { type: String, trim: true },
    sponsor: { type: String, trim: true },
    strategicAlignment: { type: String, enum: ['High', 'Medium', 'Low'], default: 'Medium' },
    businessDriver: { type: String, enum: ['Market Expansion', 'Cost Reduction', 'Regulatory Compliance', 'Digital Transformation', 'Customer Experience', 'Strategic Initiative'], default: 'Strategic Initiative' },
    investmentCategory: { type: String, enum: ['Strategic', 'Operational', 'Mandatory', 'Infrastructure'], default: 'Strategic' },
    totalBudget: { type: Number, min: 0, default: 0 },
    duration: { type: Number, min: 1, default: 12 },
    startDate: { type: String },
    endDate: { type: String }
  },
  governance: {
    steeringCommittee: [String],
    decisionGates: [{
      gate: String,
      name: String,
      status: { type: String, enum: ['Pending', 'In Review', 'Approved', 'Rejected'], default: 'Pending' },
      date: String
    }],
    riskTolerance: { type: String, enum: ['Low', 'Medium', 'High'], default: 'Medium' },
    complianceFramework: [String]
  },
  useCases: [{
    id: String,
    name: String,
    priority: { type: String, enum: ['High', 'Medium', 'Low'], default: 'Medium' },
    businessValue: { type: String, enum: ['Very High', 'High', 'Medium', 'Low'], default: 'Medium' },
    complexity: { type: String, enum: ['High', 'Medium', 'Low'], default: 'Medium' },
    budget: { type: Number, min: 0, default: 0 },
    timeline: { type: Number, min: 1, default: 6 },
    dependencies: [String],
    status: { type: String, enum: ['Concept', 'Planning', 'Analysis', 'Development', 'Testing', 'Deployment', 'Complete'], default: 'Concept' },
    roi: { type: Number, default: 0 },
    npv: { type: Number, default: 0 }
  }],
  version: { type: Number, default: 1 },
  lastModified: { type: Date, default: Date.now },
  createdBy: { type: String, default: 'system' },
  isActive: { type: Boolean, default: true },
  generatedContent: {
    businessCaseId: String,
    generatedText: String,
    inputData: Object,
    generatedAt: Date
  }
}, {
  timestamps: true
})

// Models
const Cost = mongoose.model('Cost', costSchema)
const Sales = mongoose.model('Sales', salesSchema)
const FinancialIndex = mongoose.model('FinancialIndex', financialIndexSchema)
const BusinessCase = mongoose.model('BusinessCase', businessCaseSchema)

// In-memory storage fallback
let inMemoryStorage = {
  costs: [],
  sales: [],
  financialIndexes: {},
  businessCases: [],
  nextId: 1
}

// Helper function to generate IDs
const generateId = () => {
  return `mem_${inMemoryStorage.nextId++}_${Date.now()}`
}

// Financial calculation function
const calculateFinancialIndexes = async (projectId = 'default') => {
  try {
    const costs = await Cost.find({ projectId, isActive: true })
    const sales = await Sales.find({ projectId, isActive: true })

    const totalCosts = costs.reduce((sum, cost) => sum + cost.amount, 0)
    const totalRevenue = sales.reduce((sum, sale) => sum + sale.revenue, 0)
    const netProfit = totalRevenue - totalCosts
    const roi = totalCosts > 0 ? ((netProfit / totalCosts) * 100) : 0

    // NPV calculation (5-year projection with 10% discount rate)
    const discountRate = 0.10
    const years = 5
    const annualCashFlow = netProfit / years
    let npv = -totalCosts
    for (let year = 1; year <= years; year++) {
      npv += annualCashFlow / Math.pow(1 + discountRate, year)
    }

    // IRR calculation (simplified)
    const irr = totalCosts > 0 ? ((Math.pow(totalRevenue / totalCosts, 1/years) - 1) * 100) : 0

    // Payback period
    const paybackPeriod = annualCashFlow > 0 ? totalCosts / annualCashFlow : 0

    // Profitability index
    const profitabilityIndex = totalCosts > 0 ? (npv + totalCosts) / totalCosts : 0

    // Break-even point
    const avgUnitPrice = sales.length > 0 ? sales.reduce((sum, sale) => sum + sale.unitPrice, 0) / sales.length : 0
    const breakEvenPoint = avgUnitPrice > 0 ? totalCosts / avgUnitPrice : 0

    const financialData = {
      projectId,
      totalCosts: Math.round(totalCosts),
      totalRevenue: Math.round(totalRevenue),
      netProfit: Math.round(netProfit),
      roi: Math.round(roi * 100) / 100,
      npv: Math.round(npv),
      irr: Math.round(irr * 100) / 100,
      paybackPeriod: Math.round(paybackPeriod * 100) / 100,
      profitabilityIndex: Math.round(profitabilityIndex * 100) / 100,
      breakEvenPoint: Math.round(breakEvenPoint),
      calculatedAt: new Date()
    }

    // Update or create financial index
    await FinancialIndex.findOneAndUpdate(
      { projectId },
      financialData,
      { upsert: true, new: true }
    )

    return financialData
  } catch (error) {
    console.error('Error calculating financial indexes:', error)
    throw error
  }
}

// COST ROUTES

// GET all costs
app.get('/api/costs', async (req, res) => {
  try {
    const { projectId = 'default', category, year } = req.query
    const filter = { projectId, isActive: true }

    if (category) filter.category = category
    if (year) filter.year = parseInt(year)

    const costs = await Cost.find(filter).sort({ createdAt: -1 })
    res.json(costs)
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

// POST new cost
app.post('/api/costs', async (req, res) => {
  try {
    const cost = new Cost(req.body)
    await cost.save()

    // Recalculate financial indexes
    const financialIndexes = await calculateFinancialIndexes(cost.projectId)

    res.status(201).json({ cost, financialIndexes })
  } catch (error) {
    res.status(400).json({ error: error.message })
  }
})

// PUT update cost
app.put('/api/costs/:id', async (req, res) => {
  try {
    const cost = await Cost.findByIdAndUpdate(req.params.id, req.body, { new: true })
    if (!cost) {
      return res.status(404).json({ error: 'Cost not found' })
    }

    // Recalculate financial indexes
    const financialIndexes = await calculateFinancialIndexes(cost.projectId)

    res.json({ cost, financialIndexes })
  } catch (error) {
    res.status(400).json({ error: error.message })
  }
})

// DELETE cost
app.delete('/api/costs/:id', async (req, res) => {
  try {
    const cost = await Cost.findByIdAndUpdate(
      req.params.id,
      { isActive: false },
      { new: true }
    )
    if (!cost) {
      return res.status(404).json({ error: 'Cost not found' })
    }

    // Recalculate financial indexes
    const financialIndexes = await calculateFinancialIndexes(cost.projectId)

    res.json({ message: 'Cost deleted successfully', financialIndexes })
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

// SALES ROUTES

// GET all sales
app.get('/api/sales', async (req, res) => {
  try {
    const { projectId = 'default', category, year, quarter } = req.query
    const filter = { projectId, isActive: true }

    if (category) filter.category = category
    if (year) filter.year = parseInt(year)
    if (quarter) filter.quarter = quarter

    const sales = await Sales.find(filter).sort({ createdAt: -1 })
    res.json(sales)
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

// POST new sales
app.post('/api/sales', async (req, res) => {
  try {
    // Calculate revenue if not provided
    if (!req.body.revenue && req.body.units && req.body.unitPrice) {
      req.body.revenue = req.body.units * req.body.unitPrice
    }

    const sales = new Sales(req.body)
    await sales.save()

    // Recalculate financial indexes
    const financialIndexes = await calculateFinancialIndexes(sales.projectId)

    res.status(201).json({ sales, financialIndexes })
  } catch (error) {
    res.status(400).json({ error: error.message })
  }
})

// PUT update sales
app.put('/api/sales/:id', async (req, res) => {
  try {
    // Calculate revenue if not provided
    if (!req.body.revenue && req.body.units && req.body.unitPrice) {
      req.body.revenue = req.body.units * req.body.unitPrice
    }

    const sales = await Sales.findByIdAndUpdate(req.params.id, req.body, { new: true })
    if (!sales) {
      return res.status(404).json({ error: 'Sales record not found' })
    }

    // Recalculate financial indexes
    const financialIndexes = await calculateFinancialIndexes(sales.projectId)

    res.json({ sales, financialIndexes })
  } catch (error) {
    res.status(400).json({ error: error.message })
  }
})

// DELETE sales
app.delete('/api/sales/:id', async (req, res) => {
  try {
    const sales = await Sales.findByIdAndUpdate(
      req.params.id,
      { isActive: false },
      { new: true }
    )
    if (!sales) {
      return res.status(404).json({ error: 'Sales record not found' })
    }

    // Recalculate financial indexes
    const financialIndexes = await calculateFinancialIndexes(sales.projectId)

    res.json({ message: 'Sales record deleted successfully', financialIndexes })
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

// FINANCIAL INDEX ROUTES

// GET financial indexes
app.get('/api/financial-indexes/:projectId?', async (req, res) => {
  try {
    const projectId = req.params.projectId || 'default'
    let financialIndex = await FinancialIndex.findOne({ projectId })

    if (!financialIndex) {
      // Calculate if not exists
      financialIndex = await calculateFinancialIndexes(projectId)
    }

    res.json(financialIndex)
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

// POST recalculate financial indexes
app.post('/api/financial-indexes/recalculate/:projectId?', async (req, res) => {
  try {
    const projectId = req.params.projectId || 'default'
    const financialIndexes = await calculateFinancialIndexes(projectId)
    res.json(financialIndexes)
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

// BUSINESS CASE ROUTES

// GET all business cases
app.get('/api/business-cases', async (req, res) => {
  try {
    const { isActive = true } = req.query
    const filter = { isActive: isActive === 'true' }

    const businessCases = await BusinessCase.find(filter)
      .sort({ lastModified: -1 })
      .select('programInfo governance version lastModified createdAt')

    res.json(businessCases)
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

// GET single business case
app.get('/api/business-cases/:id', async (req, res) => {
  try {
    const businessCase = await BusinessCase.findById(req.params.id)
    if (!businessCase) {
      return res.status(404).json({ error: 'Business case not found' })
    }
    res.json(businessCase)
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

// POST new business case
app.post('/api/business-cases', async (req, res) => {
  try {
    const businessCase = new BusinessCase({
      ...req.body,
      lastModified: new Date(),
      version: 1
    })

    await businessCase.save()
    res.status(201).json(businessCase)
  } catch (error) {
    res.status(400).json({ error: error.message })
  }
})

// PUT update business case
app.put('/api/business-cases/:id', async (req, res) => {
  try {
    const updateData = {
      ...req.body,
      lastModified: new Date()
    }

    const businessCase = await BusinessCase.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    )

    if (!businessCase) {
      return res.status(404).json({ error: 'Business case not found' })
    }

    res.json(businessCase)
  } catch (error) {
    res.status(400).json({ error: error.message })
  }
})

// DELETE business case (soft delete)
app.delete('/api/business-cases/:id', async (req, res) => {
  try {
    const businessCase = await BusinessCase.findByIdAndUpdate(
      req.params.id,
      {
        isActive: false,
        lastModified: new Date()
      },
      { new: true }
    )

    if (!businessCase) {
      return res.status(404).json({ error: 'Business case not found' })
    }

    res.json({ message: 'Business case deleted successfully' })
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

// POST duplicate business case
app.post('/api/business-cases/:id/duplicate', async (req, res) => {
  try {
    const originalCase = await BusinessCase.findById(req.params.id)
    if (!originalCase) {
      return res.status(404).json({ error: 'Business case not found' })
    }

    const duplicatedCase = new BusinessCase({
      ...originalCase.toObject(),
      _id: undefined,
      programInfo: {
        ...originalCase.programInfo,
        programName: `${originalCase.programInfo.programName} (Copy)`
      },
      version: 1,
      lastModified: new Date(),
      createdAt: undefined,
      updatedAt: undefined
    })

    await duplicatedCase.save()
    res.status(201).json(duplicatedCase)
  } catch (error) {
    res.status(400).json({ error: error.message })
  }
})

// BUSINESS CASE GENERATION API

// POST generate business case
app.post('/api/generateBusinessCase', async (req, res) => {
  try {
    // Validate required fields
    const requiredFields = ['projectTitle', 'problemStatement', 'proposedSolution', 'objectives']
    const errors = []

    for (const field of requiredFields) {
      if (!req.body[field]) {
        errors.push({
          field: field,
          message: `${field} is required.`
        })
      }
    }

    // Validate objectives is an array
    if (req.body.objectives && !Array.isArray(req.body.objectives)) {
      errors.push({
        field: 'objectives',
        message: 'objectives must be an array.'
      })
    }

    if (errors.length > 0) {
      return res.status(400).json({
        businessCaseId: null,
        generatedText: null,
        requestDetails: req.body,
        errors: errors
      })
    }

    // Generate unique business case ID
    const businessCaseId = `bc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Extract and validate input data
    const {
      projectTitle,
      problemStatement,
      proposedSolution,
      executiveSummaryPoints = [],
      objectives,
      scope = {},
      benefits = {},
      costs = {},
      financialProjections = {},
      risksAndMitigation = [],
      assumptions = [],
      timelineMilestones = [],
      stakeholders = [],
      alternativesConsidered = [],
      successMetrics = [],
      companyContext = '',
      targetAudience = 'Executive Board',
      desiredTone = 'Formal',
      outputFormat = 'markdown'
    } = req.body

    // Generate business case using LLM-style template
    const generatedText = await generateBusinessCaseText({
      projectTitle,
      problemStatement,
      proposedSolution,
      executiveSummaryPoints,
      objectives,
      scope,
      benefits,
      costs,
      financialProjections,
      risksAndMitigation,
      assumptions,
      timelineMilestones,
      stakeholders,
      alternativesConsidered,
      successMetrics,
      companyContext,
      targetAudience,
      desiredTone,
      outputFormat
    })

    // Save generated business case to database or in-memory storage
    const businessCaseData = {
      programInfo: {
        programName: projectTitle,
        programManager: '',
        sponsor: '',
        strategicAlignment: 'High',
        businessDriver: 'Strategic Initiative',
        investmentCategory: 'Strategic',
        totalBudget: calculateTotalBudget(costs),
        duration: calculateDuration(timelineMilestones),
        startDate: getStartDate(timelineMilestones),
        endDate: getEndDate(timelineMilestones)
      },
      governance: {
        steeringCommittee: stakeholders.slice(0, 5), // Take first 5 stakeholders
        decisionGates: [
          { gate: 'Gate 0', name: 'Strategic Alignment', status: 'Pending', date: '' },
          { gate: 'Gate 1', name: 'Business Case Approval', status: 'Pending', date: '' },
          { gate: 'Gate 2', name: 'Solution Design', status: 'Pending', date: '' },
          { gate: 'Gate 3', name: 'Implementation', status: 'Pending', date: '' },
          { gate: 'Gate 4', name: 'Benefits Realization', status: 'Pending', date: '' }
        ],
        riskTolerance: 'Medium',
        complianceFramework: ['PMI', 'PGMP']
      },
      useCases: [],
      version: 1,
      lastModified: new Date(),
      generatedContent: {
        businessCaseId,
        generatedText,
        inputData: req.body,
        generatedAt: new Date()
      }
    }

    let savedBusinessCase
    if (isMongoConnected) {
      // Save to MongoDB
      const businessCase = new BusinessCase(businessCaseData)
      savedBusinessCase = await businessCase.save()
    } else {
      // Save to in-memory storage
      businessCaseData._id = generateId()
      businessCaseData.createdAt = new Date()
      businessCaseData.updatedAt = new Date()
      inMemoryStorage.businessCases.push(businessCaseData)
      savedBusinessCase = businessCaseData
    }

    res.status(201).json({
      businessCaseId,
      generatedText,
      requestDetails: req.body,
      errors: [],
      savedBusinessCase: {
        id: savedBusinessCase._id,
        programName: savedBusinessCase.programInfo.programName,
        storage: isMongoConnected ? 'MongoDB' : 'In-Memory'
      }
    })

  } catch (error) {
    console.error('Business case generation error:', error)
    res.status(500).json({
      businessCaseId: null,
      generatedText: null,
      requestDetails: req.body,
      errors: [{
        field: 'general',
        message: 'Internal server error during business case generation.'
      }]
    })
  }
})

// Helper functions for business case generation
const generateBusinessCaseText = async (data) => {
  const {
    projectTitle,
    problemStatement,
    proposedSolution,
    executiveSummaryPoints,
    objectives,
    scope,
    benefits,
    costs,
    financialProjections,
    risksAndMitigation,
    assumptions,
    timelineMilestones,
    stakeholders,
    alternativesConsidered,
    successMetrics,
    companyContext,
    targetAudience,
    desiredTone,
    outputFormat
  } = data

  // Generate comprehensive business case text
  let businessCase = ''

  if (outputFormat === 'markdown') {
    businessCase = `# Business Case: ${projectTitle}

## 1. Executive Summary

${executiveSummaryPoints.length > 0
  ? `This business case presents ${projectTitle}, a strategic initiative designed to address critical business needs and drive organizational growth. Key highlights include:

${executiveSummaryPoints.map(point => `- ${point}`).join('\n')}

The proposed solution offers significant value through improved efficiency, cost reduction, and enhanced competitive positioning.`
  : `This business case presents ${projectTitle}, a strategic initiative designed to address critical business needs and drive organizational growth. The proposed solution offers significant value through improved efficiency, cost reduction, and enhanced competitive positioning.`
}

## 2. Problem Statement / Business Opportunity

${problemStatement}

This challenge represents a significant opportunity for our organization to improve operations, reduce costs, and enhance our competitive position in the market.

## 3. Proposed Solution

${proposedSolution}

This solution has been carefully designed to address the core issues identified while providing scalable, sustainable benefits for the organization.

## 4. Project Objectives

The following objectives have been established to ensure project success:

${objectives.map(obj => `- ${obj}`).join('\n')}

${scope.inScope || scope.outOfScope ? `## 5. Project Scope

${scope.inScope ? `### In Scope:
${scope.inScope.map(item => `- ${item}`).join('\n')}` : ''}

${scope.outOfScope ? `### Out of Scope:
${scope.outOfScope.map(item => `- ${item}`).join('\n')}` : ''}` : ''}

${benefits.quantitative || benefits.qualitative ? `## 6. Expected Benefits

${benefits.quantitative ? `### Quantitative Benefits:
${benefits.quantitative.map(benefit => `- ${benefit}`).join('\n')}` : ''}

${benefits.qualitative ? `### Qualitative Benefits:
${benefits.qualitative.map(benefit => `- ${benefit}`).join('\n')}` : ''}` : ''}

${costs.oneTime || costs.recurring ? `## 7. Cost Analysis

${costs.oneTime ? `### One-time Costs:
${costs.oneTime.map(cost => `- ${cost.item}: $${cost.amount.toLocaleString()}`).join('\n')}` : ''}

${costs.recurring ? `### Recurring Costs:
${costs.recurring.map(cost => `- ${cost.item}: $${cost.amount.toLocaleString()} (${cost.frequency})`).join('\n')}` : ''}

**Total Investment Required:** $${calculateTotalBudget(costs).toLocaleString()}` : ''}

${financialProjections.roiPercentage || financialProjections.paybackPeriodYears || financialProjections.npv ? `## 8. Financial Projections

${financialProjections.roiPercentage ? `- **Return on Investment (ROI):** ${financialProjections.roiPercentage}%` : ''}
${financialProjections.paybackPeriodYears ? `- **Payback Period:** ${financialProjections.paybackPeriodYears} years` : ''}
${financialProjections.npv ? `- **Net Present Value (NPV):** $${financialProjections.npv.toLocaleString()}` : ''}

These projections demonstrate strong financial viability and justify the proposed investment.` : ''}

${risksAndMitigation.length > 0 ? `## 9. Risk Analysis and Mitigation

${risksAndMitigation.map(risk => `### Risk: ${risk.risk}
**Mitigation Strategy:** ${risk.mitigation}`).join('\n\n')}` : ''}

${assumptions.length > 0 ? `## 10. Key Assumptions

${assumptions.map(assumption => `- ${assumption}`).join('\n')}` : ''}

${timelineMilestones.length > 0 ? `## 11. Timeline and Key Milestones

${timelineMilestones.map(milestone => `- **${milestone.milestone}:** ${milestone.targetDate}`).join('\n')}` : ''}

${stakeholders.length > 0 ? `## 12. Stakeholders

${stakeholders.map(stakeholder => `- ${stakeholder}`).join('\n')}` : ''}

${alternativesConsidered.length > 0 ? `## 13. Alternatives Considered

${alternativesConsidered.map(alt => `### ${alt.alternative}
**Reason for Rejection:** ${alt.reasonForRejection}`).join('\n\n')}` : ''}

${successMetrics.length > 0 ? `## 14. Success Metrics and KPIs

${successMetrics.map(metric => `- ${metric}`).join('\n')}` : ''}

## 15. Recommendation

Based on the comprehensive analysis presented in this business case, we strongly recommend proceeding with ${projectTitle}. The initiative addresses critical business needs, offers substantial financial returns, and positions our organization for continued success.

The combination of quantifiable benefits, manageable risks, and strategic alignment makes this project an excellent investment opportunity that will deliver significant value to our organization and stakeholders.

---

*This business case was generated on ${new Date().toLocaleDateString()} for ${targetAudience}.*
${companyContext ? `\n*Company Context: ${companyContext}*` : ''}`

  } else {
    // Plain text format
    businessCase = `BUSINESS CASE: ${projectTitle.toUpperCase()}

EXECUTIVE SUMMARY
${executiveSummaryPoints.length > 0
  ? `This business case presents ${projectTitle}, a strategic initiative designed to address critical business needs. Key highlights: ${executiveSummaryPoints.join(', ')}.`
  : `This business case presents ${projectTitle}, a strategic initiative designed to address critical business needs.`
}

PROBLEM STATEMENT
${problemStatement}

PROPOSED SOLUTION
${proposedSolution}

PROJECT OBJECTIVES
${objectives.map((obj, i) => `${i + 1}. ${obj}`).join('\n')}

${benefits.quantitative || benefits.qualitative ? `EXPECTED BENEFITS
${benefits.quantitative ? `Quantitative: ${benefits.quantitative.join(', ')}` : ''}
${benefits.qualitative ? `Qualitative: ${benefits.qualitative.join(', ')}` : ''}` : ''}

${costs.oneTime || costs.recurring ? `COST ANALYSIS
Total Investment Required: $${calculateTotalBudget(costs).toLocaleString()}` : ''}

RECOMMENDATION
We recommend proceeding with ${projectTitle} based on the analysis presented.

Generated on ${new Date().toLocaleDateString()} for ${targetAudience}.`
  }

  return businessCase
}

const calculateTotalBudget = (costs) => {
  let total = 0

  if (costs.oneTime) {
    total += costs.oneTime.reduce((sum, cost) => sum + (cost.amount || 0), 0)
  }

  if (costs.recurring) {
    // Calculate 3 years of recurring costs as estimate
    const annualRecurring = costs.recurring.reduce((sum, cost) => {
      const amount = cost.amount || 0
      const multiplier = cost.frequency === 'monthly' ? 12 :
                        cost.frequency === 'quarterly' ? 4 : 1
      return sum + (amount * multiplier)
    }, 0)
    total += annualRecurring * 3
  }

  return total
}

const calculateDuration = (milestones) => {
  if (!milestones || milestones.length === 0) return 12

  const dates = milestones
    .map(m => new Date(m.targetDate))
    .filter(d => !isNaN(d))
    .sort((a, b) => a - b)

  if (dates.length < 2) return 12

  const startDate = dates[0]
  const endDate = dates[dates.length - 1]
  const diffTime = Math.abs(endDate - startDate)
  const diffMonths = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 30))

  return diffMonths || 12
}

const getStartDate = (milestones) => {
  if (!milestones || milestones.length === 0) {
    return new Date().toISOString().split('T')[0]
  }

  const dates = milestones
    .map(m => new Date(m.targetDate))
    .filter(d => !isNaN(d))
    .sort((a, b) => a - b)

  return dates.length > 0 ? dates[0].toISOString().split('T')[0] : new Date().toISOString().split('T')[0]
}

const getEndDate = (milestones) => {
  if (!milestones || milestones.length === 0) {
    const endDate = new Date()
    endDate.setFullYear(endDate.getFullYear() + 1)
    return endDate.toISOString().split('T')[0]
  }

  const dates = milestones
    .map(m => new Date(m.targetDate))
    .filter(d => !isNaN(d))
    .sort((a, b) => a - b)

  return dates.length > 0 ? dates[dates.length - 1].toISOString().split('T')[0] : new Date().toISOString().split('T')[0]
}

// Health check
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    database: isMongoConnected ? 'Connected' : 'Disconnected',
    storage: isMongoConnected ? 'MongoDB' : 'In-Memory',
    message: isMongoConnected ? 'All systems operational' : 'Running with in-memory storage'
  })
})

// CORS test endpoint
app.get('/api/cors-test', (req, res) => {
  res.json({
    message: 'CORS is working correctly!',
    origin: req.headers.origin,
    timestamp: new Date().toISOString()
  })
})

app.post('/api/cors-test', (req, res) => {
  res.json({
    message: 'CORS POST request successful!',
    origin: req.headers.origin,
    body: req.body,
    timestamp: new Date().toISOString()
  })
})

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack)
  res.status(500).json({ error: 'Something went wrong!' })
})

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' })
})

app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`)
  console.log(`📊 API endpoints available at http://localhost:${PORT}/api`)
})

module.exports = app
