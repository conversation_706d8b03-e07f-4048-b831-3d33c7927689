{"name": "mybc-backend", "version": "1.0.0", "description": "Master Business Case - Backend API with MongoDB", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "seed": "node scripts/seedData.js"}, "keywords": ["business-case", "financial-modeling", "mongodb", "express", "api"], "author": "Master BC Framework", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "validator": "^13.11.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}