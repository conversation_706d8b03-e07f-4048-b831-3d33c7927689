import React, { useState, useEffect } from 'react'

const IntegratedDashboard = ({ projectData, activeTab, setActiveTab }) => {
  const [dashboardMetrics, setDashboardMetrics] = useState({
    totalBusinessCases: 3,
    activeProjects: 2,
    totalInvestment: 6200000,
    expectedROI: 23.4,
    completedSteps: 18,
    totalSteps: 24,
    riskScore: 5.2,
    complianceScore: 87
  })

  const [integrationStatus, setIntegrationStatus] = useState({
    framework: { status: 'active', completion: 85 },
    financial: { status: 'active', completion: 92 },
    stakeholders: { status: 'active', completion: 78 },
    compliance: { status: 'active', completion: 87 },
    excel: { status: 'ready', completion: 100 },
    roadmap: { status: 'active', completion: 65 }
  })

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const getStatusColor = (status) => {
    const colors = {
      'active': '#28a745',
      'ready': '#17a2b8',
      'pending': '#ffc107',
      'completed': '#6f42c1'
    }
    return colors[status] || '#6c757d'
  }

  const quickActions = [
    {
      id: 'new-business-case',
      title: 'Create New Business Case',
      description: 'Start the 6-step framework process',
      icon: '📋',
      action: () => setActiveTab('framework'),
      color: '#667eea'
    },
    {
      id: 'financial-analysis',
      title: 'Advanced Financial Modeling',
      description: 'Run financial analysis and scenarios',
      icon: '🧮',
      action: () => setActiveTab('modeling'),
      color: '#28a745'
    },
    {
      id: 'excel-import',
      title: 'Import Excel Data',
      description: 'Upload and analyze Excel financial data',
      icon: '🤖',
      action: () => setActiveTab('excel'),
      color: '#17a2b8'
    },
    {
      id: 'stakeholder-feedback',
      title: 'Manage Stakeholders',
      description: 'Update stakeholder feedback and comments',
      icon: '👥',
      action: () => setActiveTab('stakeholders'),
      color: '#ffc107'
    }
  ]

  const recentActivity = [
    {
      id: 1,
      type: 'business-case',
      title: 'Digital Transformation Initiative',
      action: 'Updated financial projections',
      timestamp: '2 hours ago',
      user: 'Sarah Johnson',
      status: 'active'
    },
    {
      id: 2,
      type: 'stakeholder',
      title: 'Stakeholder Feedback',
      action: 'New comment from John Smith',
      timestamp: '4 hours ago',
      user: 'John Smith',
      status: 'new'
    },
    {
      id: 3,
      type: 'compliance',
      title: 'PMI Compliance Review',
      action: 'Completed Phase 2 assessment',
      timestamp: '1 day ago',
      user: 'Mike Chen',
      status: 'completed'
    },
    {
      id: 4,
      type: 'excel',
      title: 'Excel Analysis',
      action: 'Imported Q4 financial data',
      timestamp: '2 days ago',
      user: 'Emily Watson',
      status: 'completed'
    }
  ]

  return (
    <div className="integrated-dashboard">
      <div className="dashboard-header">
        <div className="header-content">
          <h1>🎯 Master Business Case - Integrated Platform</h1>
          <p className="header-description">
            Comprehensive business case development with PMI/PGMP compliance, 
            advanced financial modeling, and stakeholder management
          </p>
        </div>
        <div className="header-stats">
          <div className="stat-item">
            <div className="stat-value">{dashboardMetrics.totalBusinessCases}</div>
            <div className="stat-label">Business Cases</div>
          </div>
          <div className="stat-item">
            <div className="stat-value">{formatCurrency(dashboardMetrics.totalInvestment)}</div>
            <div className="stat-label">Total Investment</div>
          </div>
          <div className="stat-item">
            <div className="stat-value">{dashboardMetrics.expectedROI}%</div>
            <div className="stat-label">Expected ROI</div>
          </div>
        </div>
      </div>

      <div className="dashboard-content">
        <div className="dashboard-grid">
          {/* Key Metrics */}
          <div className="dashboard-section metrics-section">
            <h3>📊 Key Metrics</h3>
            <div className="metrics-grid">
              <div className="metric-card">
                <div className="metric-icon">💼</div>
                <div className="metric-content">
                  <div className="metric-value">{dashboardMetrics.activeProjects}</div>
                  <div className="metric-label">Active Projects</div>
                </div>
              </div>
              <div className="metric-card">
                <div className="metric-icon">✅</div>
                <div className="metric-content">
                  <div className="metric-value">{dashboardMetrics.completedSteps}/{dashboardMetrics.totalSteps}</div>
                  <div className="metric-label">Completed Steps</div>
                </div>
              </div>
              <div className="metric-card">
                <div className="metric-icon">⚠️</div>
                <div className="metric-content">
                  <div className="metric-value">{dashboardMetrics.riskScore}/10</div>
                  <div className="metric-label">Risk Score</div>
                </div>
              </div>
              <div className="metric-card">
                <div className="metric-icon">🏛️</div>
                <div className="metric-content">
                  <div className="metric-value">{dashboardMetrics.complianceScore}%</div>
                  <div className="metric-label">PMI Compliance</div>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="dashboard-section actions-section">
            <h3>⚡ Quick Actions</h3>
            <div className="actions-grid">
              {quickActions.map((action) => (
                <div 
                  key={action.id} 
                  className="action-card"
                  onClick={action.action}
                  style={{ borderLeftColor: action.color }}
                >
                  <div className="action-icon" style={{ color: action.color }}>
                    {action.icon}
                  </div>
                  <div className="action-content">
                    <h4>{action.title}</h4>
                    <p>{action.description}</p>
                  </div>
                  <div className="action-arrow">→</div>
                </div>
              ))}
            </div>
          </div>

          {/* Integration Status */}
          <div className="dashboard-section integration-section">
            <h3>🔗 System Integration Status</h3>
            <div className="integration-grid">
              {Object.entries(integrationStatus).map(([key, status]) => (
                <div key={key} className="integration-item">
                  <div className="integration-header">
                    <div className="integration-name">
                      {key.charAt(0).toUpperCase() + key.slice(1)}
                    </div>
                    <div 
                      className="integration-status"
                      style={{ color: getStatusColor(status.status) }}
                    >
                      {status.status.toUpperCase()}
                    </div>
                  </div>
                  <div className="integration-progress">
                    <div className="progress-bar">
                      <div 
                        className="progress-fill"
                        style={{ 
                          width: `${status.completion}%`,
                          backgroundColor: getStatusColor(status.status)
                        }}
                      ></div>
                    </div>
                    <span className="progress-text">{status.completion}%</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Recent Activity */}
          <div className="dashboard-section activity-section">
            <h3>📈 Recent Activity</h3>
            <div className="activity-list">
              {recentActivity.map((activity) => (
                <div key={activity.id} className="activity-item">
                  <div className="activity-icon">
                    {activity.type === 'business-case' && '📋'}
                    {activity.type === 'stakeholder' && '👥'}
                    {activity.type === 'compliance' && '🏛️'}
                    {activity.type === 'excel' && '🤖'}
                  </div>
                  <div className="activity-content">
                    <div className="activity-title">{activity.title}</div>
                    <div className="activity-action">{activity.action}</div>
                    <div className="activity-meta">
                      <span className="activity-user">{activity.user}</span>
                      <span className="activity-time">{activity.timestamp}</span>
                    </div>
                  </div>
                  <div className={`activity-status ${activity.status}`}>
                    {activity.status === 'active' && '🔄'}
                    {activity.status === 'new' && '🆕'}
                    {activity.status === 'completed' && '✅'}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* System Overview */}
          <div className="dashboard-section overview-section">
            <h3>🎯 Platform Overview</h3>
            <div className="overview-content">
              <div className="overview-item">
                <h4>📋 Business Case Framework</h4>
                <p>6-step disciplined approach to business case development</p>
                <div className="overview-features">
                  <span className="feature-tag">Project Identification</span>
                  <span className="feature-tag">Alternatives Analysis</span>
                  <span className="feature-tag">Benefit Analysis</span>
                  <span className="feature-tag">Risk Assessment</span>
                </div>
              </div>
              
              <div className="overview-item">
                <h4>🧮 Advanced Financial Modeling</h4>
                <p>Real-time calculations with Excel integration</p>
                <div className="overview-features">
                  <span className="feature-tag">IRR/NPV/ROI</span>
                  <span className="feature-tag">Scenario Analysis</span>
                  <span className="feature-tag">Excel Import/Export</span>
                  <span className="feature-tag">Auto-Calculations</span>
                </div>
              </div>
              
              <div className="overview-item">
                <h4>🏛️ PMI/PGMP Compliance</h4>
                <p>Professional project management standards</p>
                <div className="overview-features">
                  <span className="feature-tag">Lifecycle Tracking</span>
                  <span className="feature-tag">Knowledge Areas</span>
                  <span className="feature-tag">Governance</span>
                  <span className="feature-tag">Compliance Scoring</span>
                </div>
              </div>
              
              <div className="overview-item">
                <h4>👥 Stakeholder Management</h4>
                <p>Comprehensive stakeholder engagement platform</p>
                <div className="overview-features">
                  <span className="feature-tag">CRUD Operations</span>
                  <span className="feature-tag">Feedback Tracking</span>
                  <span className="feature-tag">Comment System</span>
                  <span className="feature-tag">Analytics</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default IntegratedDashboard
