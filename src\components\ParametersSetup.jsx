import React, { useState } from 'react'

const ParametersSetup = ({ data, onChange }) => {
  const [parameters, setParameters] = useState({
    discountRate: 10,
    taxRate: 25,
    inflationRate: 3,
    baseCurrency: 'USD',
    exchangeRates: {
      EUR: 0.85,
      GBP: 0.73,
      JPY: 110
    },
    inflationToggles: {
      rd: true,
      production: true,
      sales: false
    },
    projectDuration: 10,
    ...data
  })

  const handleChange = (field, value) => {
    const updated = { ...parameters, [field]: value }
    setParameters(updated)
    onChange(updated)
  }

  const handleNestedChange = (parent, field, value) => {
    const updated = {
      ...parameters,
      [parent]: {
        ...parameters[parent],
        [field]: value
      }
    }
    setParameters(updated)
    onChange(updated)
  }

  return (
    <div className="parameters-setup">
      <div className="card">
        <div className="card-header">
          <h2 className="card-title">⚙️ Financial Parameters</h2>
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label className="form-label">Discount Rate (%)</label>
            <input
              type="number"
              className="form-input"
              value={parameters.discountRate}
              onChange={(e) => handleChange('discountRate', parseFloat(e.target.value))}
              step="0.1"
            />
          </div>
          
          <div className="form-group">
            <label className="form-label">Tax Rate (%)</label>
            <input
              type="number"
              className="form-input"
              value={parameters.taxRate}
              onChange={(e) => handleChange('taxRate', parseFloat(e.target.value))}
              step="0.1"
            />
          </div>
          
          <div className="form-group">
            <label className="form-label">Inflation Rate (%)</label>
            <input
              type="number"
              className="form-input"
              value={parameters.inflationRate}
              onChange={(e) => handleChange('inflationRate', parseFloat(e.target.value))}
              step="0.1"
            />
          </div>
          
          <div className="form-group">
            <label className="form-label">Project Duration (Years)</label>
            <input
              type="number"
              className="form-input"
              value={parameters.projectDuration}
              onChange={(e) => handleChange('projectDuration', parseInt(e.target.value))}
            />
          </div>
        </div>
      </div>

      <div className="card">
        <div className="card-header">
          <h2 className="card-title">💱 Currency & Exchange Rates</h2>
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label className="form-label">Base Currency</label>
            <select
              className="form-input"
              value={parameters.baseCurrency}
              onChange={(e) => handleChange('baseCurrency', e.target.value)}
            >
              <option value="USD">USD - US Dollar</option>
              <option value="EUR">EUR - Euro</option>
              <option value="GBP">GBP - British Pound</option>
              <option value="JPY">JPY - Japanese Yen</option>
            </select>
          </div>
          
          <div className="form-group">
            <label className="form-label">EUR Exchange Rate</label>
            <input
              type="number"
              className="form-input"
              value={parameters.exchangeRates.EUR}
              onChange={(e) => handleNestedChange('exchangeRates', 'EUR', parseFloat(e.target.value))}
              step="0.01"
            />
          </div>
          
          <div className="form-group">
            <label className="form-label">GBP Exchange Rate</label>
            <input
              type="number"
              className="form-input"
              value={parameters.exchangeRates.GBP}
              onChange={(e) => handleNestedChange('exchangeRates', 'GBP', parseFloat(e.target.value))}
              step="0.01"
            />
          </div>
          
          <div className="form-group">
            <label className="form-label">JPY Exchange Rate</label>
            <input
              type="number"
              className="form-input"
              value={parameters.exchangeRates.JPY}
              onChange={(e) => handleNestedChange('exchangeRates', 'JPY', parseFloat(e.target.value))}
              step="0.01"
            />
          </div>
        </div>
      </div>

      <div className="card">
        <div className="card-header">
          <h2 className="card-title">📈 Inflation Adjustments</h2>
        </div>
        
        <div className="inflation-toggles">
          <div className="toggle-group">
            <label className="toggle-label">
              <input
                type="checkbox"
                checked={parameters.inflationToggles.rd}
                onChange={(e) => handleNestedChange('inflationToggles', 'rd', e.target.checked)}
              />
              <span className="toggle-text">Apply inflation to R&D costs</span>
            </label>
          </div>
          
          <div className="toggle-group">
            <label className="toggle-label">
              <input
                type="checkbox"
                checked={parameters.inflationToggles.production}
                onChange={(e) => handleNestedChange('inflationToggles', 'production', e.target.checked)}
              />
              <span className="toggle-text">Apply inflation to Production costs</span>
            </label>
          </div>
          
          <div className="toggle-group">
            <label className="toggle-label">
              <input
                type="checkbox"
                checked={parameters.inflationToggles.sales}
                onChange={(e) => handleNestedChange('inflationToggles', 'sales', e.target.checked)}
              />
              <span className="toggle-text">Apply inflation to Sales prices</span>
            </label>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ParametersSetup
