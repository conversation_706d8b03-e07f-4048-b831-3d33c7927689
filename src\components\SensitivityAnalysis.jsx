import React, { useState } from 'react'

const SensitivityAnalysis = ({ data, onChange }) => {
  const [variables, setVariables] = useState({
    salesPrice: { base: 1000, min: 800, max: 1200, impact: 'high' },
    salesVolume: { base: 10000, min: 8000, max: 12000, impact: 'high' },
    productionCost: { base: 600, min: 500, max: 700, impact: 'medium' },
    marketingCost: { base: 100000, min: 80000, max: 120000, impact: 'low' },
    discountRate: { base: 10, min: 8, max: 12, impact: 'medium' },
    ...data
  })

  const [scenarios, setScenarios] = useState({
    best: { npv: 2500000, irr: 18.5, payback: 3.2 },
    base: { npv: 1800000, irr: 15.2, payback: 4.1 },
    worst: { npv: 950000, irr: 11.8, payback: 5.8 }
  })

  const updateVariable = (varName, field, value) => {
    const updated = {
      ...variables,
      [varName]: {
        ...variables[varName],
        [field]: parseFloat(value)
      }
    }
    setVariables(updated)
    onChange(updated)
  }

  const calculateScenarios = () => {
    // Simplified calculation for demo purposes
    // In real implementation, this would use complex financial models
    const baseNPV = 1800000
    const variation = 0.3
    
    setScenarios({
      best: { 
        npv: Math.round(baseNPV * (1 + variation)), 
        irr: 18.5, 
        payback: 3.2 
      },
      base: { 
        npv: baseNPV, 
        irr: 15.2, 
        payback: 4.1 
      },
      worst: { 
        npv: Math.round(baseNPV * (1 - variation)), 
        irr: 11.8, 
        payback: 5.8 
      }
    })
  }

  const getImpactColor = (impact) => {
    switch (impact) {
      case 'high': return '#dc3545'
      case 'medium': return '#ffc107'
      case 'low': return '#28a745'
      default: return '#6c757d'
    }
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value)
  }

  return (
    <div className="sensitivity-analysis">
      <div className="card">
        <div className="card-header">
          <h2 className="card-title">📊 Sensitivity Variables</h2>
          <button 
            className="btn btn-primary"
            onClick={calculateScenarios}
          >
            🔄 Recalculate Scenarios
          </button>
        </div>

        <div className="sensitivity-grid">
          {Object.entries(variables).map(([varName, varData]) => (
            <div key={varName} className="variable-card">
              <div className="variable-header">
                <span className="variable-name">
                  {varName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                </span>
                <span 
                  className="impact-badge"
                  style={{ 
                    backgroundColor: getImpactColor(varData.impact),
                    color: 'white',
                    padding: '0.25rem 0.5rem',
                    borderRadius: '4px',
                    fontSize: '0.8rem'
                  }}
                >
                  {varData.impact} impact
                </span>
              </div>

              <div className="form-group">
                <label className="form-label">Base Value</label>
                <input
                  type="number"
                  className="form-input"
                  value={varData.base}
                  onChange={(e) => updateVariable(varName, 'base', e.target.value)}
                />
              </div>

              <div className="range-inputs">
                <div className="form-group">
                  <label className="form-label">Minimum</label>
                  <input
                    type="number"
                    className="form-input"
                    value={varData.min}
                    onChange={(e) => updateVariable(varName, 'min', e.target.value)}
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Maximum</label>
                  <input
                    type="number"
                    className="form-input"
                    value={varData.max}
                    onChange={(e) => updateVariable(varName, 'max', e.target.value)}
                  />
                </div>
              </div>

              <div className="variable-range">
                <div style={{ 
                  background: `linear-gradient(90deg, #dc3545 0%, #ffc107 50%, #28a745 100%)`,
                  height: '8px',
                  borderRadius: '4px',
                  margin: '0.5rem 0'
                }}></div>
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between',
                  fontSize: '0.8rem',
                  color: '#666'
                }}>
                  <span>{varData.min}</span>
                  <span>{varData.base}</span>
                  <span>{varData.max}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="card">
        <div className="card-header">
          <h2 className="card-title">🎯 Scenario Analysis Results</h2>
        </div>

        <div className="scenario-results">
          <div className="scenario-card scenario-best">
            <h3>🟢 Best Case</h3>
            <div className="scenario-metrics">
              <div><strong>NPV:</strong> {formatCurrency(scenarios.best.npv)}</div>
              <div><strong>IRR:</strong> {scenarios.best.irr}%</div>
              <div><strong>Payback:</strong> {scenarios.best.payback} years</div>
            </div>
          </div>

          <div className="scenario-card scenario-base">
            <h3>🟡 Base Case</h3>
            <div className="scenario-metrics">
              <div><strong>NPV:</strong> {formatCurrency(scenarios.base.npv)}</div>
              <div><strong>IRR:</strong> {scenarios.base.irr}%</div>
              <div><strong>Payback:</strong> {scenarios.base.payback} years</div>
            </div>
          </div>

          <div className="scenario-card scenario-worst">
            <h3>🔴 Worst Case</h3>
            <div className="scenario-metrics">
              <div><strong>NPV:</strong> {formatCurrency(scenarios.worst.npv)}</div>
              <div><strong>IRR:</strong> {scenarios.worst.irr}%</div>
              <div><strong>Payback:</strong> {scenarios.worst.payback} years</div>
            </div>
          </div>
        </div>

        <div className="sensitivity-insights" style={{ marginTop: '2rem' }}>
          <h3>📋 Key Insights</h3>
          <ul style={{ paddingLeft: '1.5rem', lineHeight: '1.6' }}>
            <li>Sales price has the highest impact on project profitability</li>
            <li>Even in worst-case scenario, NPV remains positive</li>
            <li>IRR exceeds discount rate in all scenarios</li>
            <li>Payback period varies from 3.2 to 5.8 years across scenarios</li>
          </ul>
        </div>
      </div>
    </div>
  )
}

export default SensitivityAnalysis
