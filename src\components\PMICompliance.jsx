import React, { useState } from 'react'

const PMICompliance = ({ data, onChange }) => {
  const [complianceData, setComplianceData] = useState({
    projectLifecycle: {
      currentPhase: 'Planning',
      phases: [
        { name: 'Initiating', status: 'Completed', progress: 100, startDate: '2024-01-01', endDate: '2024-01-31' },
        { name: 'Planning', status: 'In Progress', progress: 65, startDate: '2024-02-01', endDate: '2024-03-31' },
        { name: 'Executing', status: 'Not Started', progress: 0, startDate: '2024-04-01', endDate: '2024-10-31' },
        { name: 'Monitoring', status: 'Not Started', progress: 0, startDate: '2024-04-01', endDate: '2024-10-31' },
        { name: 'Closing', status: 'Not Started', progress: 0, startDate: '2024-11-01', endDate: '2024-11-30' }
      ]
    },
    knowledgeAreas: [
      { area: 'Integration Management', compliance: 85, status: 'Good', artifacts: ['Project Charter', 'Project Management Plan'] },
      { area: 'Scope Management', compliance: 78, status: 'Good', artifacts: ['Scope Statement', 'WBS', 'Requirements'] },
      { area: 'Schedule Management', compliance: 72, status: 'Needs Attention', artifacts: ['Schedule Management Plan', 'Project Schedule'] },
      { area: 'Cost Management', compliance: 90, status: 'Excellent', artifacts: ['Cost Management Plan', 'Budget Baseline'] },
      { area: 'Quality Management', compliance: 68, status: 'Needs Attention', artifacts: ['Quality Management Plan', 'Quality Metrics'] },
      { area: 'Resource Management', compliance: 75, status: 'Good', artifacts: ['Resource Management Plan', 'Team Charter'] },
      { area: 'Communications Management', compliance: 82, status: 'Good', artifacts: ['Communications Plan', 'Stakeholder Register'] },
      { area: 'Risk Management', compliance: 88, status: 'Excellent', artifacts: ['Risk Management Plan', 'Risk Register'] },
      { area: 'Procurement Management', compliance: 70, status: 'Good', artifacts: ['Procurement Plan', 'Vendor Contracts'] },
      { area: 'Stakeholder Management', compliance: 80, status: 'Good', artifacts: ['Stakeholder Analysis', 'Engagement Plan'] }
    ],
    pgmpCompliance: {
      programStrategy: {
        strategicAlignment: 85,
        benefitsRealization: 78,
        stakeholderEngagement: 82
      },
      programLifecycle: {
        programDefinition: 90,
        programBenefitsDelivery: 65,
        programClosure: 0
      },
      supportingActivities: {
        programGovernance: 88,
        programStakeholderEngagement: 80,
        programCommunications: 75,
        programFinancialManagement: 92,
        programQualityManagement: 70,
        programResourceManagement: 77,
        programRiskManagement: 85,
        programProcurementManagement: 73
      }
    },
    artifacts: [
      { name: 'Program Charter', status: 'Approved', lastUpdated: '2024-01-15', owner: 'Program Manager' },
      { name: 'Program Management Plan', status: 'In Review', lastUpdated: '2024-02-10', owner: 'Program Manager' },
      { name: 'Benefits Realization Plan', status: 'Draft', lastUpdated: '2024-02-05', owner: 'Business Analyst' },
      { name: 'Stakeholder Register', status: 'Approved', lastUpdated: '2024-01-20', owner: 'Program Manager' },
      { name: 'Risk Register', status: 'Active', lastUpdated: '2024-02-12', owner: 'Risk Manager' },
      { name: 'Program Roadmap', status: 'Approved', lastUpdated: '2024-01-25', owner: 'Program Manager' }
    ],
    ...data
  })

  const [activeSection, setActiveSection] = useState('lifecycle')

  const getComplianceColor = (score) => {
    if (score >= 85) return '#28a745'
    if (score >= 70) return '#ffc107'
    return '#dc3545'
  }

  const getStatusColor = (status) => {
    const colors = {
      'Completed': '#28a745',
      'In Progress': '#17a2b8',
      'Not Started': '#6c757d',
      'Approved': '#28a745',
      'In Review': '#ffc107',
      'Draft': '#6c757d',
      'Active': '#17a2b8',
      'Excellent': '#28a745',
      'Good': '#28a745',
      'Needs Attention': '#ffc107',
      'Poor': '#dc3545'
    }
    return colors[status] || '#6c757d'
  }

  const renderLifecycle = () => (
    <div className="lifecycle-view">
      <div className="card">
        <div className="card-header">
          <h2 className="card-title">🔄 Project Lifecycle Management</h2>
          <div className="current-phase">
            Current Phase: <span className="phase-badge">{complianceData.projectLifecycle.currentPhase}</span>
          </div>
        </div>
        
        <div className="lifecycle-phases">
          {complianceData.projectLifecycle.phases.map((phase, index) => (
            <div key={index} className="phase-card">
              <div className="phase-header">
                <h4>{phase.name}</h4>
                <span 
                  className="phase-status"
                  style={{ 
                    backgroundColor: getStatusColor(phase.status),
                    color: 'white',
                    padding: '0.25rem 0.75rem',
                    borderRadius: '12px',
                    fontSize: '0.8rem'
                  }}
                >
                  {phase.status}
                </span>
              </div>
              
              <div className="phase-progress">
                <div className="progress-bar">
                  <div 
                    className="progress-fill"
                    style={{ 
                      width: `${phase.progress}%`,
                      backgroundColor: getStatusColor(phase.status)
                    }}
                  ></div>
                </div>
                <span className="progress-text">{phase.progress}%</span>
              </div>
              
              <div className="phase-dates">
                <div>Start: {phase.startDate}</div>
                <div>End: {phase.endDate}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )

  const renderKnowledgeAreas = () => (
    <div className="knowledge-areas-view">
      <div className="card">
        <div className="card-header">
          <h2 className="card-title">📚 PMI Knowledge Areas Compliance</h2>
          <div className="overall-compliance">
            Overall Compliance: <span className="compliance-score">
              {Math.round(complianceData.knowledgeAreas.reduce((sum, area) => sum + area.compliance, 0) / complianceData.knowledgeAreas.length)}%
            </span>
          </div>
        </div>
        
        <div className="knowledge-areas-grid">
          {complianceData.knowledgeAreas.map((area, index) => (
            <div key={index} className="knowledge-area-card">
              <div className="area-header">
                <h4>{area.area}</h4>
                <span 
                  className="compliance-badge"
                  style={{ 
                    backgroundColor: getComplianceColor(area.compliance),
                    color: 'white',
                    padding: '0.25rem 0.75rem',
                    borderRadius: '12px',
                    fontSize: '0.8rem'
                  }}
                >
                  {area.compliance}%
                </span>
              </div>
              
              <div className="area-status">
                Status: <span style={{ color: getStatusColor(area.status) }}>{area.status}</span>
              </div>
              
              <div className="area-artifacts">
                <h5>Key Artifacts:</h5>
                <ul>
                  {area.artifacts.map((artifact, idx) => (
                    <li key={idx}>{artifact}</li>
                  ))}
                </ul>
              </div>
              
              <div className="compliance-bar">
                <div 
                  className="compliance-fill"
                  style={{ 
                    width: `${area.compliance}%`,
                    backgroundColor: getComplianceColor(area.compliance)
                  }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )

  const renderPGMPCompliance = () => (
    <div className="pgmp-view">
      <div className="card">
        <div className="card-header">
          <h2 className="card-title">🏆 PGMP Framework Compliance</h2>
        </div>
        
        <div className="pgmp-sections">
          <div className="pgmp-section">
            <h3>🎯 Program Strategy</h3>
            <div className="pgmp-metrics">
              <div className="pgmp-metric">
                <span>Strategic Alignment</span>
                <div className="metric-bar">
                  <div 
                    className="metric-fill"
                    style={{ 
                      width: `${complianceData.pgmpCompliance.programStrategy.strategicAlignment}%`,
                      backgroundColor: getComplianceColor(complianceData.pgmpCompliance.programStrategy.strategicAlignment)
                    }}
                  ></div>
                  <span>{complianceData.pgmpCompliance.programStrategy.strategicAlignment}%</span>
                </div>
              </div>
              
              <div className="pgmp-metric">
                <span>Benefits Realization</span>
                <div className="metric-bar">
                  <div 
                    className="metric-fill"
                    style={{ 
                      width: `${complianceData.pgmpCompliance.programStrategy.benefitsRealization}%`,
                      backgroundColor: getComplianceColor(complianceData.pgmpCompliance.programStrategy.benefitsRealization)
                    }}
                  ></div>
                  <span>{complianceData.pgmpCompliance.programStrategy.benefitsRealization}%</span>
                </div>
              </div>
              
              <div className="pgmp-metric">
                <span>Stakeholder Engagement</span>
                <div className="metric-bar">
                  <div 
                    className="metric-fill"
                    style={{ 
                      width: `${complianceData.pgmpCompliance.programStrategy.stakeholderEngagement}%`,
                      backgroundColor: getComplianceColor(complianceData.pgmpCompliance.programStrategy.stakeholderEngagement)
                    }}
                  ></div>
                  <span>{complianceData.pgmpCompliance.programStrategy.stakeholderEngagement}%</span>
                </div>
              </div>
            </div>
          </div>

          <div className="pgmp-section">
            <h3>🔄 Program Lifecycle</h3>
            <div className="pgmp-metrics">
              <div className="pgmp-metric">
                <span>Program Definition</span>
                <div className="metric-bar">
                  <div 
                    className="metric-fill"
                    style={{ 
                      width: `${complianceData.pgmpCompliance.programLifecycle.programDefinition}%`,
                      backgroundColor: getComplianceColor(complianceData.pgmpCompliance.programLifecycle.programDefinition)
                    }}
                  ></div>
                  <span>{complianceData.pgmpCompliance.programLifecycle.programDefinition}%</span>
                </div>
              </div>
              
              <div className="pgmp-metric">
                <span>Benefits Delivery</span>
                <div className="metric-bar">
                  <div 
                    className="metric-fill"
                    style={{ 
                      width: `${complianceData.pgmpCompliance.programLifecycle.programBenefitsDelivery}%`,
                      backgroundColor: getComplianceColor(complianceData.pgmpCompliance.programLifecycle.programBenefitsDelivery)
                    }}
                  ></div>
                  <span>{complianceData.pgmpCompliance.programLifecycle.programBenefitsDelivery}%</span>
                </div>
              </div>
              
              <div className="pgmp-metric">
                <span>Program Closure</span>
                <div className="metric-bar">
                  <div 
                    className="metric-fill"
                    style={{ 
                      width: `${complianceData.pgmpCompliance.programLifecycle.programClosure}%`,
                      backgroundColor: getComplianceColor(complianceData.pgmpCompliance.programLifecycle.programClosure)
                    }}
                  ></div>
                  <span>{complianceData.pgmpCompliance.programLifecycle.programClosure}%</span>
                </div>
              </div>
            </div>
          </div>

          <div className="pgmp-section">
            <h3>🛠️ Supporting Activities</h3>
            <div className="supporting-grid">
              {Object.entries(complianceData.pgmpCompliance.supportingActivities).map(([activity, score]) => (
                <div key={activity} className="supporting-item">
                  <div className="supporting-name">
                    {activity.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                  </div>
                  <div className="supporting-score" style={{ color: getComplianceColor(score) }}>
                    {score}%
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  const renderArtifacts = () => (
    <div className="artifacts-view">
      <div className="card">
        <div className="card-header">
          <h2 className="card-title">📄 Program Artifacts & Documentation</h2>
          <button className="btn btn-primary">+ Add Artifact</button>
        </div>
        
        <div className="artifacts-table">
          <table className="data-table">
            <thead>
              <tr>
                <th>Artifact Name</th>
                <th>Status</th>
                <th>Last Updated</th>
                <th>Owner</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {complianceData.artifacts.map((artifact, index) => (
                <tr key={index}>
                  <td>{artifact.name}</td>
                  <td>
                    <span 
                      className="status-badge"
                      style={{ 
                        backgroundColor: getStatusColor(artifact.status),
                        color: 'white',
                        padding: '0.25rem 0.75rem',
                        borderRadius: '12px',
                        fontSize: '0.8rem'
                      }}
                    >
                      {artifact.status}
                    </span>
                  </td>
                  <td>{artifact.lastUpdated}</td>
                  <td>{artifact.owner}</td>
                  <td>
                    <button className="btn btn-sm btn-secondary">View</button>
                    <button className="btn btn-sm btn-primary">Edit</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )

  const sections = [
    { id: 'lifecycle', label: 'Project Lifecycle', icon: '🔄' },
    { id: 'knowledge', label: 'Knowledge Areas', icon: '📚' },
    { id: 'pgmp', label: 'PGMP Framework', icon: '🏆' },
    { id: 'artifacts', label: 'Artifacts', icon: '📄' }
  ]

  return (
    <div className="pmi-compliance">
      <div className="compliance-navigation">
        {sections.map((section) => (
          <button
            key={section.id}
            className={`compliance-nav-btn ${activeSection === section.id ? 'active' : ''}`}
            onClick={() => setActiveSection(section.id)}
          >
            <span className="nav-icon">{section.icon}</span>
            <span className="nav-label">{section.label}</span>
          </button>
        ))}
      </div>

      <div className="compliance-content">
        {activeSection === 'lifecycle' && renderLifecycle()}
        {activeSection === 'knowledge' && renderKnowledgeAreas()}
        {activeSection === 'pgmp' && renderPGMPCompliance()}
        {activeSection === 'artifacts' && renderArtifacts()}
      </div>
    </div>
  )
}

export default PMICompliance
