import React from 'react'

const Visualizations = ({ data, type = 'cashflow' }) => {
  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  }

  const formatPercentage = (value) => {
    return `${value.toFixed(1)}%`;
  }

  // Cash Flow Chart
  const CashFlowChart = ({ yearlyData }) => {
    if (!yearlyData || yearlyData.length === 0) return null;

    const maxValue = Math.max(...yearlyData.map(d => Math.max(d.revenue, Math.abs(d.costs), Math.abs(d.cashFlow))));
    const chartHeight = 300;
    const chartWidth = 800;
    const barWidth = chartWidth / (yearlyData.length * 3 + 1);

    return (
      <div className="chart-container">
        <h3>📊 Cash Flow Analysis</h3>
        <div style={{ position: 'relative', width: chartWidth, height: chartHeight + 50, margin: '0 auto' }}>
          <svg width={chartWidth} height={chartHeight + 50}>
            {/* Grid lines */}
            {[0, 0.25, 0.5, 0.75, 1].map(ratio => (
              <g key={ratio}>
                <line
                  x1={0}
                  y1={chartHeight * ratio}
                  x2={chartWidth}
                  y2={chartHeight * ratio}
                  stroke="#e1e5e9"
                  strokeWidth={1}
                />
                <text
                  x={-5}
                  y={chartHeight * ratio + 5}
                  fontSize="12"
                  fill="#666"
                  textAnchor="end"
                >
                  {formatCurrency(maxValue * (1 - ratio))}
                </text>
              </g>
            ))}

            {/* Bars */}
            {yearlyData.map((year, index) => {
              const x = (index * 3 + 1) * barWidth;
              const revenueHeight = (year.revenue / maxValue) * chartHeight;
              const costsHeight = (Math.abs(year.costs) / maxValue) * chartHeight;
              const cashFlowHeight = (Math.abs(year.cashFlow) / maxValue) * chartHeight;

              return (
                <g key={year.year}>
                  {/* Revenue bar */}
                  <rect
                    x={x}
                    y={chartHeight - revenueHeight}
                    width={barWidth * 0.8}
                    height={revenueHeight}
                    fill="#28a745"
                    opacity={0.8}
                  />
                  
                  {/* Costs bar */}
                  <rect
                    x={x + barWidth}
                    y={chartHeight - costsHeight}
                    width={barWidth * 0.8}
                    height={costsHeight}
                    fill="#dc3545"
                    opacity={0.8}
                  />
                  
                  {/* Cash flow bar */}
                  <rect
                    x={x + barWidth * 2}
                    y={year.cashFlow >= 0 ? chartHeight - cashFlowHeight : chartHeight}
                    width={barWidth * 0.8}
                    height={cashFlowHeight}
                    fill={year.cashFlow >= 0 ? "#667eea" : "#ffc107"}
                    opacity={0.8}
                  />
                  
                  {/* Year label */}
                  <text
                    x={x + barWidth * 1.5}
                    y={chartHeight + 20}
                    fontSize="12"
                    fill="#333"
                    textAnchor="middle"
                  >
                    Year {year.year}
                  </text>
                </g>
              );
            })}
          </svg>
          
          {/* Legend */}
          <div style={{ display: 'flex', justifyContent: 'center', gap: '20px', marginTop: '10px' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
              <div style={{ width: '15px', height: '15px', backgroundColor: '#28a745', opacity: 0.8 }}></div>
              <span style={{ fontSize: '12px' }}>Revenue</span>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
              <div style={{ width: '15px', height: '15px', backgroundColor: '#dc3545', opacity: 0.8 }}></div>
              <span style={{ fontSize: '12px' }}>Costs</span>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
              <div style={{ width: '15px', height: '15px', backgroundColor: '#667eea', opacity: 0.8 }}></div>
              <span style={{ fontSize: '12px' }}>Cash Flow</span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // NPV Sensitivity Chart
  const SensitivityChart = ({ sensitivityResults }) => {
    if (!sensitivityResults) return null;

    const scenarios = [
      { name: 'Worst Case', value: sensitivityResults.worst.npv, color: '#dc3545' },
      { name: 'Base Case', value: sensitivityResults.base.npv, color: '#6c757d' },
      { name: 'Best Case', value: sensitivityResults.best.npv, color: '#28a745' }
    ];

    const maxValue = Math.max(...scenarios.map(s => Math.abs(s.value)));
    const chartHeight = 200;
    const chartWidth = 400;

    return (
      <div className="chart-container">
        <h3>📈 NPV Sensitivity Analysis</h3>
        <div style={{ position: 'relative', width: chartWidth, height: chartHeight + 50, margin: '0 auto' }}>
          <svg width={chartWidth} height={chartHeight + 50}>
            {/* Zero line */}
            <line
              x1={0}
              y1={chartHeight / 2}
              x2={chartWidth}
              y2={chartHeight / 2}
              stroke="#333"
              strokeWidth={2}
            />
            
            {scenarios.map((scenario, index) => {
              const barHeight = Math.abs(scenario.value / maxValue) * (chartHeight / 2);
              const x = index * (chartWidth / 3) + 20;
              const y = scenario.value >= 0 ? (chartHeight / 2) - barHeight : chartHeight / 2;

              return (
                <g key={scenario.name}>
                  <rect
                    x={x}
                    y={y}
                    width={chartWidth / 3 - 40}
                    height={barHeight}
                    fill={scenario.color}
                    opacity={0.8}
                  />
                  
                  <text
                    x={x + (chartWidth / 3 - 40) / 2}
                    y={chartHeight + 20}
                    fontSize="12"
                    fill="#333"
                    textAnchor="middle"
                  >
                    {scenario.name}
                  </text>
                  
                  <text
                    x={x + (chartWidth / 3 - 40) / 2}
                    y={y - 5}
                    fontSize="11"
                    fill={scenario.color}
                    textAnchor="middle"
                    fontWeight="bold"
                  >
                    {formatCurrency(scenario.value)}
                  </text>
                </g>
              );
            })}
          </svg>
        </div>
      </div>
    );
  };

  // Cumulative Cash Flow Line Chart
  const CumulativeCashFlowChart = ({ yearlyData }) => {
    if (!yearlyData || yearlyData.length === 0) return null;

    const chartHeight = 250;
    const chartWidth = 600;
    const maxValue = Math.max(...yearlyData.map(d => Math.abs(d.cumulative)));
    const minValue = Math.min(...yearlyData.map(d => d.cumulative));
    const range = maxValue - minValue;

    const getY = (value) => {
      return chartHeight - ((value - minValue) / range) * chartHeight;
    };

    const pathData = yearlyData.map((year, index) => {
      const x = (index / (yearlyData.length - 1)) * chartWidth;
      const y = getY(year.cumulative);
      return `${index === 0 ? 'M' : 'L'} ${x} ${y}`;
    }).join(' ');

    return (
      <div className="chart-container">
        <h3>📈 Cumulative Cash Flow</h3>
        <div style={{ position: 'relative', width: chartWidth, height: chartHeight + 50, margin: '0 auto' }}>
          <svg width={chartWidth} height={chartHeight + 50}>
            {/* Grid lines */}
            {[0, 0.25, 0.5, 0.75, 1].map(ratio => (
              <g key={ratio}>
                <line
                  x1={0}
                  y1={chartHeight * ratio}
                  x2={chartWidth}
                  y2={chartHeight * ratio}
                  stroke="#e1e5e9"
                  strokeWidth={1}
                />
                <text
                  x={-5}
                  y={chartHeight * ratio + 5}
                  fontSize="12"
                  fill="#666"
                  textAnchor="end"
                >
                  {formatCurrency(maxValue - (ratio * range))}
                </text>
              </g>
            ))}

            {/* Zero line */}
            {minValue < 0 && (
              <line
                x1={0}
                y1={getY(0)}
                x2={chartWidth}
                y2={getY(0)}
                stroke="#333"
                strokeWidth={2}
                strokeDasharray="5,5"
              />
            )}

            {/* Line path */}
            <path
              d={pathData}
              stroke="#667eea"
              strokeWidth={3}
              fill="none"
            />

            {/* Data points */}
            {yearlyData.map((year, index) => {
              const x = (index / (yearlyData.length - 1)) * chartWidth;
              const y = getY(year.cumulative);

              return (
                <g key={year.year}>
                  <circle
                    cx={x}
                    cy={y}
                    r={4}
                    fill={year.cumulative >= 0 ? "#28a745" : "#dc3545"}
                  />
                  
                  <text
                    x={x}
                    y={chartHeight + 20}
                    fontSize="12"
                    fill="#333"
                    textAnchor="middle"
                  >
                    Y{year.year}
                  </text>
                </g>
              );
            })}
          </svg>
        </div>
      </div>
    );
  };

  // Metrics Comparison Radar Chart (simplified)
  const MetricsRadarChart = ({ metrics }) => {
    if (!metrics) return null;

    const chartSize = 200;
    const center = chartSize / 2;
    const radius = 80;

    const metricsData = [
      { name: 'NPV Score', value: Math.min(100, Math.max(0, (metrics.npv / 2000000) * 100)) },
      { name: 'IRR Score', value: Math.min(100, Math.max(0, (metrics.irr / 25) * 100)) },
      { name: 'Payback Score', value: Math.min(100, Math.max(0, (1 / (metrics.payback || 1)) * 100)) },
      { name: 'Yield Score', value: Math.min(100, Math.max(0, ((metrics.yieldIndex || 0) / 3) * 100)) }
    ];

    return (
      <div className="chart-container">
        <h3>🎯 Performance Radar</h3>
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          <svg width={chartSize} height={chartSize}>
            {/* Grid circles */}
            {[0.2, 0.4, 0.6, 0.8, 1].map(ratio => (
              <circle
                key={ratio}
                cx={center}
                cy={center}
                r={radius * ratio}
                fill="none"
                stroke="#e1e5e9"
                strokeWidth={1}
              />
            ))}

            {/* Axes */}
            {metricsData.map((metric, index) => {
              const angle = (index / metricsData.length) * 2 * Math.PI - Math.PI / 2;
              const x = center + Math.cos(angle) * radius;
              const y = center + Math.sin(angle) * radius;

              return (
                <g key={metric.name}>
                  <line
                    x1={center}
                    y1={center}
                    x2={x}
                    y2={y}
                    stroke="#ddd"
                    strokeWidth={1}
                  />
                  
                  <text
                    x={x + Math.cos(angle) * 20}
                    y={y + Math.sin(angle) * 20}
                    fontSize="10"
                    fill="#333"
                    textAnchor="middle"
                  >
                    {metric.name}
                  </text>
                </g>
              );
            })}

            {/* Data polygon */}
            <polygon
              points={metricsData.map((metric, index) => {
                const angle = (index / metricsData.length) * 2 * Math.PI - Math.PI / 2;
                const distance = (metric.value / 100) * radius;
                const x = center + Math.cos(angle) * distance;
                const y = center + Math.sin(angle) * distance;
                return `${x},${y}`;
              }).join(' ')}
              fill="#667eea"
              fillOpacity={0.3}
              stroke="#667eea"
              strokeWidth={2}
            />

            {/* Data points */}
            {metricsData.map((metric, index) => {
              const angle = (index / metricsData.length) * 2 * Math.PI - Math.PI / 2;
              const distance = (metric.value / 100) * radius;
              const x = center + Math.cos(angle) * distance;
              const y = center + Math.sin(angle) * distance;

              return (
                <circle
                  key={metric.name}
                  cx={x}
                  cy={y}
                  r={3}
                  fill="#667eea"
                />
              );
            })}
          </svg>
        </div>
      </div>
    );
  };

  // Render appropriate chart based on type
  switch (type) {
    case 'cashflow':
      return <CashFlowChart yearlyData={data.yearlyData} />;
    case 'sensitivity':
      return <SensitivityChart sensitivityResults={data.sensitivityResults} />;
    case 'cumulative':
      return <CumulativeCashFlowChart yearlyData={data.yearlyData} />;
    case 'radar':
      return <MetricsRadarChart metrics={data.results} />;
    default:
      return (
        <div className="visualizations-container">
          <CashFlowChart yearlyData={data.yearlyData} />
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '2rem', marginTop: '2rem' }}>
            <SensitivityChart sensitivityResults={data.sensitivityResults} />
            <MetricsRadarChart metrics={data.results} />
          </div>
          <CumulativeCashFlowChart yearlyData={data.yearlyData} />
        </div>
      );
  }
};

export default Visualizations;
