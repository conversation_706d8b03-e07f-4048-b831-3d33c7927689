import React, { useState } from 'react'

const ProgramManagement = ({ data, onChange }) => {
  const [activeSection, setActiveSection] = useState('roles')

  const handleChange = (field, value) => {
    const updatedData = { ...data, [field]: value }
    onChange(updatedData)
  }

  const handleArrayChange = (field, index, value) => {
    const updatedArray = [...(data[field] || [])]
    updatedArray[index] = value
    handleChange(field, updatedArray)
  }

  const handleObjectArrayChange = (field, index, key, value) => {
    const updatedArray = [...(data[field] || [])]
    updatedArray[index] = { ...updatedArray[index], [key]: value }
    handleChange(field, updatedArray)
  }

  const addArrayItem = (field, defaultValue = '') => {
    const updatedArray = [...(data[field] || []), defaultValue]
    handleChange(field, updatedArray)
  }

  const addObjectArrayItem = (field, defaultObject = {}) => {
    const updatedArray = [...(data[field] || []), defaultObject]
    handleChange(field, updatedArray)
  }

  const removeArrayItem = (field, index) => {
    const updatedArray = (data[field] || []).filter((_, i) => i !== index)
    handleChange(field, updatedArray)
  }

  const sections = [
    { id: 'roles', label: 'Roles & Responsibilities', icon: '👥' },
    { id: 'initiation', label: 'Initiation & Coordination', icon: '🚀' },
    { id: 'approval', label: 'Approval & Prioritization', icon: '✅' },
    { id: 'interface', label: 'Strategic Interface', icon: '🔗' }
  ]

  const renderRolesSection = () => (
    <div className="roles-section">
      <h3>👥 Roles & Responsibilities</h3>
      
      <div className="responsibility-card">
        <h4>🎯 Strategic Vision Translation</h4>
        <div className="form-group">
          <label>Translation Process</label>
          <textarea
            value={data.translationProcess || ''}
            onChange={(e) => handleChange('translationProcess', e.target.value)}
            placeholder="Describe how strategic vision is translated into actionable programs..."
            rows={3}
          />
        </div>
        
        <div className="form-group">
          <label>Key Stakeholders</label>
          {(data.translationStakeholders || []).map((stakeholder, index) => (
            <div key={index} className="array-input">
              <input
                type="text"
                value={stakeholder}
                onChange={(e) => handleArrayChange('translationStakeholders', index, e.target.value)}
                placeholder="e.g., Chief Strategy Officer, Program Directors"
              />
              <button 
                type="button" 
                onClick={() => removeArrayItem('translationStakeholders', index)}
                className="btn-remove"
              >
                ✕
              </button>
            </div>
          ))}
          <button 
            type="button" 
            onClick={() => addArrayItem('translationStakeholders')}
            className="btn btn-secondary btn-sm"
          >
            + Add Stakeholder
          </button>
        </div>
      </div>

      <div className="responsibility-card">
        <h4>🔄 Program Lifecycle Oversight</h4>
        <div className="form-group">
          <label>Lifecycle Stages</label>
          {(data.lifecycleStages || []).map((stage, index) => (
            <div key={index} className="stage-input">
              <input
                type="text"
                value={stage.name || ''}
                onChange={(e) => handleObjectArrayChange('lifecycleStages', index, 'name', e.target.value)}
                placeholder="Stage name"
              />
              <input
                type="text"
                value={stage.description || ''}
                onChange={(e) => handleObjectArrayChange('lifecycleStages', index, 'description', e.target.value)}
                placeholder="Stage description"
              />
              <button 
                type="button" 
                onClick={() => removeArrayItem('lifecycleStages', index)}
                className="btn-remove"
              >
                ✕
              </button>
            </div>
          ))}
          <button 
            type="button" 
            onClick={() => addObjectArrayItem('lifecycleStages', { name: '', description: '' })}
            className="btn btn-secondary btn-sm"
          >
            + Add Stage
          </button>
        </div>
      </div>

      <div className="responsibility-card">
        <h4>🛡️ Governance & Risk Management</h4>
        <div className="form-group">
          <label>Governance Framework</label>
          <textarea
            value={data.governanceFramework || ''}
            onChange={(e) => handleChange('governanceFramework', e.target.value)}
            placeholder="Define the governance structure and decision-making processes..."
            rows={3}
          />
        </div>
        
        <div className="form-group">
          <label>Risk Management Approach</label>
          <textarea
            value={data.riskManagement || ''}
            onChange={(e) => handleChange('riskManagement', e.target.value)}
            placeholder="Describe the risk identification, assessment, and mitigation strategies..."
            rows={3}
          />
        </div>
      </div>

      <div className="responsibility-card">
        <h4>💰 Resource Allocation</h4>
        <div className="form-group">
          <label>Resource Allocation Strategy</label>
          <textarea
            value={data.resourceAllocation || ''}
            onChange={(e) => handleChange('resourceAllocation', e.target.value)}
            placeholder="Explain how resources are allocated across programs..."
            rows={3}
          />
        </div>
      </div>
    </div>
  )

  const renderInitiationSection = () => (
    <div className="initiation-section">
      <h3>🚀 Initiation & Coordination</h3>
      
      <div className="initiation-card">
        <h4>📋 Program Initiation Criteria</h4>
        <div className="form-group">
          <label>Strategic Priority Assessment</label>
          <textarea
            value={data.priorityAssessment || ''}
            onChange={(e) => handleChange('priorityAssessment', e.target.value)}
            placeholder="Define how strategic priorities are assessed for program initiation..."
            rows={3}
          />
        </div>
        
        <div className="form-group">
          <label>Feasibility Assessment Framework</label>
          <textarea
            value={data.feasibilityFramework || ''}
            onChange={(e) => handleChange('feasibilityFramework', e.target.value)}
            placeholder="Describe the feasibility assessment process and criteria..."
            rows={3}
          />
        </div>
      </div>

      <div className="initiation-card">
        <h4>🤝 Cross-functional Coordination</h4>
        <div className="form-group">
          <label>Team Structure</label>
          {(data.teamStructure || []).map((team, index) => (
            <div key={index} className="team-input">
              <input
                type="text"
                value={team.role || ''}
                onChange={(e) => handleObjectArrayChange('teamStructure', index, 'role', e.target.value)}
                placeholder="Team role"
              />
              <input
                type="text"
                value={team.responsibilities || ''}
                onChange={(e) => handleObjectArrayChange('teamStructure', index, 'responsibilities', e.target.value)}
                placeholder="Key responsibilities"
              />
              <button 
                type="button" 
                onClick={() => removeArrayItem('teamStructure', index)}
                className="btn-remove"
              >
                ✕
              </button>
            </div>
          ))}
          <button 
            type="button" 
            onClick={() => addObjectArrayItem('teamStructure', { role: '', responsibilities: '' })}
            className="btn btn-secondary btn-sm"
          >
            + Add Team Role
          </button>
        </div>
      </div>

      <div className="initiation-card">
        <h4>🎯 Stakeholder Alignment</h4>
        <div className="form-group">
          <label>Alignment Process</label>
          <textarea
            value={data.alignmentProcess || ''}
            onChange={(e) => handleChange('alignmentProcess', e.target.value)}
            placeholder="Describe the stakeholder alignment and engagement process..."
            rows={3}
          />
        </div>
      </div>

      <div className="initiation-card">
        <h4>📊 Milestone Tracking</h4>
        <div className="form-group">
          <label>Tracking Methodology</label>
          <textarea
            value={data.trackingMethodology || ''}
            onChange={(e) => handleChange('trackingMethodology', e.target.value)}
            placeholder="Explain the milestone tracking and reporting methodology..."
            rows={3}
          />
        </div>
      </div>
    </div>
  )

  const renderApprovalSection = () => (
    <div className="approval-section">
      <h3>✅ Approval & Prioritization</h3>
      
      <div className="approval-card">
        <h4>📊 Prioritization Criteria</h4>
        <div className="criteria-grid">
          <div className="criteria-item">
            <label>Strategic Fit Weight (%)</label>
            <input
              type="number"
              value={data.strategicFitWeight || ''}
              onChange={(e) => handleChange('strategicFitWeight', e.target.value)}
              placeholder="0-100"
            />
          </div>
          <div className="criteria-item">
            <label>Cost-Benefit Weight (%)</label>
            <input
              type="number"
              value={data.costBenefitWeight || ''}
              onChange={(e) => handleChange('costBenefitWeight', e.target.value)}
              placeholder="0-100"
            />
          </div>
          <div className="criteria-item">
            <label>Risk Profile Weight (%)</label>
            <input
              type="number"
              value={data.riskProfileWeight || ''}
              onChange={(e) => handleChange('riskProfileWeight', e.target.value)}
              placeholder="0-100"
            />
          </div>
          <div className="criteria-item">
            <label>Resource Availability Weight (%)</label>
            <input
              type="number"
              value={data.resourceAvailabilityWeight || ''}
              onChange={(e) => handleChange('resourceAvailabilityWeight', e.target.value)}
              placeholder="0-100"
            />
          </div>
        </div>
      </div>

      <div className="approval-card">
        <h4>🏛️ Program Governance Board</h4>
        <div className="form-group">
          <label>Board Composition</label>
          {(data.boardComposition || []).map((member, index) => (
            <div key={index} className="board-input">
              <input
                type="text"
                value={member.role || ''}
                onChange={(e) => handleObjectArrayChange('boardComposition', index, 'role', e.target.value)}
                placeholder="Board role"
              />
              <input
                type="text"
                value={member.authority || ''}
                onChange={(e) => handleObjectArrayChange('boardComposition', index, 'authority', e.target.value)}
                placeholder="Decision authority"
              />
              <button 
                type="button" 
                onClick={() => removeArrayItem('boardComposition', index)}
                className="btn-remove"
              >
                ✕
              </button>
            </div>
          ))}
          <button 
            type="button" 
            onClick={() => addObjectArrayItem('boardComposition', { role: '', authority: '' })}
            className="btn btn-secondary btn-sm"
          >
            + Add Board Member
          </button>
        </div>
        
        <div className="form-group">
          <label>Review Process</label>
          <textarea
            value={data.reviewProcess || ''}
            onChange={(e) => handleChange('reviewProcess', e.target.value)}
            placeholder="Describe the formal review and approval process..."
            rows={3}
          />
        </div>
      </div>

      <div className="approval-card">
        <h4>📈 Business Case Development Approval</h4>
        <div className="form-group">
          <label>Approval Criteria</label>
          <textarea
            value={data.approvalCriteria || ''}
            onChange={(e) => handleChange('approvalCriteria', e.target.value)}
            placeholder="Define the criteria for approving programs for business case development..."
            rows={3}
          />
        </div>
      </div>
    </div>
  )

  const renderInterfaceSection = () => (
    <div className="interface-section">
      <h3>🔗 Strategic Interface</h3>
      
      <div className="interface-card">
        <h4>🌉 Bridge Function</h4>
        <div className="form-group">
          <label>Strategic Intent Translation</label>
          <textarea
            value={data.intentTranslation || ''}
            onChange={(e) => handleChange('intentTranslation', e.target.value)}
            placeholder="Describe how Program Management bridges strategic intent and operational execution..."
            rows={3}
          />
        </div>
      </div>

      <div className="interface-card">
        <h4>🔄 Regular Review Process</h4>
        <div className="form-group">
          <label>Review Frequency</label>
          <select
            value={data.reviewFrequency || ''}
            onChange={(e) => handleChange('reviewFrequency', e.target.value)}
          >
            <option value="">Select frequency</option>
            <option value="monthly">Monthly</option>
            <option value="quarterly">Quarterly</option>
            <option value="semi-annual">Semi-Annual</option>
            <option value="annual">Annual</option>
          </select>
        </div>
        
        <div className="form-group">
          <label>Review Scope</label>
          <textarea
            value={data.reviewScope || ''}
            onChange={(e) => handleChange('reviewScope', e.target.value)}
            placeholder="Define what aspects are reviewed to ensure strategic alignment..."
            rows={3}
          />
        </div>
      </div>

      <div className="interface-card">
        <h4>🎯 Strategic Goal Evolution</h4>
        <div className="form-group">
          <label>Adaptation Process</label>
          <textarea
            value={data.adaptationProcess || ''}
            onChange={(e) => handleChange('adaptationProcess', e.target.value)}
            placeholder="Explain how programs adapt to evolving strategic goals..."
            rows={3}
          />
        </div>
      </div>

      <div className="interface-card">
        <h4>📊 Performance Metrics</h4>
        <div className="form-group">
          <label>Strategic Alignment Metrics</label>
          {(data.alignmentMetrics || []).map((metric, index) => (
            <div key={index} className="array-input">
              <input
                type="text"
                value={metric}
                onChange={(e) => handleArrayChange('alignmentMetrics', index, e.target.value)}
                placeholder="e.g., Strategic Objective Achievement Rate, Stakeholder Satisfaction"
              />
              <button 
                type="button" 
                onClick={() => removeArrayItem('alignmentMetrics', index)}
                className="btn-remove"
              >
                ✕
              </button>
            </div>
          ))}
          <button 
            type="button" 
            onClick={() => addArrayItem('alignmentMetrics')}
            className="btn btn-secondary btn-sm"
          >
            + Add Metric
          </button>
        </div>
      </div>
    </div>
  )

  return (
    <div className="program-management">
      <div className="program-header">
        <h2>👥 Program Management Framework</h2>
        <p>Define roles, processes, and governance for translating strategic vision into actionable programs</p>
      </div>

      <div className="program-navigation">
        {sections.map(section => (
          <button
            key={section.id}
            className={`nav-btn ${activeSection === section.id ? 'active' : ''}`}
            onClick={() => setActiveSection(section.id)}
          >
            {section.icon} {section.label}
          </button>
        ))}
      </div>

      <div className="program-content">
        {activeSection === 'roles' && renderRolesSection()}
        {activeSection === 'initiation' && renderInitiationSection()}
        {activeSection === 'approval' && renderApprovalSection()}
        {activeSection === 'interface' && renderInterfaceSection()}
      </div>

      <div className="program-summary">
        <h4>📊 Program Management Summary</h4>
        <div className="summary-stats">
          <div className="stat-item">
            <span className="stat-label">Lifecycle Stages:</span>
            <span className="stat-value">{(data.lifecycleStages || []).length}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Team Roles:</span>
            <span className="stat-value">{(data.teamStructure || []).length}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Board Members:</span>
            <span className="stat-value">{(data.boardComposition || []).length}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Review Frequency:</span>
            <span className="stat-value">{data.reviewFrequency || 'Not Set'}</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProgramManagement
